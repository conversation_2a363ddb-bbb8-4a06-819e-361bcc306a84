(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{1240:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var l=t(5155),a=t(2115),i=t(6874),r=t.n(i),n=t(6681),d=t(5317),o=t(6104),c=t(3592),x=t(4752),m=t.n(x);function p(){let{user:e,loading:s}=(0,n.wC)(),[t,i]=(0,a.useState)(!1),[x,p]=(0,a.useState)(null),h=async()=>{try{if(i(!0),p(null),!(await m().fire({title:"Fix Plan Expiry Dates",html:'\n          <div class="text-left">\n            <p class="mb-3">This will:</p>\n            <ul class="list-disc list-inside space-y-1 text-sm">\n              <li>Find all users with paid plans but missing planExpiry dates</li>\n              <li>Calculate proper expiry dates based on their current active days</li>\n              <li>Set planExpiry = today + (30 - activeDays) days</li>\n              <li>This ensures users expire when they reach 30 active days</li>\n            </ul>\n            <p class="mt-3 text-red-600 font-semibold">This action cannot be undone!</p>\n          </div>\n        ',icon:"warning",showCancelButton:!0,confirmButtonText:"Fix Plan Expiry Dates",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)return void i(!1);let e=(0,d.P)((0,d.collection)(o.db,c.COLLECTIONS.users),(0,d._M)(c.FIELD_NAMES.plan,"!=","Trial")),s=await (0,d.getDocs)(e),t=s.size,l=0,a=0,r=[];for(let e of(console.log("Found ".concat(t," users with paid plans")),s.docs))try{let s=e.data(),t=e.id;if(!s[c.FIELD_NAMES.planExpiry]){l++;let e=s[c.FIELD_NAMES.activeDays]||1,i=s[c.FIELD_NAMES.plan],r=(0,c.getPlanValidityDays)(i),n=new Date,x=Math.max(0,r-e),m=new Date(n.getTime()+24*x*36e5);await (0,d.mZ)((0,d.H9)(o.db,c.COLLECTIONS.users,t),{[c.FIELD_NAMES.planExpiry]:d.Dc.fromDate(m)}),a++,console.log("Fixed user ".concat(t,": plan=").concat(i,", activeDays=").concat(e,", expiry=").concat(m.toDateString()))}}catch(s){console.error("Error fixing user ".concat(e.id,":"),s),r.push("User ".concat(e.id,": ").concat(s))}p({totalUsers:t,usersWithoutExpiry:l,usersFixed:a,errors:r}),m().fire({icon:"success",title:"Plan Expiry Fix Complete",html:'\n          <div class="text-left">\n            <p><strong>Total paid plan users:</strong> '.concat(t,"</p>\n            <p><strong>Users without expiry:</strong> ").concat(l,"</p>\n            <p><strong>Users fixed:</strong> ").concat(a,"</p>\n            ").concat(r.length>0?'<p class="text-red-600"><strong>Errors:</strong> '.concat(r.length,"</p>"):"","\n          </div>\n        ")})}catch(e){console.error("Error fixing plan expiry dates:",e),m().fire({icon:"error",title:"Error",text:"Failed to fix plan expiry dates. Please try again."})}finally{i(!1)}};return s?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,l.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Fix Plan Expiry Dates"}),(0,l.jsx)("p",{className:"text-gray-600 mt-1",children:"Fix users with missing plan expiry dates"})]}),(0,l.jsx)(r(),{href:"/admin",className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors",children:"Back to Admin"})]})}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plan Expiry Fix Tool"}),(0,l.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:[(0,l.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"⚠️ Problem Description"}),(0,l.jsxs)("p",{className:"text-yellow-700 text-sm mb-2",children:["Users with paid plans who were manually uploaded may have missing ",(0,l.jsx)("code",{children:"planExpiry"})," dates. This allows them to work indefinitely after 30 active days because the system falls back to using their join date."]}),(0,l.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"\uD83D\uDD27 Solution"}),(0,l.jsxs)("p",{className:"text-yellow-700 text-sm",children:["This tool will set proper ",(0,l.jsx)("code",{children:"planExpiry"})," dates for all users with paid plans but missing expiry dates. The expiry will be calculated as: ",(0,l.jsx)("strong",{children:"today + (30 - currentActiveDays) days"})]})]}),(0,l.jsx)("button",{onClick:h,disabled:t,className:"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg transition-colors font-semibold",children:t?(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing..."]}):"Fix Plan Expiry Dates"})]}),x&&(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Fix Results"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,l.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,l.jsx)("p",{className:"text-sm text-blue-600 font-medium",children:"Total Paid Users"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:x.totalUsers})]}),(0,l.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,l.jsx)("p",{className:"text-sm text-yellow-600 font-medium",children:"Without Expiry"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-yellow-900",children:x.usersWithoutExpiry})]}),(0,l.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,l.jsx)("p",{className:"text-sm text-green-600 font-medium",children:"Fixed"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-green-900",children:x.usersFixed})]}),(0,l.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,l.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Errors"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-red-900",children:x.errors.length})]})]}),x.errors.length>0&&(0,l.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"font-semibold text-red-800 mb-2",children:"Errors"}),(0,l.jsx)("ul",{className:"text-sm text-red-700 space-y-1",children:x.errors.map((e,s)=>(0,l.jsx)("li",{children:e},s))})]})]})]})})}},1427:(e,s,t)=>{Promise.resolve().then(t.bind(t,1240))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>s(1427)),_N_E=e.O()}]);