(()=>{var e={};e.id=4246,e.ids=[4246],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},25122:(e,t,r)=>{Promise.resolve().then(r.bind(r,92920))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\work\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\work\\page.tsx","default")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},47061:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),o=r(48088),a=r(88170),i=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["work",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\work\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\work\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/work/page",pathname:"/work",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59007:(e,t,r)=>{"use strict";r.d(t,{CA:()=>c,No:()=>d,ZB:()=>h,iD:()=>u,tx:()=>m});let s={CURRENT_BATCH:"mytube_current_batch",BATCH_PREFIX:"mytube_batch_",VIDEO_INDEX:"mytube_video_index",TOTAL_VIDEOS:"mytube_total_videos",LAST_PROCESSED:"mytube_last_processed"};function o(e){for(let t of[/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,/youtube\.com\/v\/([^&\n?#]+)/]){let r=e.match(t);if(r)return r[1]}return null}function a(e){let t=o(e);return t?`https://www.youtube.com/embed/${t}`:e}function i(e,t){return`Video ${t+1}`}function n(e){let t=function(e){try{let t=localStorage.getItem(`${s.BATCH_PREFIX}${e}`);if(!t)return null;let r=JSON.parse(t);if(Date.now()-r.lastUpdated>864e5)return localStorage.removeItem(`${s.BATCH_PREFIX}${e}`),null;return r}catch(t){return console.error(`Error loading batch ${e}:`,t),null}}(e);return t?t.videos:[]}function l(){return n(parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0"))}function c(){let e=(parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0")+1)%Math.ceil(parseInt(localStorage.getItem(s.TOTAL_VIDEOS)||"0")/100);return localStorage.setItem(s.CURRENT_BATCH,e.toString()),n(e)}function d(){let e=parseInt(localStorage.getItem(s.TOTAL_VIDEOS)||"0"),t=parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0"),r=Math.ceil(e/100),o=n(t);return{totalVideos:e,currentBatch:t,totalBatches:r,videosInCurrentBatch:o.length}}function u(){Object.keys(localStorage).forEach(e=>{(e.startsWith(s.BATCH_PREFIX)||Object.values(s).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all video storage")}async function m(){try{let e=await fetch("/Mytube.json");if(!e.ok)throw Error(`Failed to load videos: ${e.statusText}`);let t=await e.json();console.log("Raw video data loaded:",Object.keys(t).length,"entries");let r=[];return Array.isArray(t)?t.forEach((e,t)=>{Object.entries(e).forEach(([e,t])=>{let s=o(t);s&&r.push({id:`video_${r.length}_${s}`,title:i(t,r.length),url:t,embedUrl:a(t),duration:300,category:"General",batchIndex:Math.floor(r.length/100)})})}):Object.entries(t).forEach(([e,t],s)=>{let n=o(t);n&&r.push({id:`video_${r.length}_${n}`,title:i(t,r.length),url:t,embedUrl:a(t),duration:300,category:"General",batchIndex:Math.floor(r.length/100)})}),r}catch(e){throw console.error("Error loading videos from file:",e),e}}async function h(){try{if(!function(){let e=localStorage.getItem(s.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached video data..."),l();{console.log("Loading fresh video data...");let e=await m();return!function(e){let t=Math.ceil(e.length/100);for(let o=0;o<t;o++){let t=100*o,a=Math.min(t+100,e.length),i=e.slice(t,a);var r=o;try{let e={batchNumber:r,videos:i,totalVideos:i.length,lastUpdated:Date.now()};localStorage.setItem(`${s.BATCH_PREFIX}${r}`,JSON.stringify(e))}catch(e){console.error(`Error saving batch ${r}:`,e)}}localStorage.setItem(s.TOTAL_VIDEOS,e.length.toString()),localStorage.setItem(s.CURRENT_BATCH,"0"),localStorage.setItem(s.LAST_PROCESSED,Date.now().toString()),console.log(`Saved ${e.length} videos in ${t} batches`)}(e),l()}}catch(t){console.error("Error initializing video system:",t);let e=l();if(e.length>0)return console.log("Using cached videos as fallback"),e;throw t}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},90842:(e,t,r)=>{Promise.resolve().then(r.bind(r,30766))},91645:e=>{"use strict";e.exports=require("net")},92920:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(60687),o=r(43210),a=r(85814),i=r.n(a),n=r(87979),l=r(744),c=r(55986),d=r(3582),u=r(92617),m=r(51278),h=r(59007);r(87087);var x=r(98873),p=r(77567);function g(){let{user:e,loading:t}=(0,n.Nu)(),{hasBlockingNotifications:a,isChecking:g,markAllAsRead:b}=(0,l.J)(e?.uid||null),{isBlocked:f,leaveStatus:v,checkLeaveStatus:y}=(0,c.l)({userId:e?.uid||null,checkInterval:3e4,enabled:!!e}),[w,j]=(0,o.useState)(null),[N,S]=(0,o.useState)(0),[C,k]=(0,o.useState)(0),[A,E]=(0,o.useState)(50),[_,T]=(0,o.useState)(!1),[D,$]=(0,o.useState)(0),[B,P]=(0,o.useState)(!1),[I,q]=(0,o.useState)(!1),[O,M]=(0,o.useState)(!1),[R,V]=(0,o.useState)(0),[U,F]=(0,o.useState)([]),[G,Y]=(0,o.useState)([]),[L,J]=(0,o.useState)(!1),[W,H]=(0,o.useState)([]),[Q,z]=(0,o.useState)(0),[X,K]=(0,o.useState)(!0),[Z,ee]=(0,o.useState)({totalVideos:0,currentBatch:0,totalBatches:0,videosInCurrentBatch:0}),[et,er]=(0,o.useState)({videoDuration:300,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1,quickAdvantageExpiry:null}),[es,eo]=(0,o.useState)(null),[ea,ei]=(0,o.useState)(0),[en,el]=(0,o.useState)(!0),[ec,ed]=(0,o.useState)(!1),[eu,em]=(0,o.useState)(0),eh=(0,o.useRef)(null),ex=(0,o.useRef)(null),ep=async()=>{if(!_){try{let{isUserPlanExpired:t}=await Promise.resolve().then(r.bind(r,3582)),s=await t(e.uid);if(s.expired)return void p.A.fire({icon:"error",title:"Plan Expired",html:`
            <div class="text-center">
              <p class="mb-2">Your plan has expired and you cannot watch videos.</p>
              <p class="text-sm text-gray-600 mb-2">${s.reason}</p>
              <p class="text-sm text-blue-600">Please upgrade your plan to continue earning.</p>
            </div>
          `,confirmButtonText:"Go to Plans",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"})}catch(e){console.error("Error checking plan expiry:",e)}if(f)return void p.A.fire({icon:"warning",title:"Work Not Available",text:v.reason||"Work is currently blocked due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});if(A<=0||N>=50)return void p.A.fire({icon:"warning",title:"Daily Session Completed",html:`
          <div class="text-center">
            <p class="mb-2">You have completed your daily session of 50 videos!</p>
            <p class="text-sm text-gray-600">Videos completed today: ${N}/50</p>
            <p class="text-sm text-green-600 mt-2">Come back tomorrow for your next session.</p>
          </div>
        `,confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});if(T(!0),$(et.videoDuration),M(!1),ed(!1),em(0),ex.current&&w){let e=`${w.embedUrl}?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1`;ex.current.src=e}en?eh.current=setInterval(()=>{$(e=>e<=1?(M(!0),eh.current&&clearInterval(eh.current),0):e-1)},1e3):(ed(!0),em(et.videoDuration))}},eg=()=>{T(!1),$(0),M(!1),ed(!1),em(0),ex.current&&w&&(ex.current.src=w.embedUrl),eh.current&&(clearInterval(eh.current),eh.current=null)},eb=async()=>{if(I)return void console.log("⚠️ Submission already in progress, ignoring click");if(!B||R<50){console.log("⚠️ Cannot submit: canSubmit =",B,"localVideoCount =",R),p.A.fire({icon:"warning",title:"Cannot Submit",text:`You need to complete exactly 50 videos to submit. Current: ${R}/50`,confirmButtonText:"Continue Watching"});return}try{if((await (0,d.getVideoCountData)(e.uid)).todayVideos>=50){console.log("⚠️ User already submitted 50 videos today"),p.A.fire({icon:"info",title:"Already Submitted",text:"You have already submitted your daily videos. Come back tomorrow!",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}}catch(e){console.error("Error checking current video data:",e)}try{let{isUserPlanExpired:t}=await Promise.resolve().then(r.bind(r,3582)),s=await t(e.uid);if(s.expired)return void p.A.fire({icon:"error",title:"Plan Expired",html:`
            <div class="text-center">
              <p class="mb-2">Your plan has expired and you cannot submit videos.</p>
              <p class="text-sm text-gray-600 mb-2">${s.reason}</p>
              <p class="text-sm text-blue-600">Please upgrade your plan to continue earning.</p>
            </div>
          `,confirmButtonText:"Go to Plans",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"})}catch(e){console.error("Error checking plan expiry:",e)}if(f)return void p.A.fire({icon:"warning",title:"Submission Not Available",text:v.reason||"Video submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});if(!(await p.A.fire({title:"Submit Videos & Earn?",html:`
        <div class="text-left">
          <p><strong>Videos Completed:</strong> ${R}/50</p>
          <p><strong>Earning Amount:</strong> ₹${et.earningPerBatch}</p>
          <p class="text-sm text-gray-600 mt-2">This action cannot be undone. You can only submit once per day.</p>
        </div>
      `,icon:"question",showCancelButton:!0,confirmButtonColor:"#10b981",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Submit & Earn!",cancelButtonText:"Cancel",allowOutsideClick:!1,allowEscapeKey:!1})).isConfirmed)return void console.log("\uD83D\uDCDD User cancelled submission");try{if(q(!0),console.log("\uD83D\uDE80 Starting video submission for 50 videos..."),50!==R)throw Error(`Invalid video count: ${R}. Expected exactly 50 videos.`);let t=et.earningPerBatch;if(console.log(`💰 Batch earning amount: ₹${t}`),(await (0,d.getVideoCountData)(e.uid)).todayVideos>=50)throw Error("Videos already submitted today. Cannot submit again.");let s=null;try{console.log("\uD83D\uDE80 Submitting batch via optimized function..."),s=await u.x8.submitVideoBatch(e.uid,50),console.log("✅ Batch submitted via optimized function:",s)}catch(a){console.warn("⚠️ Optimized submission failed, using fallback:",a),console.log("\uD83D\uDCCA Submitting batch of 50 videos to database...");let{submitBatchVideos:o}=await Promise.resolve().then(r.bind(r,3582));s=await o(e.uid,50),console.log("✅ Batch submission result:",s),console.log("\uD83D\uDCB0 Adding earnings to wallet..."),await (0,d.updateWalletBalance)(e.uid,t),console.log("\uD83D\uDCDD Adding transaction record..."),await (0,d.addTransaction)(e.uid,{type:"video_earning",amount:t,description:"Batch completion reward - 50 videos watched"})}s&&(S(s.todayVideos||50),k(s.totalVideos||0)),E(0);let o=new Date().toDateString(),a=`video_session_${e.uid}_${o}`,i=`watch_times_${e.uid}_${o}`;localStorage.removeItem(a),localStorage.removeItem(i),V(0),F([]),P(!1),eg(),p.A.fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:`
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${t} Earned!</p>
            <p class="mb-2">50 videos completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
          </div>
        `,confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(t){console.error("Error submitting videos:",t);let e=t instanceof Error?t.message:"Unknown error occurred";p.A.fire({icon:"error",title:"Submission Failed",html:`
          <div class="text-left">
            <p><strong>Error:</strong> ${e}</p>
            <p class="text-sm text-gray-600 mt-2">Please try again or contact support if the problem persists.</p>
            <p class="text-xs text-gray-500 mt-2">If you believe this is an error, please screenshot this message.</p>
          </div>
        `,confirmButtonText:"Try Again",allowOutsideClick:!1})}finally{q(!1)}},ef=e=>{let t=Math.floor(e/60);return`${t}:${(e%60).toString().padStart(2,"0")}`},ev=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/6e4);if(t<1)return"Just now";if(t<60)return`${t} min ago`;let r=Math.floor(t/60);if(r<24)return`${r}h ago`;let s=Math.floor(r/24);return`${s}d ago`};return t||X||g?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:t?"Loading...":g?"Checking notifications...":"Loading videos..."})]})}):a&&e?(0,s.jsx)(x.A,{userId:e.uid,onAllRead:b}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)(i(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Watch Videos & Earn"}),(0,s.jsxs)("div",{className:"text-white text-right",children:[(0,s.jsxs)("p",{className:"text-sm",children:["Plan: ",et.plan]}),(0,s.jsxs)("p",{className:"text-sm",children:["₹",et.earningPerBatch,"/batch (50 videos)"]})]})]}),(0,s.jsx)("div",{className:"bg-blue-500/20 border border-blue-400/30 rounded-lg p-3 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt text-blue-400 mr-2"}),(0,s.jsx)("span",{className:"text-white/90 text-sm",children:'Refresh page or click "Change Video" for different content'})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-4 gap-2 text-center",children:[(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-blue-400",children:N}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Today's Videos"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-green-400",children:C}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Total Videos"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-purple-400",children:A}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Videos Left"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[ea,"/","Trial"===et.plan?"2":"30"]}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-play-circle mr-2"}),"Watch Video & Earn"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[et.hasQuickAdvantage&&(0,s.jsxs)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-lg px-3 py-1",children:[(0,s.jsxs)("div",{className:"flex items-center text-green-300 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-bolt mr-1"}),(0,s.jsx)("span",{className:"font-medium",children:"Quick Advantage Active"})]}),et.quickAdvantageExpiry&&(0,s.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:["Until: ",new Date(et.quickAdvantageExpiry).toLocaleDateString()]})]}),(0,s.jsxs)("button",{onClick:()=>window.location.reload(),className:"glass-button px-3 py-1 text-white text-sm",title:"Refresh to change video",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-1"}),"Change Video"]})]})]}),w&&(0,s.jsxs)("div",{className:`aspect-video mb-4 video-container ${_?"watching":""}`,children:[(0,s.jsx)("iframe",{ref:ex,src:_?`${w.embedUrl}?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1&disablekb=1`:w.embedUrl,title:w.title,className:"w-full h-full rounded-lg border-0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0}),_&&(0,s.jsx)("div",{className:"video-protection-overlay rounded-lg"}),!_&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black/30 backdrop-blur-sm rounded-lg flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-center text-white",children:(0,s.jsx)("i",{className:"fas fa-play-circle text-6xl opacity-60 text-youtube-red"})})})]}),(0,s.jsx)("div",{className:"text-center",children:_?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-white",children:ef(D)}),ec&&(0,s.jsx)("div",{className:"bg-red-500/20 border border-red-400/30 rounded-lg p-3 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,s.jsx)("i",{className:"fas fa-pause text-red-400 mr-2"}),(0,s.jsx)("span",{className:"text-red-300 text-sm font-medium",children:"Timer Paused - Please stay on this page to continue watching"})]})}),(0,s.jsx)("div",{className:"bg-white/20 rounded-full h-3 max-w-md mx-auto",children:(0,s.jsx)("div",{className:`h-3 rounded-full transition-all duration-1000 ${ec?"bg-red-500":"bg-youtube-red"}`,style:{width:`${(et.videoDuration-D)/et.videoDuration*100}%`}})}),(0,s.jsxs)("div",{className:"space-x-4",children:[(0,s.jsxs)("button",{onClick:eg,className:"btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-stop mr-2"}),"Stop Watching"]}),O&&(0,s.jsx)("button",{onClick:()=>{if(!O||I)return;if(f){eg(),p.A.fire({icon:"warning",title:"Work Suspended",text:v.reason||"Work has been suspended due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}let t=R+1,r=new Date,s=[...U,r],o=[...G,r];V(t),F(s),Y(o),E(Math.max(0,50-t));let a=new Date().toDateString(),i=`video_session_${e.uid}_${a}`,n=`watch_times_${e.uid}_${a}`,l=`daily_watch_times_${e.uid}_${a}`;(0,m.CQ)(i,t.toString(),e.uid),(0,m.CQ)(n,JSON.stringify(s.map(e=>e.toISOString())),e.uid),(0,m.CQ)(l,JSON.stringify(o.map(e=>e.toISOString())),e.uid);let c=Q+1;if(c>=W.length)try{let e=(0,h.CA)();H(e);let r=Math.floor(Math.random()*e.length);z(r),j(e[r]);let s=(0,h.No)();ee(s),p.A.fire({icon:"info",title:"New Video Batch Loaded",text:`Video ${t}/50 completed. Batch ${s.currentBatch+1}/${s.totalBatches} loaded.`,timer:2e3,showConfirmButton:!1})}catch(t){console.error("Error loading next batch:",t);let e=Math.floor(Math.random()*W.length);z(e),j(W[e])}else{if(.3>Math.random()&&W.length>3){let e=W.map((e,t)=>t).filter(e=>e!==Q);c=e[Math.floor(Math.random()*e.length)],console.log(`Randomized next video: ${c} (was going to ${Q+1})`)}z(c),j(W[c])}eg(),t<50?p.A.fire({icon:"success",title:"Video Completed!",text:`Progress: ${t}/50 videos watched. ${50-t} more to go!`,timer:2e3,showConfirmButton:!1}):p.A.fire({icon:"success",title:"\uD83C\uDF89 All Videos Completed!",text:'You have watched all 50 videos! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1})},disabled:I||!O,className:`${I?"btn-disabled cursor-not-allowed opacity-50":"btn-primary"}`,children:I?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-arrow-right mr-2"}),"Next Video"]})})]})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{onClick:ep,disabled:R>=50||_||I,className:`text-lg px-8 py-4 ${R>=50||_||I?"btn-disabled cursor-not-allowed opacity-50":et.hasQuickAdvantage?"btn-success bg-green-500 hover:bg-green-600":"btn-primary"}`,children:_?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Starting Video..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:`mr-2 ${et.hasQuickAdvantage?"fas fa-bolt":"fas fa-play"}`}),et.hasQuickAdvantage?"Quick Watch":"Start Watching"," (",ef(et.videoDuration),")"]})}),B&&R>=50&&(0,s.jsx)("button",{onClick:eb,disabled:I||50!==R||N>=50,className:`text-lg px-8 py-4 transition-all duration-200 ${I||50!==R||N>=50?"btn-disabled cursor-not-allowed opacity-50 bg-gray-500":"btn-success bg-green-500 hover:bg-green-600 active:bg-green-700 transform hover:scale-105 active:scale-95"}`,style:{pointerEvents:I?"none":"auto"},children:I?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Submitting All Videos..."]}):50!==R?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),"Need ",50-R," More Videos"]}):N>=50?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Already Submitted Today"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-trophy mr-2"}),"Submit & Earn ₹",et.earningPerBatch]})}),R>0&&R<50&&(0,s.jsx)("div",{className:"mt-4 bg-blue-500/20 border border-blue-400/30 rounded-lg p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,s.jsx)("i",{className:"fas fa-info-circle text-blue-400 mr-2"}),(0,s.jsxs)("span",{className:"text-blue-300 text-sm",children:["Progress: ",R,"/50 videos completed (",50-R," remaining)"]})]})})]})})]}),G.length>0&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-clock mr-2"}),"Today's Watch History"]}),(0,s.jsxs)("div",{className:"text-white/70 text-sm",children:["Total: ",G.length," videos watched"]})]}),(0,s.jsx)("div",{className:"max-h-64 overflow-y-auto",children:(0,s.jsx)("div",{className:"grid gap-2",children:G.map((e,t)=>(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-youtube-red/20 rounded-full p-2 mr-3",children:(0,s.jsx)("i",{className:"fas fa-play text-youtube-red text-sm"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white font-medium",children:["Video #",t+1]}),(0,s.jsx)("p",{className:"text-white/70 text-sm",children:e.toLocaleDateString("en-IN",{weekday:"short",year:"numeric",month:"short",day:"numeric"})})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"text-white font-medium",children:e.toLocaleTimeString("en-IN",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!0})}),(0,s.jsx)("p",{className:"text-white/70 text-xs",children:ev(e)})]})]},t))})}),G.length>=50&&(0,s.jsx)("div",{className:"mt-4 bg-green-500/20 border border-green-400/30 rounded-lg p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,s.jsx)("i",{className:"fas fa-trophy text-green-400 mr-2"}),(0,s.jsx)("span",{className:"text-green-300 text-sm font-medium",children:"Daily target completed! Great job! \uD83C\uDF89"})]})})]})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,6958,7567,8441,3582,7979,4887],()=>r(47061));module.exports=s})();