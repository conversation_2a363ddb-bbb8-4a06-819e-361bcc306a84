(()=>{var e={};e.id=3698,e.ids=[1391,3698,3772],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1132:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},9504:(e,t,s)=>{Promise.resolve().then(s.bind(s,1132))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12454:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(60687),r=s(43210),i=s(85814),o=s.n(i),n=s(30474),l=s(87979);s(91391),s(92617);var d=s(51278);function c(){let{user:e,loading:t,isAdmin:s}=(0,l.wC)(),[i,c]=(0,r.useState)(null),[m,h]=(0,r.useState)(!0),[u,x]=(0,r.useState)(!1);return t||m?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,a.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ${u?"translate-x-0":"-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-center h-16 bg-gray-900",children:[(0,a.jsx)(n.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:32,height:32,className:"mr-2"}),(0,a.jsx)("span",{className:"text-white text-xl font-bold",children:"MyTube Admin"})]}),(0,a.jsx)("nav",{className:"mt-8",children:(0,a.jsxs)("div",{className:"px-4 space-y-2",children:[(0,a.jsxs)(o(),{href:"/admin",className:"flex items-center px-4 py-2 text-white bg-gray-700 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-tachometer-alt mr-3"}),"Dashboard"]}),(0,a.jsxs)(o(),{href:"/admin/users",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-users mr-3"}),"Users"]}),(0,a.jsxs)(o(),{href:"/admin/simple-upload",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-file-csv mr-3"}),"Simple Upload"]}),(0,a.jsxs)(o(),{href:"/admin/transactions",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-exchange-alt mr-3"}),"Transactions"]}),(0,a.jsxs)(o(),{href:"/admin/withdrawals",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave mr-3"}),"Withdrawals"]}),(0,a.jsxs)(o(),{href:"/admin/notifications",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-bell mr-3"}),"Notifications"]}),(0,a.jsxs)(o(),{href:"/admin/leaves",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-calendar-times mr-3"}),"Leave Management"]}),(0,a.jsxs)(o(),{href:"/admin/settings",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-cog mr-3"}),"Settings"]}),(0,a.jsxs)(o(),{href:"/admin/fix-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-tools mr-3"}),"Fix Active Days"]}),(0,a.jsxs)(o(),{href:"/admin/daily-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Daily Active Days"]})]})}),(0,a.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,a.jsxs)("button",{onClick:()=>{(0,d._f)(e?.uid,"/admin/login")},className:"w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt mr-3"}),"Logout"]})})]}),(0,a.jsxs)("div",{className:"lg:ml-64",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,a.jsx)("button",{onClick:()=>x(!u),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-bars text-xl"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-gray-700",children:"Welcome, Admin"}),(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-user-shield text-gray-600"})})]})]})}),(0,a.jsxs)("main",{className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-users text-blue-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i?.totalUsers?.toLocaleString()||"0"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-video text-green-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Videos"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i?.totalVideos?.toLocaleString()||"0"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-rupee-sign text-yellow-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Earnings"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₹",i?.totalEarnings?.toLocaleString()||"0"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-clock text-red-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i?.pendingWithdrawals||"0"})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow mb-8",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Today's Activity"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:i?.todayUsers||"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"New Users"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-green-600",children:i?.todayVideos?.toLocaleString()||"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Videos Watched"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-3xl font-bold text-yellow-600",children:["₹",i?.todayEarnings?.toLocaleString()||"0"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Earnings Paid"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-red-600",children:i?.todayWithdrawals||"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Withdrawals"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(o(),{href:"/admin/users",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-users text-blue-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manage Users"}),(0,a.jsx)("p",{className:"text-gray-600",children:"View and manage user accounts"})]})]})}),(0,a.jsx)(o(),{href:"/admin/withdrawals",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-money-bill-wave text-green-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Process Withdrawals"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Review and approve withdrawals"})]})]})}),(0,a.jsx)(o(),{href:"/admin/notifications",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-bell text-yellow-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Send Notifications"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Notify users about updates"})]})]})}),(0,a.jsx)(o(),{href:"/admin/settings",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-cog text-purple-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Settings"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Configure platform settings"})]})]})}),(0,a.jsx)(o(),{href:"/admin/fix-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-tools text-red-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fix Active Days"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Fix daily counts and active days"})]})]})}),(0,a.jsx)(o(),{href:"/admin/simple-upload",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-file-csv text-green-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Simple Upload"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Update videos, wallet & active days via CSV"})]})]})}),(0,a.jsx)(o(),{href:"/admin/daily-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-indigo-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-calendar-plus text-indigo-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Daily Active Days"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage daily active days increment"})]})]})})]})]})]}),u&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>x(!1)})]})}},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22528:(e,t,s)=>{Promise.resolve().then(s.bind(s,12454))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91391:(e,t,s)=>{"use strict";s.d(t,{CF:()=>c,I0:()=>h,TK:()=>g,getAdminDashboardStats:()=>n,getAllPendingWithdrawals:()=>u,getAllWithdrawals:()=>x,hG:()=>p,lo:()=>l,nQ:()=>m,updateWithdrawalStatus:()=>f,x5:()=>d});var a=s(75535),r=s(33784),i=s(3582);let o=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=o.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let s=a.Dc.fromDate(t),n=await (0,a.getDocs)((0,a.collection)(r.db,i.COLLECTIONS.users)),l=n.size,d=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a._M)(i.FIELD_NAMES.joinedDate,">=",s)),c=(await (0,a.getDocs)(d)).size,m=0,h=0,u=0,x=0;n.forEach(e=>{let s=e.data();m+=s[i.FIELD_NAMES.totalVideos]||0,h+=s[i.FIELD_NAMES.wallet]||0;let a=s[i.FIELD_NAMES.lastVideoDate]?.toDate();a&&a.toDateString()===t.toDateString()&&(u+=s[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,a.AB)(1e3));(await (0,a.getDocs)(e)).forEach(e=>{let s=e.data(),a=s[i.FIELD_NAMES.date]?.toDate();a&&a>=t&&(x+=s[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let g=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending")),p=(await (0,a.getDocs)(g)).size,f=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("date",">=",s)),w=(await (0,a.getDocs)(f)).size,y={totalUsers:l,totalVideos:m,totalEarnings:h,pendingWithdrawals:p,todayUsers:c,todayVideos:u,todayEarnings:x,todayWithdrawals:w};return o.set(e,{data:y,timestamp:Date.now()}),y}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(e=50,t=null){try{let s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let o=await (0,a.getDocs)(s);return{users:o.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(s)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})).filter(e=>{let s=String(e[i.FIELD_NAMES.name]||"").toLowerCase(),a=String(e[i.FIELD_NAMES.email]||"").toLowerCase(),r=String(e[i.FIELD_NAMES.mobile]||"").toLowerCase(),o=String(e[i.FIELD_NAMES.referralCode]||"").toLowerCase();return s.includes(t)||a.includes(t)||r.includes(t)||o.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error getting all users:",e),e}}async function m(){try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users));return(await (0,a.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function h(e=50,t=null){try{let s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let o=await (0,a.getDocs)(s);return{transactions:o.docs.map(e=>({id:e.id,...e.data(),date:e.data()[i.FIELD_NAMES.date]?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function u(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending"),(0,a.My)("date","desc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} pending withdrawals`),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function x(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a.My)("date","desc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} total withdrawals`),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function g(e,t){try{await (0,a.mZ)((0,a.H9)(r.db,i.COLLECTIONS.users,e),t),o.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function p(e){try{await (0,a.kd)((0,a.H9)(r.db,i.COLLECTIONS.users,e)),o.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function f(e,t,n){try{let l=await (0,a.x7)((0,a.H9)(r.db,i.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:m}=l.data(),h={status:t,updatedAt:a.Dc.now()};if(n&&(h.adminNotes=n),await (0,a.mZ)((0,a.H9)(r.db,i.COLLECTIONS.withdrawals,e),h),"approved"===t&&"approved"!==m){let{addTransaction:e}=await Promise.resolve().then(s.bind(s,3582));await e(d,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===t&&"rejected"!==m){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(s.bind(s,3582));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}o.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91445:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1132)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},92617:(e,t,s)=>{"use strict";s.d(t,{x8:()=>f});var a=s(24791),r=s(33784);let i=(0,a.Qg)(r.Cn,"getUserDashboardData"),o=(0,a.Qg)(r.Cn,"submitVideoBatch"),n=(0,a.Qg)(r.Cn,"processWithdrawalRequest"),l=(0,a.Qg)(r.Cn,"getUserNotifications"),d=(0,a.Qg)(r.Cn,"getUserTransactions"),c=(0,a.Qg)(r.Cn,"getAdminWithdrawals"),m=(0,a.Qg)(r.Cn,"getAdminDashboardStats"),h=(0,a.Qg)(r.Cn,"getAdminUsers"),u=(0,a.Qg)(r.Cn,"getAdminNotifications"),x=(0,a.Qg)(r.Cn,"createAdminNotification");async function g(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",r.Cn.app.options.projectId);let t=await i({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}}async function p(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await m({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let f={getDashboardData:async function(e){try{return await g(e)}catch(l){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:a,getVideoCountData:r}=await s.e(3582).then(s.bind(s,3582)),[i,o,n]=await Promise.all([t(e),a(e),r(e)]);return{userData:i,walletData:o,videoData:n}}},submitVideoBatch:async function(e,t=50){try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let s=await o({userId:e,videoCount:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await n(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e,t=10){try{console.log("\uD83D\uDE80 Using optimized notifications function...");let s=await l({userId:e,limit:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e,t=10,s="all"){try{console.log("\uD83D\uDE80 Using optimized transactions function...");let a=await d({userId:e,limit:t,type:s});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(e=!1){try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",r.Cn.app.options.projectId);let t=await c({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}},getAdminDashboardStats:async function(){try{return await p()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([s.e(3582),s.e(1391)]).then(s.bind(s,91391));return await e()}},getAdminUsers:async function(e={}){try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await h(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(e=50,t="all"){try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let s=await u({limit:e,type:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await x(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",r.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",r.Cn.region);let e=await i({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),!1}}}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[6204,6958,7567,8441,3582,7979],()=>s(91445));module.exports=a})();