(()=>{var e={};e.id=773,e.ids=[773,1391,3772],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1223:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),l=s.n(i),o=s(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(t,n);let d={children:["",{children:["admin",{children:["simple-upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,73495)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\simple-upload\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\simple-upload\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/simple-upload/page",pathname:"/admin/simple-upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73495:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\simple-upload\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\simple-upload\\page.tsx","default")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76184:(e,t,s)=>{Promise.resolve().then(s.bind(s,93429))},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91391:(e,t,s)=>{"use strict";s.d(t,{CF:()=>c,I0:()=>p,TK:()=>x,getAdminDashboardStats:()=>o,getAllPendingWithdrawals:()=>m,getAllWithdrawals:()=>h,hG:()=>w,lo:()=>n,nQ:()=>u,updateWithdrawalStatus:()=>g,x5:()=>d});var a=s(75535),r=s(33784),i=s(3582);let l=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=l.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let s=a.Dc.fromDate(t),o=await (0,a.getDocs)((0,a.collection)(r.db,i.COLLECTIONS.users)),n=o.size,d=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a._M)(i.FIELD_NAMES.joinedDate,">=",s)),c=(await (0,a.getDocs)(d)).size,u=0,p=0,m=0,h=0;o.forEach(e=>{let s=e.data();u+=s[i.FIELD_NAMES.totalVideos]||0,p+=s[i.FIELD_NAMES.wallet]||0;let a=s[i.FIELD_NAMES.lastVideoDate]?.toDate();a&&a.toDateString()===t.toDateString()&&(m+=s[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,a.AB)(1e3));(await (0,a.getDocs)(e)).forEach(e=>{let s=e.data(),a=s[i.FIELD_NAMES.date]?.toDate();a&&a>=t&&(h+=s[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let x=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending")),w=(await (0,a.getDocs)(x)).size,g=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("date",">=",s)),v=(await (0,a.getDocs)(g)).size,f={totalUsers:n,totalVideos:u,totalEarnings:p,pendingWithdrawals:w,todayUsers:c,todayVideos:m,todayEarnings:h,todayWithdrawals:v};return l.set(e,{data:f,timestamp:Date.now()}),f}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function n(e=50,t=null){try{let s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let l=await (0,a.getDocs)(s);return{users:l.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(s)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})).filter(e=>{let s=String(e[i.FIELD_NAMES.name]||"").toLowerCase(),a=String(e[i.FIELD_NAMES.email]||"").toLowerCase(),r=String(e[i.FIELD_NAMES.mobile]||"").toLowerCase(),l=String(e[i.FIELD_NAMES.referralCode]||"").toLowerCase();return s.includes(t)||a.includes(t)||r.includes(t)||l.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users));return(await (0,a.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function p(e=50,t=null){try{let s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let l=await (0,a.getDocs)(s);return{transactions:l.docs.map(e=>({id:e.id,...e.data(),date:e.data()[i.FIELD_NAMES.date]?.toDate()})),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function m(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending"),(0,a.My)("date","desc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} pending withdrawals`),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function h(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a.My)("date","desc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} total withdrawals`),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function x(e,t){try{await (0,a.mZ)((0,a.H9)(r.db,i.COLLECTIONS.users,e),t),l.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function w(e){try{await (0,a.kd)((0,a.H9)(r.db,i.COLLECTIONS.users,e)),l.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function g(e,t,o){try{let n=await (0,a.x7)((0,a.H9)(r.db,i.COLLECTIONS.withdrawals,e));if(!n.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=n.data(),p={status:t,updatedAt:a.Dc.now()};if(o&&(p.adminNotes=o),await (0,a.mZ)((0,a.H9)(r.db,i.COLLECTIONS.withdrawals,e),p),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(s.bind(s,3582));await e(d,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(s.bind(s,3582));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}l.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},91864:(e,t,s)=>{Promise.resolve().then(s.bind(s,73495))},93429:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(60687),r=s(43210),i=s(85814),l=s.n(i),o=s(87979),n=s(91391),d=s(77567);function c(){let{user:e,loading:t,isAdmin:i}=(0,o.wC)(),[c,u]=(0,r.useState)(!1),[p,m]=(0,r.useState)(null),[h,x]=(0,r.useState)(null),[w,g]=(0,r.useState)([]),[v,f]=(0,r.useState)(!1),y=async()=>{if(h)try{u(!0);let e=(await h.text()).split("\n").filter(e=>e.trim());if(e.length<2)throw Error("CSV file must have at least a header row and one data row");let t=e[0],s=t.includes("	")?"	":",",a=t.split(s).map(e=>e.trim().replace(/"/g,"").toLowerCase());if(["email"].filter(e=>!a.some(t=>t.includes(e))).length>0)throw Error("Missing required column: email");let r=e.slice(1).map((e,t)=>{let r=e.split(s).map(e=>e.trim().replace(/"/g,"")),i={};a.forEach((e,t)=>{i[e]=r[t]||""});let l=i.email||"",o=parseInt(i.totalvideos||i.videos||i.totalVideos||"0")||0,n=parseFloat(i.walletbalance||i.wallet||i.walletBalance||"0")||0,d=parseInt(i.activedays||i.active||i.activeDays||"0")||0,c=parseInt(i.quickvideodays||i.quickdays||i.quickVideoRemainingDays||i.quickVideoDays||"0")||0;if(!l)throw Error(`Row ${t+2}: Email is required`);if(!l.includes("@"))throw Error(`Row ${t+2}: Invalid email format`);return{email:l,totalVideos:o,walletBalance:n,activeDays:d,quickVideoRemainingDays:c}});g(r.slice(0,10)),f(!0)}catch(e){console.error("Error previewing file:",e),d.A.fire({icon:"error",title:"Preview Failed",text:e.message||"Failed to preview file. Please check the format."})}finally{u(!1)}},b=async()=>{if(h&&(await d.A.fire({icon:"question",title:"Confirm Data Upload",html:`
        <div class="text-left">
          <p><strong>Are you sure you want to update user data from this file?</strong></p>
          <br>
          <p>This will:</p>
          <ul>
            <li>Find users by email address</li>
            <li>Add to their existing total videos count (if provided)</li>
            <li>Add to their existing wallet balance (if provided)</li>
            <li>SET their active days to the specified value (if provided)</li>
            <li>SET their quick video remaining days (if provided)</li>
            <li>Skip users not found in the system</li>
          </ul>
          <br>
          <p class="text-yellow-600"><strong>Note:</strong> Videos and wallet will be ADDED, but active days and quick video days will be SET (replaced)!</p>
        </div>
      `,showCancelButton:!0,confirmButtonText:"Yes, Update Users",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)try{u(!0),m(null),d.A.fire({title:"Updating Users",html:`
          <div class="text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p>Processing user updates...</p>
            <p class="text-sm text-gray-600 mt-2">Please wait...</p>
          </div>
        `,allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1});let e=(await h.text()).split("\n").filter(e=>e.trim()),t=e[0],a=t.includes("	")?"	":",",r=t.split(a).map(e=>e.trim().replace(/"/g,"").toLowerCase()),i=e.slice(1).map(e=>{let t=e.split(a).map(e=>e.trim().replace(/"/g,"")),s={};return r.forEach((e,a)=>{s[e]=t[a]||""}),{email:s.email||"",totalVideos:parseInt(s.totalvideos||s.videos||s.totalVideos||"0")||0,walletBalance:parseFloat(s.walletbalance||s.wallet||s.walletBalance||"0")||0,activeDays:parseInt(s.activedays||s.active||s.activeDays||"0")||0,quickVideoRemainingDays:parseInt(s.quickvideodays||s.quickdays||s.quickVideoRemainingDays||s.quickVideoDays||"0")||0,videoDuration:parseInt(s.videoduration||s.duration||s.videoDuration||"0")||void 0}}).filter(e=>e.email),l=0,o=0,c=0,p=[];for(let e of i)try{let t=(await (0,n.x5)(e.email)).find(t=>t.email?.toLowerCase()===e.email.toLowerCase());if(!t){c++,p.push(`User not found: ${e.email}`);continue}let{getWalletData:a,getVideoCountData:r}=await Promise.resolve().then(s.bind(s,3582)),[i,o]=await Promise.all([a(t.id),r(t.id)]),d={};if(e.totalVideos>0&&(d.totalVideos=(o.totalVideos||0)+e.totalVideos),0!==e.walletBalance&&(d.wallet=(i.wallet||0)+e.walletBalance),e.activeDays>0&&(d.activeDays=e.activeDays,d.manuallySetActiveDays=!0),e.videoDuration&&e.videoDuration>0){let t=[1,10,30].includes(e.videoDuration),s=e.videoDuration>=60&&e.videoDuration<=600;if(t||s)d.videoDuration=e.videoDuration;else throw Error(`Invalid video duration: ${e.videoDuration}. Must be 1, 10, or 30 seconds for quick duration, or between 1-10 minutes (60-600 seconds) for standard duration`)}if(void 0!==e.quickVideoRemainingDays&&e.quickVideoRemainingDays>=0)if(d.quickVideoAdvantageRemainingDays=e.quickVideoRemainingDays,e.quickVideoRemainingDays>0){d.quickVideoAdvantage=!0;let t=new Date;t.setDate(t.getDate()+e.quickVideoRemainingDays),d.quickVideoAdvantageExpiry=t}else d.quickVideoAdvantage=!1,d.quickVideoAdvantageExpiry=null;Object.keys(d).length>0&&await (0,n.TK)(t.id,d),l++}catch(t){o++,p.push(`Failed to update ${e.email}: ${t.message}`)}d.A.close();let x={success:l,failed:o,errors:p,notFound:c};m(x),d.A.fire({icon:l>0?o>0||c>0?"warning":"success":"error",title:"Update Complete",html:`
          <div class="text-left">
            <p><strong>Update Summary:</strong></p>
            <ul>
              <li class="text-green-600">✓ Successfully updated: ${l} users</li>
              <li class="text-yellow-600">⚠ Not found: ${c} users</li>
              <li class="text-red-600">✗ Failed: ${o} users</li>
            </ul>
            ${p.length>0?`<br><p><strong>First 5 errors:</strong></p><ul>${p.slice(0,5).map(e=>`<li class="text-red-600 text-sm">${e}</li>`).join("")}</ul>`:""}
          </div>
        `,timer:o>0?void 0:5e3,showConfirmButton:o>0})}catch(e){console.error("Error updating users:",e),d.A.fire({icon:"error",title:"Update Failed",text:e.message||"Failed to update users. Please try again."})}finally{u(!1)}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Simple User Update"}),(0,a.jsx)("p",{className:"text-white/80",children:"Update user videos, wallet balance, active days, and quick video advantage via CSV"})]}),(0,a.jsxs)(l(),{href:"/admin/users",className:"btn-secondary",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload CSV File"]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Sample File"}),(0,a.jsxs)("button",{onClick:()=>{let e=new Blob(["email,totalVideos,walletBalance,activeDays,quickVideoDays,videoDuration\<EMAIL>,100,500,15,7,300\<EMAIL>,250,-200,25,0,60\<EMAIL>,75,300,5,3,600"],{type:"text/csv"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="simple-upload-sample.csv",s.click(),URL.revokeObjectURL(t)},className:"btn-secondary text-sm",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample CSV"]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select CSV File"}),(0,a.jsx)("input",{type:"file",accept:".csv,.txt",onChange:e=>{let t=e.target.files?.[0];t&&(x(t),g([]),f(!1),m(null))},className:"form-input"})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("button",{onClick:y,disabled:!h||c,className:"btn-secondary",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-eye mr-2"}),"Preview Data"]})}),(0,a.jsx)("button",{onClick:b,disabled:!h||c||!v,className:"btn-primary",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-upload mr-2"}),"Update Users"]})})]})})]}),v&&w.length>0&&(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-table mr-2"}),"Data Preview (First 10 Records)"]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-white",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-white/20",children:[(0,a.jsx)("th",{className:"text-left p-2",children:"Email"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Add Videos"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Add Wallet (₹)"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Set Active Days"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Set Quick Video Days"})]})}),(0,a.jsx)("tbody",{children:w.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b border-white/10",children:[(0,a.jsx)("td",{className:"p-2",children:e.email}),(0,a.jsx)("td",{className:"p-2",children:e.totalVideos>0?`+${e.totalVideos}`:"-"}),(0,a.jsx)("td",{className:"p-2",children:e.walletBalance>0?`+₹${e.walletBalance}`:"-"}),(0,a.jsx)("td",{className:"p-2",children:e.activeDays>0?e.activeDays:"-"}),(0,a.jsx)("td",{className:"p-2",children:void 0!==e.quickVideoRemainingDays&&e.quickVideoRemainingDays>=0?e.quickVideoRemainingDays:"-"})]},t))})]})})]}),p&&(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Update Results"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-green-400 text-2xl font-bold",children:p.success}),(0,a.jsx)("div",{className:"text-green-300 text-sm",children:"Successfully Updated"})]}),(0,a.jsxs)("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-yellow-400 text-2xl font-bold",children:p.notFound}),(0,a.jsx)("div",{className:"text-yellow-300 text-sm",children:"Users Not Found"})]}),(0,a.jsxs)("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-red-400 text-2xl font-bold",children:p.failed}),(0,a.jsx)("div",{className:"text-red-300 text-sm",children:"Failed Updates"})]})]}),p.errors.length>0&&(0,a.jsxs)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-red-400 font-bold mb-2",children:"Errors:"}),(0,a.jsxs)("ul",{className:"text-red-300 text-sm space-y-1",children:[p.errors.slice(0,10).map((e,t)=>(0,a.jsxs)("li",{children:["• ",e]},t)),p.errors.length>10&&(0,a.jsxs)("li",{className:"text-red-400",children:["... and ",p.errors.length-10," more errors"]})]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Instructions"]}),(0,a.jsxs)("div",{className:"text-white/80 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-white mb-2",children:"CSV Format:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"email:"})," User's email address (required - must exist in system)"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"totalVideos:"})," Number of videos to ADD to current count (optional)"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"walletBalance:"})," Amount to ADD to current wallet balance (supports negative values to reduce balance) (optional)"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"activeDays:"})," Active days to SET (replace current value) (optional)"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"quickVideoDays:"})," Quick video remaining days to SET (0 to disable, >0 to enable) (optional)"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"videoDuration:"})," Video duration in seconds to SET (1, 10, 30 for quick OR 60-600 for 1-10 minutes) (optional)"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-white mb-2",children:"Important Notes:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"Videos and wallet values are ADDED to existing data"}),(0,a.jsx)("li",{children:"Active days and quick video days values REPLACE the current values"}),(0,a.jsx)("li",{children:"Only email is required - other fields are optional"}),(0,a.jsx)("li",{children:"Users must already exist in the system"}),(0,a.jsx)("li",{children:"Email addresses are case-insensitive"}),(0,a.jsx)("li",{children:"Use comma or tab as delimiter"}),(0,a.jsx)("li",{children:"Quick video advantage is automatically enabled when quickVideoDays > 0"}),(0,a.jsx)("li",{children:"Setting quickVideoDays to 0 will disable quick video advantage"})]})]})]})]})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[6204,6958,7567,8441,3582,7979],()=>s(1223));module.exports=a})();