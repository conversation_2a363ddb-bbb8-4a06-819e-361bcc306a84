(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},744:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var a=s(43210),r=s(3582);function i(e){let[t,s]=(0,a.useState)(!1),[i,n]=(0,a.useState)(!0);return{hasBlockingNotifications:t,isChecking:i,checkForBlockingNotifications:async()=>{try{n(!0);let t=await (0,r.iA)(e);s(t)}catch(e){console.error("Error checking for blocking notifications:",e),s(!1)}finally{n(!1)}},markAllAsRead:()=>{s(!1)}}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54139:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,80559)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\dashboard\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58876:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(60687),r=s(43210),i=s(85814),n=s.n(i),l=s(30474),o=s(87979),c=s(744),d=s(3582);s(92617);var x=s(51278);function u({userId:e,isOpen:t,onClose:s}){let[i,n]=(0,r.useState)([]),[l,o]=(0,r.useState)(!0),c=async()=>{try{o(!0);let t=await (0,d.Ss)(e,20);n(t)}catch(e){console.error("Error loading notifications:",e)}finally{o(!1)}},x=t=>{t.id&&!(0,d.mv)(t.id,e)&&((0,d.bA)(t.id,e),n([...i]))},u=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},m=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return`${e} minute${e>1?"s":""} ago`}if(t<86400){let e=Math.floor(t/3600);return`${e} hour${e>1?"s":""} ago`}{let e=Math.floor(t/86400);return`${e} day${e>1?"s":""} ago`}};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-16 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-900",children:[(0,a.jsx)("i",{className:"fas fa-bell mr-2"}),"Notifications"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:c,disabled:l,className:"text-gray-500 hover:text-gray-700 transition-colors p-1",title:"Refresh notifications",children:(0,a.jsx)("i",{className:`fas fa-sync-alt ${l?"animate-spin":""}`})}),(0,a.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[60vh]",children:l?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"spinner w-8 h-8"})}):0===i.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-4xl mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No notifications yet"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"You'll see important updates here"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:i.map(t=>{let s=!!t.id&&(0,d.mv)(t.id,e);return(0,a.jsx)("div",{onClick:()=>x(t),className:`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${!s?"bg-blue-50 border-l-4 border-l-blue-500":""}`,children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,a.jsx)("i",{className:u(t.type)})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:`text-sm font-medium ${!s?"text-gray-900":"text-gray-700"}`,children:t.title}),!s&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,a.jsx)("p",{className:`text-sm mt-1 ${!s?"text-gray-800":"text-gray-600"}`,children:t.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:m(t.createdAt)})]})]})},t.id)})})}),i.length>0&&(0,a.jsx)("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:(0,a.jsx)("button",{onClick:()=>{i.forEach(t=>{t.id&&!(0,d.mv)(t.id,e)&&(0,d.bA)(t.id,e)}),n([...i])},className:"w-full text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Mark all as read"})})]})}):null}function m({userId:e,onClick:t}){let[s,i]=(0,r.useState)([]),[n,l]=(0,r.useState)(0),[o,c]=(0,r.useState)(!1);return(0,a.jsxs)("button",{onClick:t,className:"relative p-2 text-white hover:text-yellow-300 transition-colors",title:`${n} unread notifications`,children:[(0,a.jsx)("i",{className:`fas fa-bell text-xl notification-bell ${o?"animate-pulse":""}`}),n>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse",children:n>9?"9+":n}),o&&(0,a.jsx)("span",{className:"absolute -bottom-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-3 w-3 flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-sync-alt text-xs animate-spin"})})]})}var h=s(98873),p=s(77567);function f({userId:e,currentMonth:t,usedLeaves:i,maxLeaves:n,onLeaveCountChange:l}){let[o,c]=(0,r.useState)([]),[d,x]=(0,r.useState)(!1),[u,m]=(0,r.useState)(!1),[h,f]=(0,r.useState)(""),[g,b]=(0,r.useState)({date:"",reason:""}),v=async()=>{try{let{getUserLeaves:t}=await s.e(7087).then(s.bind(s,87087)),a=await t(e);c(a)}catch(e){console.error("Error loading user leaves:",e),c([])}},j=async()=>{try{if(!g.date||!g.reason.trim())return void p.A.fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let t=new Date(g.date),a=new Date;a.setHours(0,0,0,0);let r=new Date(a);if(r.setDate(r.getDate()+1),t<=a)return void p.A.fire({icon:"error",title:"Invalid Date",text:"Cannot apply leave for today or past dates. Please select a future date."});try{let{isUserPlanExpired:r}=await Promise.resolve().then(s.bind(s,3582));if((await r(e)).expired)return void p.A.fire({icon:"error",title:"Plan Expired",text:"Your plan has expired. Cannot apply for leave."});let{getUserData:i,getPlanValidityDays:n,calculateUserActiveDays:l}=await Promise.resolve().then(s.bind(s,3582)),o=await i(e);if(o){let s;if("Trial"===o.plan){let e=o.joinedDate||new Date;s=new Date(e.getTime()+1728e5)}else if(o.planExpiry)s=o.planExpiry;else{let t=n(o.plan),r=await l(e),i=Math.max(0,t-r);s=new Date(a.getTime()+24*i*36e5)}if(t>s)return void p.A.fire({icon:"error",title:"Date Outside Plan Period",text:`Cannot apply leave beyond your plan expiry date (${s.toLocaleDateString()}).`})}}catch(e){console.error("Error checking plan expiry:",e)}if(i>=n)return void p.A.fire({icon:"error",title:"Leave Limit Exceeded",text:`You have already used all ${n} leaves for this month.`});if(o.find(e=>e.date.toDateString()===t.toDateString()))return void p.A.fire({icon:"error",title:"Duplicate Application",text:"You have already applied for leave on this date."});m(!0);let{applyUserLeave:c}=await s.e(7087).then(s.bind(s,87087)),d=await c({userId:e,date:t,reason:g.reason.trim()});await v(),l&&l(),d.autoApproved?p.A.fire({icon:"success",title:"✅ Leave Auto-Approved!",html:`
            <div class="text-left">
              <p><strong>Your leave has been automatically approved!</strong></p>
              <br>
              <p><strong>Date:</strong> ${t.toLocaleDateString()}</p>
              <p><strong>Reason:</strong> ${g.reason.trim()}</p>
              <br>
              <p class="text-green-600"><strong>Leave Quota:</strong> ${d.usedLeaves}/${d.maxLeaves} used this month</p>
              <p class="text-blue-600"><strong>Status:</strong> Approved automatically</p>
            </div>
          `,timer:6e3,showConfirmButton:!0,confirmButtonText:"Great!"}):p.A.fire({icon:"warning",title:"⏳ Leave Pending Approval",html:`
            <div class="text-left">
              <p><strong>Your leave application has been submitted.</strong></p>
              <br>
              <p><strong>Date:</strong> ${t.toLocaleDateString()}</p>
              <p><strong>Reason:</strong> ${g.reason.trim()}</p>
              <br>
              <p class="text-orange-600"><strong>Status:</strong> Pending admin approval (quota exceeded)</p>
              <p class="text-gray-600"><strong>Leave Quota:</strong> ${d.maxLeaves}/${d.maxLeaves} used this month</p>
            </div>
          `,timer:6e3,showConfirmButton:!0,confirmButtonText:"Understood"}),b({date:"",reason:""}),x(!1)}catch(e){console.error("Error applying leave:",e),p.A.fire({icon:"error",title:"Application Failed",text:"Failed to apply for leave. Please try again."})}finally{m(!1)}},w=async e=>{try{let t=o.find(t=>t.id===e);if(!t||"pending"!==t.status)return void p.A.fire({icon:"error",title:"Cannot Cancel",text:"Only pending leave applications can be cancelled."});if((await p.A.fire({title:"Cancel Leave Application",text:"Are you sure you want to cancel this leave application?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Cancel",cancelButtonText:"Keep Application"})).isConfirmed){let{cancelUserLeave:t}=await s.e(7087).then(s.bind(s,87087));await t(e),await v(),l&&l(),p.A.fire({icon:"success",title:"Application Cancelled",text:"Your leave application has been cancelled.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error cancelling leave:",e),p.A.fire({icon:"error",title:"Cancellation Failed",text:"Failed to cancel leave application. Please try again."})}},y=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},N=(e,t)=>{switch(e){case"approved":return"system"===t?"fas fa-magic text-green-500":"fas fa-check-circle text-green-500";case"rejected":return"fas fa-times-circle text-red-500";case"pending":return"fas fa-clock text-yellow-500";default:return"fas fa-question-circle text-gray-500"}},A=n-i;return(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-calendar-times mr-2"}),"Leave Management"]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm text-white/80",children:[t," Leaves"]}),(0,a.jsxs)("div",{className:"text-lg font-bold text-white",children:[A,"/",n," Available"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-green-500/20 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-green-400",children:n}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Monthly Quota"})]}),(0,a.jsxs)("div",{className:"bg-yellow-500/20 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-yellow-400",children:i}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Used"})]}),(0,a.jsxs)("div",{className:"bg-blue-500/20 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-blue-400",children:A}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Remaining"})]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("button",{onClick:()=>x(!0),disabled:A<=0,className:"w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),A>0?"Apply for Leave":"No Leaves Available"]})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white/80",children:"Recent Applications"}),0===o.length?(0,a.jsxs)("div",{className:"text-center py-4 text-white/60",children:[(0,a.jsx)("i",{className:"fas fa-calendar-check text-2xl mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"No leave applications yet"})]}):(0,a.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:o.slice(-5).reverse().map(e=>(0,a.jsx)("div",{className:"bg-white/10 p-3 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-white font-medium",children:e.date.toLocaleDateString()}),(0,a.jsxs)("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${y(e.status)}`,children:[(0,a.jsx)("i",{className:`${N(e.status,e.reviewedBy)} mr-1`}),e.status?e.status.charAt(0).toUpperCase()+e.status.slice(1):"Unknown","system"===e.reviewedBy&&"approved"===e.status&&(0,a.jsx)("span",{className:"ml-1",title:"Auto-approved",children:"⚡"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-white/70 mt-1",children:[e.reason,"system"===e.reviewedBy&&"approved"===e.status&&(0,a.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:[(0,a.jsx)("i",{className:"fas fa-magic mr-1"}),"Auto-approved (within quota)"]}),e.reviewNotes&&"system"!==e.reviewedBy&&(0,a.jsxs)("div",{className:"text-xs text-blue-400 mt-1",children:[(0,a.jsx)("i",{className:"fas fa-comment mr-1"}),e.reviewNotes]})]})]}),"pending"===e.status&&(0,a.jsx)("button",{onClick:()=>w(e.id),className:"text-red-400 hover:text-red-300 text-sm",children:(0,a.jsx)("i",{className:"fas fa-times"})})]})},e.id))})]}),d&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",style:{zIndex:99999},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Apply for Leave"}),(0,a.jsx)("button",{onClick:()=>x(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]}),h&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),(0,a.jsxs)("span",{className:"text-sm",children:["Leave applications are allowed until ",new Date(h).toLocaleDateString()]})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,a.jsx)("input",{type:"date",value:g.date,onChange:e=>b(t=>({...t,date:e.target.value})),min:(()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]})(),max:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Leave can only be applied for future dates within your plan period"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,a.jsx)("textarea",{value:g.reason,onChange:e=>b(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"You have ",A," leave(s) remaining for ",t,"."]}),A>0&&(0,a.jsxs)("div",{className:"text-sm text-green-700 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("strong",{children:"Auto-Approval:"})," Your leave will be automatically approved since you have available quota."]}),A<=0&&(0,a.jsxs)("div",{className:"text-sm text-orange-700 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),(0,a.jsx)("strong",{children:"Manual Review:"})," Leave will require admin approval as quota is exceeded."]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,a.jsx)("button",{onClick:()=>x(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,a.jsx)("button",{onClick:j,disabled:u,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Applying..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Apply Leave"]})})]})]})})]})}function g(){let{user:e,loading:t}=(0,o.Nu)(),{hasBlockingNotifications:i,isChecking:d,markAllAsRead:p}=(0,c.J)(e?.uid||null),[g,b]=(0,r.useState)(null),[v,j]=(0,r.useState)(null),[w,y]=(0,r.useState)(null),[N,A]=(0,r.useState)(!0),[C,k]=(0,r.useState)(!1),[D,S]=(0,r.useState)(0),[E,q]=(0,r.useState)(0),P=async()=>{try{let{getUserMonthlyLeaveCount:t}=await s.e(7087).then(s.bind(s,87087)),a=new Date,r=a.getFullYear(),i=a.getMonth()+1,n=await t(e.uid,r,i);return S(n),console.log(`User ${e.uid} has used ${n} leaves this month`),n}catch(e){return console.error("Error loading user leave count:",e),S(0),0}};return t||N||d?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:t?"Loading...":d?"Checking notifications...":"Loading dashboard..."})]})}):i&&e?(0,a.jsx)(h.A,{userId:e.uid,onAllRead:p}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:40,height:40,className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"MyTube Dashboard"}),(0,a.jsxs)("p",{className:"text-white/80",children:["Welcome back, ",g?.name||"User"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,a.jsx)(m,{userId:e.uid,onClick:()=>k(!0)}),(0,a.jsxs)("button",{onClick:()=>{(0,x._f)(e?.uid,"/login")},className:"glass-button px-4 py-2 text-white hover:bg-red-500/20 transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt mr-2"}),"Logout"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6",children:[(0,a.jsxs)(n(),{href:"/work",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-play-circle text-3xl text-youtube-red mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Watch Videos"})]}),(0,a.jsxs)(n(),{href:"/wallet",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-wallet text-3xl text-green-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Wallet"})]}),(0,a.jsxs)(n(),{href:"/transactions",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-history text-3xl text-orange-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Transactions"})]}),(0,a.jsxs)(n(),{href:"/refer",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-users text-3xl text-blue-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Refer & Earn"})]}),(0,a.jsxs)(n(),{href:"/profile",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-user text-3xl text-purple-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Profile"})]}),(0,a.jsxs)(n(),{href:"/plans",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-crown text-3xl text-yellow-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Plans"})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-wallet mr-2"}),"Wallet Overview"]}),(0,a.jsxs)("div",{className:"bg-green-500/20 p-6 rounded-lg text-center",children:[(0,a.jsx)("h3",{className:"text-green-400 font-semibold mb-2",children:"My Wallet"}),(0,a.jsxs)("p",{className:"text-4xl font-bold text-white mb-2",children:["₹",(v?.wallet||0).toFixed(2)]}),(0,a.jsx)("p",{className:"text-white/60",children:"Total available balance"}),g?.plan==="Trial"&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,a.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,a.jsx)("span",{className:"text-red-400 font-medium text-sm",children:"Withdrawal Restricted"})]}),(0,a.jsx)("p",{className:"text-white/80 text-xs mb-3",children:"Trial users cannot withdraw funds. Upgrade to enable withdrawals."}),(0,a.jsxs)(n(),{href:"/plans",className:"btn-secondary text-xs px-3 py-1",children:[(0,a.jsx)("i",{className:"fas fa-arrow-up mr-1"}),"Upgrade Plan"]})]}),(0,a.jsxs)(n(),{href:"/wallet",className:"btn-primary mt-4 inline-block",children:[(0,a.jsx)("i",{className:"fas fa-eye mr-2"}),"View Details"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-video mr-2"}),"Today's Progress"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-youtube-red",children:w?.todayVideos||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Videos Watched"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-yellow-400",children:w?.remainingVideos||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Remaining"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-green-400",children:w?.totalVideos||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Total Videos"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-400",children:E}),(0,a.jsx)("p",{className:"text-white/80",children:"Active Days"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("div",{className:"bg-white/20 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-youtube-red h-3 rounded-full transition-all duration-300",style:{width:`${w?w.todayVideos/50*100:0}%`}})}),(0,a.jsxs)("p",{className:"text-white/80 text-sm mt-2 text-center",children:[w?.todayVideos||0," / 50 videos completed today"]})]})]}),e&&(0,a.jsx)(f,{userId:e.uid,currentMonth:new Date().toLocaleDateString("en-US",{month:"long",year:"numeric"}),usedLeaves:D,maxLeaves:4,onLeaveCountChange:P}),e&&(0,a.jsx)(u,{userId:e.uid,isOpen:C,onClose:()=>k(!1)})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72385:(e,t,s)=>{Promise.resolve().then(s.bind(s,80559))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\dashboard\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},85537:(e,t,s)=>{Promise.resolve().then(s.bind(s,58876))},91645:e=>{"use strict";e.exports=require("net")},92617:(e,t,s)=>{"use strict";s.d(t,{x8:()=>g});var a=s(24791),r=s(33784);let i=(0,a.Qg)(r.Cn,"getUserDashboardData"),n=(0,a.Qg)(r.Cn,"submitVideoBatch"),l=(0,a.Qg)(r.Cn,"processWithdrawalRequest"),o=(0,a.Qg)(r.Cn,"getUserNotifications"),c=(0,a.Qg)(r.Cn,"getUserTransactions"),d=(0,a.Qg)(r.Cn,"getAdminWithdrawals"),x=(0,a.Qg)(r.Cn,"getAdminDashboardStats"),u=(0,a.Qg)(r.Cn,"getAdminUsers"),m=(0,a.Qg)(r.Cn,"getAdminNotifications"),h=(0,a.Qg)(r.Cn,"createAdminNotification");async function p(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",r.Cn.app.options.projectId);let t=await i({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}}async function f(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await x({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let g={getDashboardData:async function(e){try{return await p(e)}catch(o){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:a,getVideoCountData:r}=await s.e(3582).then(s.bind(s,3582)),[i,n,l]=await Promise.all([t(e),a(e),r(e)]);return{userData:i,walletData:n,videoData:l}}},submitVideoBatch:async function(e,t=50){try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let s=await n({userId:e,videoCount:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await l(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e,t=10){try{console.log("\uD83D\uDE80 Using optimized notifications function...");let s=await o({userId:e,limit:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e,t=10,s="all"){try{console.log("\uD83D\uDE80 Using optimized transactions function...");let a=await c({userId:e,limit:t,type:s});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(e=!1){try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",r.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}},getAdminDashboardStats:async function(){try{return await f()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([s.e(3582),s.e(1391)]).then(s.bind(s,91391));return await e()}},getAdminUsers:async function(e={}){try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await u(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(e=50,t="all"){try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let s=await m({limit:e,type:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await h(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",r.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",r.Cn.region);let e=await i({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),!1}}}},94735:e=>{"use strict";e.exports=require("events")},98873:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(60687),r=s(43210),i=s(3582);function n({userId:e,onAllRead:t}){let[s,n]=(0,r.useState)([]),[l,o]=(0,r.useState)(0),[c,d]=(0,r.useState)(!0),x=async()=>{let a=s[l];a?.id&&(await (0,i.bA)(a.id,e),l<s.length-1?o(l+1):t())};if(c)return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===s.length)return null;let u=s[l];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(u.type)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,a.jsxs)("p",{className:"text-blue-100 text-sm",children:[l+1," of ",s.length," notifications"]})]})]}),(0,a.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:u.title}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,a.jsx)("p",{className:"text-gray-800 leading-relaxed",children:u.message})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,a.jsxs)("span",{children:["From: ",u.createdBy]}),(0,a.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?`${Math.floor(t/60)} minutes ago`:t<86400?`${Math.floor(t/3600)} hours ago`:`${Math.floor(t/86400)} days ago`})(u.createdAt)})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[l+1,"/",s.length]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${(l+1)/s.length*100}%`}})})]}),(0,a.jsxs)("button",{onClick:x,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,a.jsx)("i",{className:"fas fa-check"}),(0,a.jsx)("span",{children:l<s.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)("i",{className:"fas fa-info-circle"}),(0,a.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[6204,6958,7567,8441,3582,7979],()=>s(54139));module.exports=a})();