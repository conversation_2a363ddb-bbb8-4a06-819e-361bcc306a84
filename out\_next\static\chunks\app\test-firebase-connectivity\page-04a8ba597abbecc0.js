(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3241],{1247:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var i=a(5155),s=a(2115),n=a(6104),o=a(3004),c=a(5317);function r(){let[e,t]=(0,s.useState)(""),[a,r]=(0,s.useState)(!1),l=e=>{t(t=>t+e+"\n")},d=async()=>{t(""),r(!0);try{l("\uD83C\uDF10 Testing Firebase Connectivity...\n"),l("=== TEST 1: Firebase Instances ==="),l("Auth instance: ".concat(n.j2?"✅ Initialized":"❌ Not initialized")),l("Firestore instance: ".concat(n.db?"✅ Initialized":"❌ Not initialized")),l("Auth app: ".concat(n.j2.app?"✅ Connected":"❌ Not connected")),l("Firestore app: ".concat(n.db.app?"✅ Connected":"❌ Not connected")),l("\n=== TEST 2: Environment Variables ==="),l("API Key: ".concat("✅ Set")),l("Auth Domain: ".concat("✅ Set")),l("Project ID: ".concat("✅ Set")),l("Project ID Value: ".concat("mytube-india")),l("\n=== TEST 3: Anonymous Authentication ===");try{l("Attempting anonymous sign-in...");let e=await (0,o.zK)(n.j2);l("✅ Anonymous auth successful: ".concat(e.user.uid)),l("\n=== TEST 4: Firestore Write Test ===");let t=(0,c.H9)(n.db,"connectivity_test","test_".concat(Date.now()));await (0,c.BN)(t,{test:!0,timestamp:c.Dc.now(),message:"Connectivity test successful"}),l("✅ Firestore write successful"),l("\n=== TEST 5: Firestore Read Test ===");let a=await (0,c.x7)(t);a.exists()?(l("✅ Firestore read successful"),l("   Data: ".concat(JSON.stringify(a.data())))):l("❌ Firestore read failed - document not found"),await (0,o.CI)(n.j2),l("\n✅ Signed out successfully"),l("\n\uD83C\uDF89 ALL TESTS PASSED - Firebase connectivity is working!"),l("The registration issue might be specific to email/password authentication.")}catch(e){l("❌ Anonymous auth failed: ".concat(e.message)),l("   Error code: ".concat(e.code)),"auth/network-request-failed"===e.code?(l("\n\uD83D\uDD27 NETWORK CONNECTIVITY ISSUE DETECTED:"),l("   1. Check your internet connection"),l("   2. Check if firewall/antivirus is blocking Firebase"),l("   3. Try using a different network (mobile hotspot)"),l("   4. Check if your ISP blocks Firebase services"),l("   5. Try using a VPN"),l(""),l("   Firebase domains that need to be accessible:"),l("   - firebase.google.com"),l("   - firestore.googleapis.com"),l("   - identitytoolkit.googleapis.com"),l("   - mytube-india.firebaseapp.com")):"auth/operation-not-allowed"===e.code&&(l("\n\uD83D\uDD27 FIREBASE CONFIGURATION ISSUE:"),l("   1. Go to Firebase Console → Authentication → Sign-in method"),l('   2. Enable "Anonymous" authentication'),l('   3. Or enable "Email/Password" authentication'))}}catch(e){l("❌ Connectivity test failed: ".concat(e.message)),l("   Error code: ".concat(e.code))}finally{r(!1)}},m=async()=>{t(""),r(!0);try{l("\uD83D\uDD0D Network Diagnostics...\n"),l("=== TEST 1: Firebase Domain Accessibility ===");try{await fetch("https://firebase.google.com",{mode:"no-cors"}),l("✅ Firebase.google.com is accessible")}catch(e){l("❌ Firebase.google.com not accessible: ".concat(e.message))}l("\n=== TEST 2: Project Domain Accessibility ===");try{await fetch("https://mytube-india.firebaseapp.com",{mode:"no-cors"}),l("✅ mytube-india.firebaseapp.com is accessible")}catch(e){l("❌ mytube-india.firebaseapp.com not accessible: ".concat(e.message))}l("\n=== TEST 3: Firestore API Accessibility ===");try{await fetch("https://firestore.googleapis.com",{mode:"no-cors"}),l("✅ firestore.googleapis.com is accessible")}catch(e){l("❌ firestore.googleapis.com not accessible: ".concat(e.message))}l("\n=== RECOMMENDATIONS ==="),l("If any domains are not accessible:"),l("1. Check your internet connection"),l("2. Try disabling firewall/antivirus temporarily"),l("3. Try using a different network (mobile hotspot)"),l("4. Contact your ISP about Firebase access"),l("5. Try using a VPN service")}catch(e){l("❌ Network diagnostics failed: ".concat(e.message))}finally{r(!1)}},u=async()=>{t(""),r(!0);try{l("\uD83D\uDD0D Testing Specific UID: b7690183-ab6b-4719-944d-c0a080a59e8c\n"),l("=== Checking if UID exists in Firestore ===");let t=(0,c.H9)(n.db,"users","b7690183-ab6b-4719-944d-c0a080a59e8c");try{let i=await (0,c.x7)(t);if(i.exists()){var e,a;let t=i.data();l("✅ User document found!"),l("   Name: ".concat(t.name||"N/A")),l("   Email: ".concat(t.email||"N/A")),l("   Plan: ".concat(t.plan||"N/A")),l("   Referral Code: ".concat(t.referralCode||"N/A")),l("   Status: ".concat(t.status||"N/A")),l("   Joined: ".concat((null==(a=t.joinedDate)||null==(e=a.toDate())?void 0:e.toLocaleString())||"N/A")),l("\n✅ This confirms Firestore is working!"),l("The registration issue might be specific to the registration flow.")}else l("❌ User document not found"),l("This UID might be from Firebase Auth but Firestore document creation failed")}catch(e){l("❌ Firestore query failed: ".concat(e.message)),l("   Error code: ".concat(e.code)),"permission-denied"===e.code?l("   This indicates Firestore security rules are blocking reads"):"unavailable"===e.code&&l("   This indicates Firestore service is unavailable")}}catch(e){l("❌ UID test failed: ".concat(e.message))}finally{r(!1)}},b=async()=>{t(""),r(!0);let e=null;try{l("\uD83D\uDCE7 Testing Email/Password Authentication...\n"),l("=== Email/Password Authentication Test ===");let t="test".concat(Date.now(),"@example.com");l("Creating user with email: ".concat(t));try{e=(await (0,o.eJ)(n.j2,t,"test123456")).user,l("✅ Email/Password auth successful: ".concat(e.uid)),l("   Email: ".concat(e.email)),l("   Email verified: ".concat(e.emailVerified)),l("\n=== Testing Firestore Document Creation ===");let a=(0,c.H9)(n.db,"users",e.uid),i={name:"Test User",email:t.toLowerCase(),mobile:"9876543210",referralCode:"TEST".concat(Date.now().toString().slice(-4)),plan:"Trial",joinedDate:c.Dc.now(),wallet:0,status:"active"};l("Attempting Firestore document creation..."),await (0,c.BN)(a,i),l("✅ Firestore document created successfully");let s=await (0,c.x7)(a);if(s.exists()){l("✅ Document verification successful");let e=s.data();l("   Name: ".concat(e.name)),l("   Email: ".concat(e.email)),l("   Plan: ".concat(e.plan)),l("\n\uD83C\uDF89 SUCCESS: Email/Password registration flow works!"),l("The registration issue might be in the form validation or error handling.")}else l("❌ Document verification failed")}catch(e){l("❌ Email/Password auth failed: ".concat(e.message)),l("   Error code: ".concat(e.code)),"auth/operation-not-allowed"===e.code?(l("\n\uD83D\uDD27 EMAIL/PASSWORD AUTHENTICATION DISABLED:"),l("   1. Go to Firebase Console → Authentication → Sign-in method"),l('   2. Find "Email/Password" in the list'),l('   3. Click "Enable" and save'),l('   4. Make sure both "Email/Password" and "Email link" are enabled')):"auth/network-request-failed"===e.code&&(l("\n\uD83D\uDD27 NETWORK ISSUE DETECTED:"),l("   The original network issue is still present"),l("   Try the network diagnostic solutions"))}}catch(e){l("❌ Email/Password test failed: ".concat(e.message)),l("   Error code: ".concat(e.code))}finally{if(e)try{l("\n=== Cleanup ==="),await (0,o.hG)(e),l("✅ Test user deleted")}catch(e){l("⚠️ User deletion failed: ".concat(e.message))}try{await (0,o.CI)(n.j2),l("✅ Signed out")}catch(e){l("⚠️ Sign out failed: ".concat(e.message))}r(!1)}};return(0,i.jsx)("div",{className:"min-h-screen p-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Firebase Connectivity Test"}),(0,i.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,i.jsxs)("div",{className:"flex flex-wrap gap-4 mb-4",children:[(0,i.jsx)("button",{onClick:d,disabled:a,className:"btn-primary",children:a?"Testing...":"Test Firebase Connectivity"}),(0,i.jsx)("button",{onClick:m,disabled:a,className:"btn-primary",children:a?"Testing...":"Test Network Diagnostics"}),(0,i.jsx)("button",{onClick:u,disabled:a,className:"btn-primary",children:a?"Testing...":"Test Specific UID"}),(0,i.jsx)("button",{onClick:b,disabled:a,className:"btn-primary",children:a?"Testing...":"Test Email/Password Auth"})]}),(0,i.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,i.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||"Click a test button to start..."})})]}),(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},6104:(e,t,a)=>{"use strict";a.d(t,{Cn:()=>m,db:()=>d,j2:()=>l});var i=a(3915),s=a(3004),n=a(5317),o=a(858),c=a(2144);let r=(0,i.Dk)().length?(0,i.Sx)():(0,i.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,s.xI)(r),d=(0,n.aU)(r);(0,o.c7)(r);let m=(0,c.Uz)(r,"us-central1")},7381:(e,t,a)=>{Promise.resolve().then(a.bind(a,1247))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8818,8441,1684,7358],()=>t(7381)),_N_E=e.O()}]);