(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3179],{3633:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(5155),l=t(2115),a=t(6874),i=t.n(a),n=t(6681),c=t(3592),d=t(4752),o=t.n(d);function x(){let{user:e,loading:s,isAdmin:t}=(0,n.wC)(),[a,d]=(0,l.useState)(!1),[x,m]=(0,l.useState)(null),[h,u]=(0,l.useState)(!1),[g,p]=(0,l.useState)(null),[j,y]=(0,l.useState)(!1),[v,b]=(0,l.useState)(null),[N,f]=(0,l.useState)(!1),[w,C]=(0,l.useState)(null),[k,E]=(0,l.useState)(!1),[S,F]=(0,l.useState)(null);if(s)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})});if(!t)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})});let A=async()=>{try{d(!0);let e=await (0,c.Oe)();m(e),o().fire({icon:"success",title:"Daily Active Days Increment Completed!",html:'\n          <div class="text-left">\n            <p><strong>Incremented:</strong> '.concat(e.incrementedCount," users</p>\n            <p><strong>Skipped:</strong> ").concat(e.skippedCount," users</p>\n            <p><strong>Errors:</strong> ").concat(e.errorCount," users</p>\n            ").concat(e.reason?"<p><strong>Reason:</strong> ".concat(e.reason,"</p>"):"","\n          </div>\n        "),timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running daily active days increment:",e),o().fire({icon:"error",title:"Error",text:"Failed to run daily active days increment. Please try again."})}finally{d(!1)}},R=async()=>{try{u(!0);let e=await (0,c.GA)();p(e),o().fire({icon:"success",title:"Quick Video Migration Completed!",html:'\n          <div class="text-left">\n            <p><strong>Migrated:</strong> '.concat(e.migratedCount," users</p>\n            <p><strong>Skipped:</strong> ").concat(e.skippedCount," users</p>\n            <p><strong>Errors:</strong> ").concat(e.errorCount," users</p>\n          </div>\n        "),timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running quick video migration:",e),o().fire({icon:"error",title:"Error",text:"Failed to run quick video migration. Please try again."})}finally{u(!1)}},D=async()=>{try{y(!0);let e=await (0,c.Kc)();b(e),o().fire({icon:"success",title:"Active Days Recalculation Completed!",html:'\n          <div class="text-left">\n            <p><strong>Recalculated:</strong> '.concat(e.recalculatedCount," users</p>\n            <p><strong>Errors:</strong> ").concat(e.errorCount,' users</p>\n            <p class="text-sm text-gray-600 mt-2">All users now have correct active days based on registration date.</p>\n          </div>\n        '),timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running active days recalculation:",e),o().fire({icon:"error",title:"Error",text:"Failed to recalculate active days. Please try again."})}finally{y(!1)}},U=async()=>{try{f(!0);let e=await (0,c.gx)();C(e),o().fire({icon:"success",title:"Force Daily Process Catchup Completed!",html:'\n          <div class="text-left">\n            <p><strong>Incremented:</strong> '.concat(e.incrementedCount," users</p>\n            <p><strong>Skipped:</strong> ").concat(e.skippedCount," users</p>\n            <p><strong>Errors:</strong> ").concat(e.errorCount,' users</p>\n            <p class="text-sm text-green-600 mt-2">All users now have updated active days regardless of last visit.</p>\n          </div>\n        '),timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running force catchup:",e),o().fire({icon:"error",title:"Error",text:"Failed to run force catchup. Please try again."})}finally{f(!1)}},P=async()=>{try{E(!0);let e=await (0,c.wD)();F(e),o().fire({icon:"success",title:"Reset Last Update Completed!",html:'\n          <div class="text-left">\n            <p><strong>Reset:</strong> '.concat(e.resetCount," users</p>\n            <p><strong>Errors:</strong> ").concat(e.errorCount,' users</p>\n            <p class="text-sm text-blue-600 mt-2">All users can now receive fresh daily increments.</p>\n          </div>\n        '),timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error resetting last update:",e),o().fire({icon:"error",title:"Error",text:"Failed to reset last update. Please try again."})}finally{E(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Daily Active Days Management"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600",children:"Manually trigger daily active days increment for all users"})]}),(0,r.jsxs)(i(),{href:"/admin",className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin"]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,r.jsx)("i",{className:"fas fa-calendar-plus mr-2 text-blue-500"}),"Daily Active Days Increment"]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"How it works:"}),(0,r.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Increments active days by 1 for all users (regardless of login status)"}),(0,r.jsx)("li",{children:"• Skips users who are on approved leave today"}),(0,r.jsx)("li",{children:"• Skips increment if today is an admin leave day"}),(0,r.jsx)("li",{children:"• Only processes each user once per day"}),(0,r.jsx)("li",{children:"• Preserves manually set active days by admin"})]})]}),(0,r.jsx)("button",{onClick:A,disabled:a,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-play mr-2"}),"Run Daily Active Days Increment"]})})]}),x&&(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Last Execution Result"}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:x.incrementedCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Incremented"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:x.skippedCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:x.errorCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]}),x.reason&&(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("span",{className:"inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm",children:x.reason})})]})]}),(0,r.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Important Notes"}),(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsxs)("ul",{className:"text-yellow-800 text-sm space-y-2",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Automatic Execution:"})," This process also runs automatically when users interact with the platform (once per day)"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Manual Trigger:"})," Use this page to manually trigger the process if needed"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Safety:"})," The process is safe to run multiple times per day - it will skip users already processed"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Leave Days:"})," Active days will not increment on admin leave days or user leave days"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Manual Override:"})," Users with manually set active days will still get daily increments"]})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,r.jsx)("i",{className:"fas fa-video mr-2 text-purple-500"}),"Quick Video Advantage Migration"]}),(0,r.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"font-medium text-purple-900 mb-2",children:"Migration Purpose:"}),(0,r.jsxs)("ul",{className:"text-purple-800 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Converts old expiry date system to new remaining days system"}),(0,r.jsx)("li",{children:"• Calculates remaining days from current expiry dates"}),(0,r.jsx)("li",{children:"• Disables expired quick video advantages"}),(0,r.jsx)("li",{children:"• Ensures proper daily decrement functionality"}),(0,r.jsx)("li",{children:"• Safe to run multiple times (skips already migrated users)"})]})]}),(0,r.jsx)("button",{onClick:R,disabled:h,className:"bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Migrating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Migrate Quick Video System"]})})]}),g&&(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Migration Result"}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:g.migratedCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Migrated"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:g.skippedCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:g.errorCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,r.jsx)("i",{className:"fas fa-calculator mr-2 text-orange-500"}),"Active Days Recalculation"]}),(0,r.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"font-medium text-orange-900 mb-2",children:"Fix Active Days Issues:"}),(0,r.jsxs)("ul",{className:"text-orange-800 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Recalculates active days for all users based on correct formula"}),(0,r.jsx)("li",{children:'• Fixes the "showing 3 days instead of 2" issue'}),(0,r.jsx)("li",{children:"• Ensures Trial plans expire at exactly 3+ active days"}),(0,r.jsx)("li",{children:"• Ensures other plans expire at exactly 31+ active days"}),(0,r.jsx)("li",{children:"• Safe to run multiple times (only updates incorrect values)"})]})]}),(0,r.jsx)("button",{onClick:D,disabled:j,className:"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Recalculating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-calculator mr-2"}),"Fix Active Days Calculation"]})})]}),v&&(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recalculation Result"}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:v.recalculatedCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Fixed"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:v.errorCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,r.jsx)("i",{className:"fas fa-rocket mr-2 text-red-500"}),"Force Daily Process Catchup"]}),(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"font-medium text-red-900 mb-2",children:"Emergency Active Days Update:"}),(0,r.jsxs)("ul",{className:"text-red-800 text-sm space-y-1",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Forces daily process to run"})," regardless of last run date"]}),(0,r.jsx)("li",{children:"• Updates ALL users' active days by +1 (except on leave days)"}),(0,r.jsx)("li",{children:"• Use this when many users haven't received daily increments"}),(0,r.jsx)("li",{children:"• Safe to run - respects leave days and prevents double increments"}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Recommended:"})," Run this to catch up all missed days"]})]})]}),(0,r.jsx)("button",{onClick:U,disabled:N,className:"bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:N?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Force Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-rocket mr-2"}),"Force Daily Process Catchup"]})})]}),w&&(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Force Catchup Result"}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:w.incrementedCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Updated"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:w.skippedCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:w.errorCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,r.jsx)("i",{className:"fas fa-undo mr-2 text-indigo-500"}),"Reset Last Update Tracking"]}),(0,r.jsxs)("div",{className:"bg-indigo-50 border border-indigo-200 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"font-medium text-indigo-900 mb-2",children:"Reset Update Tracking:"}),(0,r.jsxs)("ul",{className:"text-indigo-800 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Clears the lastActiveDaysUpdate field for all users"}),(0,r.jsx)("li",{children:"• Allows fresh daily increments for all users"}),(0,r.jsx)("li",{children:"• Use this if users are stuck with old update timestamps"}),(0,r.jsx)("li",{children:"• Run this BEFORE running the force catchup for best results"}),(0,r.jsx)("li",{children:"• Safe to run multiple times"})]})]}),(0,r.jsx)("button",{onClick:P,disabled:k,className:"bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:k?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Resetting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-undo mr-2"}),"Reset All Users' Last Update"]})})]}),S&&(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Reset Result"}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:S.resetCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Reset"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:S.errorCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,r.jsx)("i",{className:"fas fa-lightbulb mr-2 text-yellow-500"}),"Recommended Process for Fixing Active Days"]}),(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-yellow-900 mb-3",children:"Step-by-Step Fix:"}),(0,r.jsxs)("ol",{className:"text-yellow-800 text-sm space-y-2 list-decimal list-inside",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Step 1:"}),' Click "Reset All Users\' Last Update" to clear tracking']}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Step 2:"}),' Click "Force Daily Process Catchup" to update all users']}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Step 3:"})," Verify results - all users should now have correct active days"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Step 4:"})," The daily process will now run automatically going forward"]})]}),(0,r.jsx)("div",{className:"mt-4 p-3 bg-yellow-100 rounded-lg",children:(0,r.jsxs)("p",{className:"text-yellow-900 text-sm font-medium",children:["\uD83D\uDCA1 ",(0,r.jsx)("strong",{children:"Important:"})," After running these steps, active days will automatically increment daily for ALL users, regardless of whether they visit the platform or not."]})})]})]})]})})}},9178:(e,s,t)=>{Promise.resolve().then(t.bind(t,3633))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>s(9178)),_N_E=e.O()}]);