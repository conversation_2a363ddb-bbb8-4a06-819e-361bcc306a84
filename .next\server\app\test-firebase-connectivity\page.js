(()=>{var e={};e.id=3241,e.ids=[3241],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14923:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l});var r=s(65239),i=s(48088),o=s(88170),a=s.n(o),n=s(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let l={children:["",{children:["test-firebase-connectivity",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,42689)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-firebase-connectivity\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-firebase-connectivity\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-firebase-connectivity/page",pathname:"/test-firebase-connectivity",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30475:(e,t,s)=>{Promise.resolve().then(s.bind(s,74511))},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,s)=>{"use strict";s.d(t,{Cn:()=>u,db:()=>d,j2:()=>l});var r=s(67989),i=s(63385),o=s(75535),a=s(70146),n=s(24791);let c=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,i.xI)(c),d=(0,o.aU)(c);(0,a.c7)(c);let u=(0,n.Uz)(c,"us-central1")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},42689:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-firebase-connectivity\\page.tsx","default")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74511:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(60687),i=s(43210),o=s(33784),a=s(63385),n=s(75535);function c(){let[e,t]=(0,i.useState)(""),[s,c]=(0,i.useState)(!1),l=e=>{t(t=>t+e+"\n")},d=async()=>{t(""),c(!0);try{l("\uD83C\uDF10 Testing Firebase Connectivity...\n"),l("=== TEST 1: Firebase Instances ==="),l(`Auth instance: ${o.j2?"✅ Initialized":"❌ Not initialized"}`),l(`Firestore instance: ${o.db?"✅ Initialized":"❌ Not initialized"}`),l(`Auth app: ${o.j2.app?"✅ Connected":"❌ Not connected"}`),l(`Firestore app: ${o.db.app?"✅ Connected":"❌ Not connected"}`),l("\n=== TEST 2: Environment Variables ==="),l(`API Key: ✅ Set`),l(`Auth Domain: ✅ Set`),l(`Project ID: ✅ Set`),l("Project ID Value: mytube-india"),l("\n=== TEST 3: Anonymous Authentication ===");try{l("Attempting anonymous sign-in...");let e=await (0,a.zK)(o.j2);l(`✅ Anonymous auth successful: ${e.user.uid}`),l("\n=== TEST 4: Firestore Write Test ===");let t=(0,n.H9)(o.db,"connectivity_test",`test_${Date.now()}`);await (0,n.BN)(t,{test:!0,timestamp:n.Dc.now(),message:"Connectivity test successful"}),l("✅ Firestore write successful"),l("\n=== TEST 5: Firestore Read Test ===");let s=await (0,n.x7)(t);s.exists()?(l("✅ Firestore read successful"),l(`   Data: ${JSON.stringify(s.data())}`)):l("❌ Firestore read failed - document not found"),await (0,a.CI)(o.j2),l("\n✅ Signed out successfully"),l("\n\uD83C\uDF89 ALL TESTS PASSED - Firebase connectivity is working!"),l("The registration issue might be specific to email/password authentication.")}catch(e){l(`❌ Anonymous auth failed: ${e.message}`),l(`   Error code: ${e.code}`),"auth/network-request-failed"===e.code?(l("\n\uD83D\uDD27 NETWORK CONNECTIVITY ISSUE DETECTED:"),l("   1. Check your internet connection"),l("   2. Check if firewall/antivirus is blocking Firebase"),l("   3. Try using a different network (mobile hotspot)"),l("   4. Check if your ISP blocks Firebase services"),l("   5. Try using a VPN"),l(""),l("   Firebase domains that need to be accessible:"),l("   - firebase.google.com"),l("   - firestore.googleapis.com"),l("   - identitytoolkit.googleapis.com"),l("   - mytube-india.firebaseapp.com")):"auth/operation-not-allowed"===e.code&&(l("\n\uD83D\uDD27 FIREBASE CONFIGURATION ISSUE:"),l("   1. Go to Firebase Console → Authentication → Sign-in method"),l('   2. Enable "Anonymous" authentication'),l('   3. Or enable "Email/Password" authentication'))}}catch(e){l(`❌ Connectivity test failed: ${e.message}`),l(`   Error code: ${e.code}`)}finally{c(!1)}},u=async()=>{t(""),c(!0);try{l("\uD83D\uDD0D Network Diagnostics...\n"),l("=== TEST 1: Firebase Domain Accessibility ===");try{await fetch("https://firebase.google.com",{mode:"no-cors"}),l("✅ Firebase.google.com is accessible")}catch(e){l(`❌ Firebase.google.com not accessible: ${e.message}`)}l("\n=== TEST 2: Project Domain Accessibility ===");try{await fetch("https://mytube-india.firebaseapp.com",{mode:"no-cors"}),l("✅ mytube-india.firebaseapp.com is accessible")}catch(e){l(`❌ mytube-india.firebaseapp.com not accessible: ${e.message}`)}l("\n=== TEST 3: Firestore API Accessibility ===");try{await fetch("https://firestore.googleapis.com",{mode:"no-cors"}),l("✅ firestore.googleapis.com is accessible")}catch(e){l(`❌ firestore.googleapis.com not accessible: ${e.message}`)}l("\n=== RECOMMENDATIONS ==="),l("If any domains are not accessible:"),l("1. Check your internet connection"),l("2. Try disabling firewall/antivirus temporarily"),l("3. Try using a different network (mobile hotspot)"),l("4. Contact your ISP about Firebase access"),l("5. Try using a VPN service")}catch(e){l(`❌ Network diagnostics failed: ${e.message}`)}finally{c(!1)}},p=async()=>{t(""),c(!0);try{l("\uD83D\uDD0D Testing Specific UID: b7690183-ab6b-4719-944d-c0a080a59e8c\n"),l("=== Checking if UID exists in Firestore ===");let e=(0,n.H9)(o.db,"users","b7690183-ab6b-4719-944d-c0a080a59e8c");try{let t=await (0,n.x7)(e);if(t.exists()){let e=t.data();l("✅ User document found!"),l(`   Name: ${e.name||"N/A"}`),l(`   Email: ${e.email||"N/A"}`),l(`   Plan: ${e.plan||"N/A"}`),l(`   Referral Code: ${e.referralCode||"N/A"}`),l(`   Status: ${e.status||"N/A"}`),l(`   Joined: ${e.joinedDate?.toDate()?.toLocaleString()||"N/A"}`),l("\n✅ This confirms Firestore is working!"),l("The registration issue might be specific to the registration flow.")}else l("❌ User document not found"),l("This UID might be from Firebase Auth but Firestore document creation failed")}catch(e){l(`❌ Firestore query failed: ${e.message}`),l(`   Error code: ${e.code}`),"permission-denied"===e.code?l("   This indicates Firestore security rules are blocking reads"):"unavailable"===e.code&&l("   This indicates Firestore service is unavailable")}}catch(e){l(`❌ UID test failed: ${e.message}`)}finally{c(!1)}},m=async()=>{t(""),c(!0);let e=null;try{l("\uD83D\uDCE7 Testing Email/Password Authentication...\n"),l("=== Email/Password Authentication Test ===");let t=`test${Date.now()}@example.com`;l(`Creating user with email: ${t}`);try{e=(await (0,a.eJ)(o.j2,t,"test123456")).user,l(`✅ Email/Password auth successful: ${e.uid}`),l(`   Email: ${e.email}`),l(`   Email verified: ${e.emailVerified}`),l("\n=== Testing Firestore Document Creation ===");let s=(0,n.H9)(o.db,"users",e.uid),r={name:"Test User",email:t.toLowerCase(),mobile:"9876543210",referralCode:`TEST${Date.now().toString().slice(-4)}`,plan:"Trial",joinedDate:n.Dc.now(),wallet:0,status:"active"};l("Attempting Firestore document creation..."),await (0,n.BN)(s,r),l("✅ Firestore document created successfully");let i=await (0,n.x7)(s);if(i.exists()){l("✅ Document verification successful");let e=i.data();l(`   Name: ${e.name}`),l(`   Email: ${e.email}`),l(`   Plan: ${e.plan}`),l("\n\uD83C\uDF89 SUCCESS: Email/Password registration flow works!"),l("The registration issue might be in the form validation or error handling.")}else l("❌ Document verification failed")}catch(e){l(`❌ Email/Password auth failed: ${e.message}`),l(`   Error code: ${e.code}`),"auth/operation-not-allowed"===e.code?(l("\n\uD83D\uDD27 EMAIL/PASSWORD AUTHENTICATION DISABLED:"),l("   1. Go to Firebase Console → Authentication → Sign-in method"),l('   2. Find "Email/Password" in the list'),l('   3. Click "Enable" and save'),l('   4. Make sure both "Email/Password" and "Email link" are enabled')):"auth/network-request-failed"===e.code&&(l("\n\uD83D\uDD27 NETWORK ISSUE DETECTED:"),l("   The original network issue is still present"),l("   Try the network diagnostic solutions"))}}catch(e){l(`❌ Email/Password test failed: ${e.message}`),l(`   Error code: ${e.code}`)}finally{if(e)try{l("\n=== Cleanup ==="),await (0,a.hG)(e),l("✅ Test user deleted")}catch(e){l(`⚠️ User deletion failed: ${e.message}`)}try{await (0,a.CI)(o.j2),l("✅ Signed out")}catch(e){l(`⚠️ Sign out failed: ${e.message}`)}c(!1)}};return(0,r.jsx)("div",{className:"min-h-screen p-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Firebase Connectivity Test"}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 mb-4",children:[(0,r.jsx)("button",{onClick:d,disabled:s,className:"btn-primary",children:s?"Testing...":"Test Firebase Connectivity"}),(0,r.jsx)("button",{onClick:u,disabled:s,className:"btn-primary",children:s?"Testing...":"Test Network Diagnostics"}),(0,r.jsx)("button",{onClick:p,disabled:s,className:"btn-primary",children:s?"Testing...":"Test Specific UID"}),(0,r.jsx)("button",{onClick:m,disabled:s,className:"btn-primary",children:s?"Testing...":"Test Email/Password Auth"})]}),(0,r.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,r.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||"Click a test button to start..."})})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93523:(e,t,s)=>{Promise.resolve().then(s.bind(s,42689))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[6204,6958,8441],()=>s(14923));module.exports=r})();