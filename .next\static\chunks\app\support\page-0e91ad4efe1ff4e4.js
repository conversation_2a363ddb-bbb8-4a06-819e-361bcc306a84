(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6174],{7112:(e,s,t)=>{Promise.resolve().then(t.bind(t,9357))},7460:(e,s,t)=>{"use strict";t.d(s,{J:()=>i});var a=t(2115),l=t(3592);function i(e){let[s,t]=(0,a.useState)(!1),[i,n]=(0,a.useState)(!0);(0,a.useEffect)(()=>{e?r():n(!1)},[e]);let r=async()=>{try{n(!0);let s=await (0,l.iA)(e);t(s)}catch(e){console.error("Error checking for blocking notifications:",e),t(!1)}finally{n(!1)}};return{hasBlockingNotifications:s,isChecking:i,checkForBlockingNotifications:r,markAllAsRead:()=>{t(!1)}}}},8647:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(5155),l=t(2115),i=t(3592);function n(e){let{userId:s,onAllRead:t}=e,[n,r]=(0,l.useState)([]),[c,d]=(0,l.useState)(0),[o,m]=(0,l.useState)(!0);(0,l.useEffect)(()=>{s&&x()},[s]);let x=async()=>{try{m(!0);let e=await (0,i.AX)(s);r(e),0===e.length&&t()}catch(e){console.error("Error loading notifications:",e),t()}finally{m(!1)}},h=async()=>{let e=n[c];(null==e?void 0:e.id)&&(await (0,i.bA)(e.id,s),c<n.length-1?d(c+1):t())};if(o)return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===n.length)return null;let u=n[c];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(u.type)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,a.jsxs)("p",{className:"text-blue-100 text-sm",children:[c+1," of ",n.length," notifications"]})]})]}),(0,a.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:u.title}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,a.jsx)("p",{className:"text-gray-800 leading-relaxed",children:u.message})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,a.jsxs)("span",{children:["From: ",u.createdBy]}),(0,a.jsx)("span",{children:(e=>{let s=Math.floor((new Date().getTime()-e.getTime())/1e3);return s<60?"Just now":s<3600?"".concat(Math.floor(s/60)," minutes ago"):s<86400?"".concat(Math.floor(s/3600)," hours ago"):"".concat(Math.floor(s/86400)," days ago")})(u.createdAt)})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[c+1,"/",n.length]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((c+1)/n.length*100,"%")}})})]}),(0,a.jsxs)("button",{onClick:h,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,a.jsx)("i",{className:"fas fa-check"}),(0,a.jsx)("span",{children:c<n.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)("i",{className:"fas fa-info-circle"}),(0,a.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}},9357:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(5155),l=t(2115),i=t(6874),n=t.n(i),r=t(6681),c=t(7460),d=t(8647);function o(){let e=function(){let e=new Date,s=e.getHours(),t=e.getDay();if(!(t>=1&&t<=5)){let s=new Date(e);return s.setDate(e.getDate()+(0===t?1:8-t)),s.setHours(9,0,0,0),{isAvailable:!1,message:"Support is available Monday to Friday, 9 AM - 6 PM",nextAvailableTime:"Next available: ".concat(s.toLocaleDateString()," at 9:00 AM")}}if(!(s>=9&&s<18))if(s<9)return new Date(e).setHours(9,0,0,0),{isAvailable:!1,message:"Support hours: 9 AM - 6 PM (Working days)",nextAvailableTime:"Available today at 9:00 AM"};else{let s=new Date(e);s.setDate(e.getDate()+1);let t=s.getDay();if(t>=1&&t<=5)return s.setHours(9,0,0,0),{isAvailable:!1,message:"Support hours: 9 AM - 6 PM (Working days)",nextAvailableTime:"Next available: ".concat(s.toLocaleDateString()," at 9:00 AM")};{let e=new Date(s);return e.setDate(s.getDate()+(0===t?1:8-t)),e.setHours(9,0,0,0),{isAvailable:!1,message:"Support is available Monday to Friday, 9 AM - 6 PM",nextAvailableTime:"Next available: ".concat(e.toLocaleDateString()," at 9:00 AM")}}}return{isAvailable:!0,message:"Support is currently available! We typically respond within minutes."}}();return{status:e.isAvailable?"online":"offline",message:e.message,nextAvailable:e.nextAvailableTime,hoursInfo:"Monday to Friday, 9:00 AM - 6:00 PM"}}function m(){let{user:e,loading:s}=(0,r.Nu)(),{hasBlockingNotifications:t,isChecking:i,markAllAsRead:m}=(0,c.J)((null==e?void 0:e.uid)||null),[x,h]=(0,l.useState)(o());return((0,l.useEffect)(()=>{let e=setInterval(()=>{h(o())},6e4);return()=>clearInterval(e)},[]),s||i)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:s?"Loading...":"Checking notifications..."})]})}):t&&e?(0,a.jsx)(d.A,{userId:e.uid,onAllRead:m}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Support & Help"}),(0,a.jsx)("div",{className:"w-24"})," "]})}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-headset mr-2"}),"Contact Support"]}),(0,a.jsxs)("div",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("online"===x.status?"bg-green-500/20 text-green-400 border border-green-500/30":"bg-red-500/20 text-red-400 border border-red-500/30"),children:[(0,a.jsx)("i",{className:"fas fa-circle mr-2 ".concat("online"===x.status?"text-green-400":"text-red-400")}),"online"===x.status?"Online":"Offline"]})]}),(0,a.jsxs)("div",{className:"p-4 rounded-lg mb-6 ".concat("online"===x.status?"bg-green-500/10 border border-green-500/20":"bg-orange-500/10 border border-orange-500/20"),children:[(0,a.jsxs)("p",{className:"font-medium mb-2 ".concat("online"===x.status?"text-green-400":"text-orange-400"),children:[(0,a.jsx)("i",{className:"fas ".concat("online"===x.status?"fa-check-circle":"fa-clock"," mr-2")}),x.message]}),x.nextAvailable&&(0,a.jsx)("p",{className:"text-white/60 text-sm",children:x.nextAvailable}),(0,a.jsxs)("p",{className:"text-white/60 text-sm mt-2",children:[(0,a.jsx)("i",{className:"fas fa-calendar mr-2"}),x.hoursInfo]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("i",{className:"fab fa-whatsapp text-green-400 text-3xl mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-white font-bold text-lg",children:"WhatsApp Support"}),(0,a.jsx)("p",{className:"text-green-300 text-sm",children:"Instant messaging support"})]})]}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Get instant help via WhatsApp during business hours. Our team responds quickly to your queries on working days."}),(0,a.jsxs)("div",{className:"mb-3 p-2 rounded text-sm ".concat("online"===x.status?"bg-green-500/20 text-green-400":"bg-orange-500/20 text-orange-400"),children:[(0,a.jsx)("i",{className:"fas ".concat("online"===x.status?"fa-check":"fa-clock"," mr-2")}),"online"===x.status?"Available now - Usually responds within minutes":"Currently offline - Will respond when available"]}),(0,a.jsxs)("a",{href:"https://wa.me/917676636990",target:"_blank",rel:"noopener noreferrer",className:"btn-success bg-green-500 hover:bg-green-600 w-full",children:[(0,a.jsx)("i",{className:"fab fa-whatsapp mr-2"}),"Chat on WhatsApp: +91 7676636990"]})]}),(0,a.jsxs)("div",{className:"bg-blue-500/20 border border-blue-500/30 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("i",{className:"fas fa-envelope text-blue-400 text-3xl mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-white font-bold text-lg",children:"Email Support"}),(0,a.jsx)("p",{className:"text-blue-300 text-sm",children:"Detailed support via email"})]})]}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Send us detailed queries and we'll respond within 24 hours on working days."}),(0,a.jsxs)("div",{className:"mb-3 p-2 rounded text-sm ".concat("online"===x.status?"bg-blue-500/20 text-blue-400":"bg-orange-500/20 text-orange-400"),children:[(0,a.jsx)("i",{className:"fas ".concat("online"===x.status?"fa-check":"fa-clock"," mr-2")}),"online"===x.status?"Available now - Response within 24 hours":"Currently offline - Will respond on next working day"]}),(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"btn-primary bg-blue-500 hover:bg-blue-600 w-full",children:[(0,a.jsx)("i",{className:"fas fa-envelope mr-2"}),"Email: <EMAIL>"]})]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white mb-6",children:[(0,a.jsx)("i",{className:"fas fa-question-circle mr-2"}),"Frequently Asked Questions"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-play-circle mr-2 text-youtube-red"}),"How do I earn money by watching videos?"]}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"Watch videos completely to earn money. You earn ₹10-400 per batch of 50 videos depending on your plan. Videos must be watched for the full duration to count towards your earnings."})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-wallet mr-2 text-green-400"}),"When can I withdraw my earnings?"]}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"Withdrawals are available between 10:00 AM to 6:00 PM on non-leave days. Minimum withdrawal is ₹50. Trial users cannot withdraw - upgrade to a paid plan first."})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-users mr-2 text-blue-400"}),"How does the referral system work?"]}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"Share your referral code with friends. When they join and purchase a plan, you earn a bonus ranging from ₹50 to ₹1200 depending on the plan they choose."})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-crown mr-2 text-yellow-400"}),"What are the different plans available?"]}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"We offer Trial (free), Starter (₹499), Basic (₹1499), Premium (₹2999), Gold (₹3999), Platinum (₹5999), and Diamond (₹9999) plans with different earning rates and video durations."})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-bolt mr-2 text-orange-400"}),"What is Quick Video Advantage?"]}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"Admins can grant temporary quick video advantage that reduces your video duration to 30 seconds for a limited period, helping you complete tasks faster."})]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),"Support Hours"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-white font-semibold mb-2",children:"WhatsApp Support"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mb-1",children:"Monday - Friday: 9:00 AM - 6:00 PM"}),(0,a.jsx)("p",{className:"text-white/60 text-sm mb-1",children:"Working days only"}),(0,a.jsx)("p",{className:"text-green-400 text-sm",children:"Usually responds within minutes during business hours"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-white font-semibold mb-2",children:"Email Support"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mb-1",children:"Monday - Friday: 9:00 AM - 6:00 PM"}),(0,a.jsx)("p",{className:"text-white/60 text-sm mb-1",children:"Working days only"}),(0,a.jsx)("p",{className:"text-blue-400 text-sm",children:"Response within 24 hours on working days"})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>s(7112)),_N_E=e.O()}]);