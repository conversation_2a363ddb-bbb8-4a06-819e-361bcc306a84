(()=>{var e={};e.id=9479,e.ids=[9479],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},17535:(e,t,r)=>{Promise.resolve().then(r.bind(r,72757))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{Cn:()=>c,db:()=>d,j2:()=>l});var s=r(67989),i=r(63385),o=r(75535),n=r(70146),a=r(24791);let u=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,i.xI)(u),d=(0,o.aU)(u);(0,n.c7)(u);let c=(0,a.Uz)(u,"us-central1")},33873:e=>{"use strict";e.exports=require("path")},34340:(e,t,r)=>{Promise.resolve().then(r.bind(r,57871))},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},45479:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>l});var s=r(65239),i=r(48088),o=r(88170),n=r.n(o),a=r(30893),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let l={children:["",{children:["test-firebase",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72757)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-firebase\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-firebase\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-firebase/page",pathname:"/test-firebase",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57871:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),o=r(33784),n=r(63385),a=r(75535);function u(){let[e,t]=(0,i.useState)(""),[r,u]=(0,i.useState)(!1),[l,d]=(0,i.useState)(""),[c,p]=(0,i.useState)(!1),x=async()=>{u(!0),t("Testing Firebase connection...\n");try{t(e=>e+"Firebase config loaded ✓\n"),t(e=>e+`Project ID: mytube-india
`);let e=`test${Date.now()}@example.com`;t(e=>e+"Creating test user...\n");let r=(await (0,n.eJ)(o.j2,e,"test123456")).user;t(e=>e+`User created with UID: ${r.uid} ✓
`),t(e=>e+`User email: ${r.email}
`),t(e=>e+"Testing Firestore write...\n");let s={name:"Test User",email:e,mobile:"1234567890",referralCode:"TEST123",referredBy:null,plan:"trial",planExpiry:null,activeDays:2,joinedDate:new Date,wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null,createdAt:new Date,test:!0};t(e=>e+`Writing to collection: users, document: ${r.uid}
`),await (0,a.BN)((0,a.H9)(o.db,"users",r.uid),s),t(e=>e+"Firestore write successful ✓\n"),t(e=>e+"Testing Firestore read...\n");let i=await (0,a.x7)((0,a.H9)(o.db,"users",r.uid));if(i.exists()){t(e=>e+"Firestore read successful ✓\n");let e=i.data();t(e=>e+`Document exists: ${i.exists()}
`),t(t=>t+`Data keys: ${Object.keys(e).join(", ")}
`),t(t=>t+`Name: ${e.name}
`),t(t=>t+`Email: ${e.email}
`),t(t=>t+`Wallet: ${e.wallet}
`)}else t(e=>e+"Document not found ✗\n");t(e=>e+"Testing transaction creation...\n");let u={userId:r.uid,type:"test",amount:10,description:"Test transaction",date:new Date,status:"completed"},l=(0,a.H9)(o.db,"transactions",`test_${r.uid}_${Date.now()}`);await (0,a.BN)(l,u),t(e=>e+"Transaction creation successful ✓\n"),t(e=>e+"Cleaning up test data...\n"),await r.delete(),t(e=>e+"Test user deleted ✓\n"),t(e=>e+"\n\uD83C\uDF89 All tests passed! Firebase is working correctly.")}catch(e){console.error("Firebase test error:",e),t(t=>t+`
❌ Error: ${e.message}
`),t(t=>t+`Error code: ${e.code}
`),e.code&&t(t=>t+`Error details: ${e.code}
`),t(t=>t+`Stack trace: ${e.stack}
`)}finally{u(!1)}},m=async()=>{p(!0),d("Testing registration flow...\n");try{let e=`regtest${Date.now()}@example.com`;d(t=>t+`Creating user with email: ${e}
`);let t=(await (0,n.eJ)(o.j2,e,"test123456")).user;d(e=>e+`✓ User account created: ${t.uid}
`);let r={name:"Test Registration User",email:e,mobile:"**********",referralCode:`TEST${Math.random().toString(36).substr(2,4).toUpperCase()}`,referredBy:null,plan:"trial",planExpiry:null,activeDays:2,joinedDate:new Date,wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null};d(e=>e+"Creating user document in Firestore...\n"),await (0,a.BN)((0,a.H9)(o.db,"users",t.uid),r),d(e=>e+"✓ User document created successfully\n"),d(e=>e+"Verifying document creation...\n");let s=await (0,a.x7)((0,a.H9)(o.db,"users",t.uid));if(s.exists()){let e=s.data();d(e=>e+"✓ Document verification successful\n"),d(t=>t+`  Name: ${e.name}
`),d(t=>t+`  Email: ${e.email}
`),d(t=>t+`  Wallet: ${e.wallet}
`),d(t=>t+`  Plan: ${e.plan}
`)}else d(e=>e+"✗ Document not found after creation\n");d(e=>e+"Cleaning up...\n"),await t.delete(),d(e=>e+"✓ Test user deleted\n"),d(e=>e+"\n\uD83C\uDF89 Registration flow test completed successfully!")}catch(e){console.error("Registration test error:",e),d(t=>t+`
❌ Registration Error: ${e.message}
`),d(t=>t+`Error code: ${e.code}
`),e.stack&&d(t=>t+`Stack: ${e.stack}
`)}finally{p(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Firebase Connection Test"}),(0,s.jsxs)("div",{className:"flex gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:x,disabled:r,className:"btn-primary",children:r?"Testing...":"Test Firebase Connection"}),(0,s.jsx)("button",{onClick:m,disabled:c,className:"btn-secondary",children:c?"Testing...":"Test Registration Flow"})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Test Results:"}),(0,s.jsx)("pre",{className:"text-white/80 whitespace-pre-wrap font-mono text-sm",children:e||"Click the button above to test Firebase connection"})]})]})})}},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72757:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-firebase\\page.tsx","default")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,6958,8441],()=>r(45479));module.exports=s})();