import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  addDoc,
  Timestamp,
  increment,
  getCountFromServer,
  runTransaction,
  writeBatch
} from 'firebase/firestore'
import { db } from './firebase'

// Field names for Firestore collections
export const FIELD_NAMES = {
  // User fields
  name: 'name',
  email: 'email',
  mobile: 'mobile',
  referralCode: 'referralCode',
  referredBy: 'referredBy',
  referralBonusCredited: 'referralBonusCredited', // Track if referral bonus has been credited
  plan: 'plan',
  planExpiry: 'planExpiry',
  activeDays: 'activeDays',
  joinedDate: 'joinedDate',

  // Wallet fields
  wallet: 'wallet', // Single wallet instead of multiple

  // Bank details fields
  bankAccountHolderName: 'bankAccountHolderName',
  bankAccountNumber: 'bankAccountNumber',
  bankIfscCode: 'bankIfscCode',
  bankName: 'bankName',
  bankDetailsUpdated: 'bankDetailsUpdated',

  // Video fields
  totalVideos: 'totalVideos',
  todayVideos: 'todayVideos',
  lastVideoDate: 'lastVideoDate',
  videoDuration: 'videoDuration', // Duration in seconds (1, 10, 30 for quick duration OR 60-420 for 1-7 minutes)

  // Quick Video Advantage fields
  quickVideoAdvantage: 'quickVideoAdvantage', // Boolean: whether user has quick video advantage
  quickVideoAdvantageExpiry: 'quickVideoAdvantageExpiry', // Date: when the advantage expires (legacy)
  quickVideoAdvantageDays: 'quickVideoAdvantageDays', // Number: total days granted
  quickVideoAdvantageRemainingDays: 'quickVideoAdvantageRemainingDays', // Number: remaining days (decrements daily)
  quickVideoAdvantageSeconds: 'quickVideoAdvantageSeconds', // Number: video duration in seconds during advantage
  quickVideoAdvantageGrantedBy: 'quickVideoAdvantageGrantedBy', // String: admin who granted it
  quickVideoAdvantageGrantedAt: 'quickVideoAdvantageGrantedAt', // Date: when it was granted

  // Admin control fields
  manuallySetActiveDays: 'manuallySetActiveDays', // Boolean: whether active days were manually set by admin
  lastActiveDaysUpdate: 'lastActiveDaysUpdate', // Date: last time active days were updated

  // Transaction fields
  type: 'type',
  amount: 'amount',
  date: 'date',
  status: 'status',
  description: 'description',
  userId: 'userId'
}

// Collection names
export const COLLECTIONS = {
  users: 'users',
  transactions: 'transactions',
  withdrawals: 'withdrawals',
  plans: 'plans',
  settings: 'settings',
  notifications: 'notifications',
  adminLeaves: 'adminLeaves',
  userLeaves: 'userLeaves'
}

// Get user data
export async function getUserData(userId: string) {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getUserData:', userId)
      return null
    }

    const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
    if (userDoc.exists()) {
      const data = userDoc.data()

      // Ensure all values are properly typed
      const result = {
        name: String(data[FIELD_NAMES.name] || ''),
        email: String(data[FIELD_NAMES.email] || ''),
        mobile: String(data[FIELD_NAMES.mobile] || ''),
        referralCode: String(data[FIELD_NAMES.referralCode] || ''),
        referredBy: String(data[FIELD_NAMES.referredBy] || ''),
        plan: String(data[FIELD_NAMES.plan] || 'Trial'),
        planExpiry: data[FIELD_NAMES.planExpiry]?.toDate() || null,
        activeDays: Number(data[FIELD_NAMES.activeDays] || 0),
        joinedDate: data[FIELD_NAMES.joinedDate]?.toDate() || new Date(),
        videoDuration: Number(data[FIELD_NAMES.videoDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)), // Default 30 seconds for trial, 5 minutes for others

        // Quick Video Advantage fields
        quickVideoAdvantage: Boolean(data[FIELD_NAMES.quickVideoAdvantage] || false),
        quickVideoAdvantageExpiry: data[FIELD_NAMES.quickVideoAdvantageExpiry]?.toDate() || null,
        quickVideoAdvantageDays: Number(data[FIELD_NAMES.quickVideoAdvantageDays] || 0),
        quickVideoAdvantageRemainingDays: Number(data[FIELD_NAMES.quickVideoAdvantageRemainingDays] || 0),
        quickVideoAdvantageSeconds: Number(data[FIELD_NAMES.quickVideoAdvantageSeconds] || 30),
        quickVideoAdvantageGrantedBy: String(data[FIELD_NAMES.quickVideoAdvantageGrantedBy] || ''),
        quickVideoAdvantageGrantedAt: data[FIELD_NAMES.quickVideoAdvantageGrantedAt]?.toDate() || null
      }

      console.log('getUserData result:', result)
      return result
    }
    return null
  } catch (error) {
    console.error('Error getting user data:', error)
    return null // Return null instead of throwing to prevent crashes
  }
}

// Get wallet data
export async function getWalletData(userId: string) {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getWalletData:', userId)
      return { wallet: 0 }
    }

    const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
    if (userDoc.exists()) {
      const data = userDoc.data()
      const result = {
        wallet: Number(data[FIELD_NAMES.wallet] || 0)
      }

      console.log('getWalletData result:', result)
      return result
    }
    return { wallet: 0 }
  } catch (error) {
    console.error('Error getting wallet data:', error)
    return { wallet: 0 } // Return default instead of throwing
  }
}

// Get video count data
export async function getVideoCountData(userId: string) {
  try {
    const userRef = doc(db, COLLECTIONS.users, userId)
    const userDoc = await getDoc(userRef)
    if (userDoc.exists()) {
      const data = userDoc.data()
      const totalVideos = data[FIELD_NAMES.totalVideos] || 0
      let todayVideos = data[FIELD_NAMES.todayVideos] || 0
      const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate()

      // Check if it's a new day
      const today = new Date()
      const isNewDay = !lastVideoDate ||
        lastVideoDate.toDateString() !== today.toDateString()

      // If it's a new day, reset todayVideos and update active days
      if (isNewDay && todayVideos > 0) {
        console.log(`🔄 Resetting daily video count for user ${userId} (was ${todayVideos})`)
        await updateDoc(userRef, {
          [FIELD_NAMES.todayVideos]: 0
        })
        todayVideos = 0

        // Also update active days for accurate tracking
        try {
          await updateUserActiveDays(userId)
        } catch (error) {
          console.error('Error updating active days during daily reset:', error)
        }

        // Trigger centralized daily process check (runs regardless of user login)
        try {
          await checkAndRunDailyProcess()
        } catch (error) {
          console.error('Error in daily process check:', error)
        }
      }

      return {
        totalVideos,
        todayVideos,
        remainingVideos: Math.max(0, 50 - todayVideos)
      }
    }
    return { totalVideos: 0, todayVideos: 0, remainingVideos: 50 }
  } catch (error) {
    console.error('Error getting video count data:', error)
    throw error
  }
}

// Update user data
export async function updateUserData(userId: string, data: any) {
  try {
    await updateDoc(doc(db, COLLECTIONS.users, userId), data)
  } catch (error) {
    console.error('Error updating user data:', error)
    throw error
  }
}

// Add transaction
export async function addTransaction(userId: string, transactionData: {
  type: string
  amount: number
  description: string
  status?: string
}) {
  try {
    const transaction = {
      [FIELD_NAMES.userId]: userId,
      [FIELD_NAMES.type]: transactionData.type,
      [FIELD_NAMES.amount]: transactionData.amount,
      [FIELD_NAMES.description]: transactionData.description,
      [FIELD_NAMES.status]: transactionData.status || 'completed',
      [FIELD_NAMES.date]: Timestamp.now()
    }
    
    await addDoc(collection(db, COLLECTIONS.transactions), transaction)
  } catch (error) {
    console.error('Error adding transaction:', error)
    throw error
  }
}

// Get transactions
export async function getTransactions(userId: string, limitCount = 10) {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getTransactions:', userId)
      return []
    }

    // Temporary fix: Use only where clause without orderBy to avoid index requirement
    // TODO: Create composite index in Firebase console for better performance
    const q = query(
      collection(db, COLLECTIONS.transactions),
      where(FIELD_NAMES.userId, '==', userId),
      limit(limitCount)
    )

    const querySnapshot = await getDocs(q)
    const transactions = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data()[FIELD_NAMES.date]?.toDate()
    }))

    // Sort in memory since we can't use orderBy without index
    transactions.sort((a, b) => {
      const dateA = a.date || new Date(0)
      const dateB = b.date || new Date(0)
      return dateB.getTime() - dateA.getTime() // Descending order
    })

    return transactions
  } catch (error) {
    console.error('Error getting transactions:', error)
    return [] // Return empty array instead of throwing to prevent crashes
  }
}

// Get referrals
export async function getReferrals(referralCode: string) {
  try {
    const q = query(
      collection(db, COLLECTIONS.users),
      where(FIELD_NAMES.referredBy, '==', referralCode)
    )
    
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate()
    }))
  } catch (error) {
    console.error('Error getting referrals:', error)
    throw error
  }
}

// Update video count (single video)
export async function updateVideoCount(userId: string) {
  try {
    const today = new Date()
    const userRef = doc(db, COLLECTIONS.users, userId)

    // Get current data to check if we need to reset daily count
    const userDoc = await getDoc(userRef)
    if (userDoc.exists()) {
      const data = userDoc.data()
      const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate()
      const currentTodayVideos = data[FIELD_NAMES.todayVideos] || 0

      // Check if it's a new day
      const isNewDay = !lastVideoDate ||
        lastVideoDate.toDateString() !== today.toDateString()

      if (isNewDay && currentTodayVideos > 0) {
        // Reset today's count and then increment
        console.log(`🔄 Resetting and updating daily video count for user ${userId}`)
        await updateDoc(userRef, {
          [FIELD_NAMES.totalVideos]: increment(1),
          [FIELD_NAMES.todayVideos]: 1, // Reset to 1 (this video)
          [FIELD_NAMES.lastVideoDate]: Timestamp.fromDate(today)
        })
      } else {
        // Normal increment
        await updateDoc(userRef, {
          [FIELD_NAMES.totalVideos]: increment(1),
          [FIELD_NAMES.todayVideos]: increment(1),
          [FIELD_NAMES.lastVideoDate]: Timestamp.fromDate(today)
        })
      }
    } else {
      // User doesn't exist, create with initial values
      await updateDoc(userRef, {
        [FIELD_NAMES.totalVideos]: increment(1),
        [FIELD_NAMES.todayVideos]: increment(1),
        [FIELD_NAMES.lastVideoDate]: Timestamp.fromDate(today)
      })
    }
  } catch (error) {
    console.error('Error updating video count:', error)
    throw error
  }
}

// Batch video submission (for submitting exactly 50 videos at once)
export async function submitBatchVideos(userId: string, videoCount: number = 50) {
  try {
    if (videoCount !== 50) {
      throw new Error(`Invalid batch size: ${videoCount}. Expected exactly 50 videos.`)
    }

    const today = new Date()
    const userRef = doc(db, COLLECTIONS.users, userId)

    // Get current data to validate submission
    const userDoc = await getDoc(userRef)
    if (!userDoc.exists()) {
      throw new Error('User not found')
    }

    const data = userDoc.data()
    const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate()
    const currentTodayVideos = data[FIELD_NAMES.todayVideos] || 0
    const currentTotalVideos = data[FIELD_NAMES.totalVideos] || 0

    // Check if user already submitted today
    if (currentTodayVideos >= 50) {
      throw new Error('Daily video limit already reached. Cannot submit more videos today.')
    }

    // Check if it's a new day
    const isNewDay = !lastVideoDate ||
      lastVideoDate.toDateString() !== today.toDateString()

    let newTodayVideos: number
    let newTotalVideos: number

    if (isNewDay && currentTodayVideos > 0) {
      // Reset today's count and set to 50
      console.log(`🔄 Resetting daily count and submitting batch for user ${userId}`)
      newTodayVideos = 50
      newTotalVideos = currentTotalVideos + 50
    } else {
      // Add to existing count (should be 0 + 50 = 50 for first submission)
      newTodayVideos = Math.min(currentTodayVideos + 50, 50) // Cap at 50
      newTotalVideos = currentTotalVideos + 50
    }

    // Atomic update - all fields updated together
    await updateDoc(userRef, {
      [FIELD_NAMES.totalVideos]: newTotalVideos,
      [FIELD_NAMES.todayVideos]: newTodayVideos,
      [FIELD_NAMES.lastVideoDate]: Timestamp.fromDate(today)
    })

    console.log(`✅ Batch submission successful for user ${userId}: +50 videos (Total: ${newTotalVideos}, Today: ${newTodayVideos})`)

    return {
      totalVideos: newTotalVideos,
      todayVideos: newTodayVideos,
      videosAdded: 50
    }
  } catch (error) {
    console.error('Error submitting batch videos:', error)
    throw error
  }
}

// Reset daily video count for a user (admin function)
export async function resetDailyVideoCount(userId: string) {
  try {
    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.todayVideos]: 0
    })
    console.log(`✅ Reset daily video count for user ${userId}`)
  } catch (error) {
    console.error('Error resetting daily video count:', error)
    throw error
  }
}

// Get live active days for user display (always returns current value from database)
export async function getLiveActiveDays(userId: string): Promise<number> {
  try {
    console.log(`🔍 Getting live active days for user ${userId}`)

    // Get the current active days from database (this is updated by daily process)
    const userData = await getUserData(userId)
    if (!userData) {
      console.error('User data not found for live active days:', userId)
      return 1
    }

    const activeDays = userData.activeDays || 1
    console.log(`📊 Live active days for user ${userId}: ${activeDays}`)
    return activeDays
  } catch (error) {
    console.error('Error getting live active days:', error)
    return 1
  }
}

// Centralized Active Days Calculation (simplified and reliable)
export async function calculateUserActiveDays(userId: string): Promise<number> {
  try {
    const userData = await getUserData(userId)
    if (!userData) {
      console.error('User data not found for active days calculation:', userId)
      return 1
    }

    const today = new Date()
    let activeDays = 1 // Always start with 1

    if (userData.plan === 'Trial') {
      // For trial users, calculate based on joined date (start from 1)
      let joinedDate = userData.joinedDate

      // Ensure joinedDate is a proper Date object
      if (!joinedDate) {
        console.warn(`User ${userId} has no joinedDate, using current date`)
        joinedDate = new Date()
      } else if (typeof joinedDate.toDate === 'function') {
        // It's a Firestore Timestamp
        joinedDate = joinedDate.toDate()
      } else if (!(joinedDate instanceof Date)) {
        // Try to convert to Date
        joinedDate = new Date(joinedDate)
        if (isNaN(joinedDate.getTime())) {
          console.warn(`User ${userId} has invalid joinedDate, using current date`)
          joinedDate = new Date()
        }
      }

      const daysDifference = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24))
      activeDays = Math.max(1, daysDifference + 1) // Day 0 = 1 active day, Day 1 = 2 active days, etc.

      console.log(`📅 Trial user ${userId} active days calculation:`, {
        joinedDate: joinedDate.toISOString(),
        today: today.toISOString(),
        daysDifference,
        activeDays
      })
    } else {
      // For paid plans, calculate from plan activation date excluding leave days
      let planActivationDate: Date

      if (userData.planExpiry) {
        // Handle Firestore Timestamp for planExpiry
        let planExpiryDate: Date
        if (typeof userData.planExpiry.toDate === 'function') {
          planExpiryDate = userData.planExpiry.toDate()
        } else {
          planExpiryDate = new Date(userData.planExpiry)
        }

        // Calculate plan activation date by subtracting plan validity days from expiry
        planActivationDate = new Date(planExpiryDate.getTime() - (getPlanValidityDays(userData.plan) * 24 * 60 * 60 * 1000))
      } else {
        // CRITICAL FIX: For paid plans without planExpiry, use stored activeDays value
        // This prevents users from working indefinitely when planExpiry is null
        console.log(`⚠️ Paid plan user ${userId} has no planExpiry - using stored activeDays: ${userData.activeDays || 1}`)
        return userData.activeDays || 1
      }

      const daysSincePlanActivated = Math.floor(
        (today.getTime() - planActivationDate.getTime()) / (1000 * 60 * 60 * 24)
      )

      // Get leave days count
      const { isAdminLeaveDay, isUserOnLeave } = await import('./leaveService')
      let totalLeaveDays = 0

      // Count admin leave days since plan activation
      for (let i = 0; i <= daysSincePlanActivated; i++) {
        const checkDate = new Date(planActivationDate.getTime() + (i * 24 * 60 * 60 * 1000))
        const isAdminLeave = await isAdminLeaveDay(checkDate)
        const isUserLeave = await isUserOnLeave(userId, checkDate)

        if (isAdminLeave || isUserLeave) {
          totalLeaveDays++
        }
      }

      // Calculate active days: Days since plan activated - leave days + 1 (for activation day)
      activeDays = Math.max(1, daysSincePlanActivated - totalLeaveDays + 1)
    }

    return activeDays
  } catch (error) {
    console.error('Error calculating user active days:', error)
    return 1 // Return 1 on error to ensure minimum value
  }
}

// Update user's active days (respects manual admin settings)
export async function updateUserActiveDays(userId: string, forceUpdate = false) {
  try {
    const userData = await getUserData(userId)
    if (!userData) {
      console.error('User data not found for active days update:', userId)
      return 1
    }

    // Check if active days were manually set by admin (skip auto-calculation unless forced)
    const userRef = doc(db, COLLECTIONS.users, userId)
    const userDoc = await getDoc(userRef)
    const userDocData = userDoc.data()
    const manuallySetActiveDays = userDocData?.manuallySetActiveDays || false

    if (manuallySetActiveDays && !forceUpdate) {
      console.log(`⏭️ Skipping active days auto-update for user ${userId} - manually set by admin (current: ${userData.activeDays})`)
      return userData.activeDays || 1
    }

    let newActiveDays: number
    if (manuallySetActiveDays && forceUpdate) {
      // Even when forced, if manually set, don't recalculate - keep current value
      console.log(`⚠️ Force update requested but active days manually set for user ${userId} - keeping current value`)
      newActiveDays = userData.activeDays || 1
    } else {
      // Calculate correct active days using centralized function only for auto-calculated users
      newActiveDays = await calculateUserActiveDays(userId)
    }

    // Only update if the value has changed
    const currentActiveDays = userData.activeDays || 0
    if (newActiveDays !== currentActiveDays) {
      console.log(`📅 Updating active days for user ${userId}: ${currentActiveDays} → ${newActiveDays}`)
      await updateDoc(userRef, {
        [FIELD_NAMES.activeDays]: newActiveDays
      })
    }

    return newActiveDays
  } catch (error) {
    console.error('Error updating user active days:', error)
    throw error
  }
}

// Centralized daily process checker (runs regardless of user login)
export async function checkAndRunDailyProcess() {
  try {
    const today = new Date().toDateString()

    // Check Firestore for reliable cross-user tracking
    const globalResetDoc = await getDoc(doc(db, 'system', 'dailyReset'))
    const lastProcessDate = globalResetDoc.exists() ? globalResetDoc.data()?.lastResetDate : null

    if (!lastProcessDate || lastProcessDate !== today) {
      // Calculate how many days we missed
      let daysMissed = 1
      if (lastProcessDate) {
        const lastDate = new Date(lastProcessDate)
        const timeDiff = new Date().getTime() - lastDate.getTime()
        daysMissed = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
      }

      console.log(`🌅 Running daily process for all users (${daysMissed} day(s) catchup)...`)

      // Run the daily process (this handles the +1 increment for all users)
      const result = await dailyActiveDaysIncrement()

      // Update Firestore tracking
      try {
        const globalResetDocRef = doc(db, 'system', 'dailyReset')
        await setDoc(globalResetDocRef, {
          lastResetDate: today,
          lastResetTimestamp: Timestamp.now(),
          lastResult: result,
          daysCaughtUp: daysMissed,
          triggeredBy: 'automatic_daily_check'
        }, { merge: true })

        console.log(`✅ Daily process completed for ${daysMissed} day(s):`, result)
      } catch (trackingError) {
        console.error('Error updating daily process tracking:', trackingError)
      }

      return result
    } else {
      console.log('⏭️ Daily process already completed today')
      return null
    }
  } catch (error) {
    console.error('Error in centralized daily process:', error)
    throw error
  }
}

// Daily active days increment for all users (runs automatically every day)
export async function dailyActiveDaysIncrement() {
  try {
    console.log('🌅 Starting daily active days increment...')

    const today = new Date()
    const todayDateString = today.toDateString()

    // Check if today is an admin leave day
    const { isAdminLeaveDay } = await import('./leaveService')
    const isAdminLeave = await isAdminLeaveDay(today)

    if (isAdminLeave) {
      console.log('⏸️ Skipping active days increment - Admin leave day')
      return { incrementedCount: 0, skippedCount: 0, errorCount: 0, reason: 'Admin leave day' }
    }

    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    let incrementedCount = 0
    let skippedCount = 0
    let errorCount = 0

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userData = userDoc.data()
        const userId = userDoc.id

        // Check if we already updated today
        const lastUpdate = userData[FIELD_NAMES.lastActiveDaysUpdate]?.toDate()
        if (lastUpdate && lastUpdate.toDateString() === todayDateString) {
          console.log(`⏭️ Skipping user ${userId} - Already updated today (${lastUpdate.toDateString()})`)
          skippedCount++
          continue
        }

        // Check if user is on leave today
        const { isUserOnLeave } = await import('./leaveService')
        const isUserLeave = await isUserOnLeave(userId, today)

        if (isUserLeave) {
          console.log(`⏸️ Skipping active days increment for user ${userId} - User leave day`)
          // Still update the last update date to mark that we processed this user today
          await updateDoc(doc(db, COLLECTIONS.users, userId), {
            [FIELD_NAMES.lastActiveDaysUpdate]: Timestamp.fromDate(today)
          })
          skippedCount++
          continue
        }

        // Simple daily increment: +1 for all users (regardless of manual/auto setting)
        const currentActiveDays = userData[FIELD_NAMES.activeDays] || 1
        const newActiveDays = currentActiveDays + 1
        console.log(`📅 Daily active days increment for user ${userId}: ${currentActiveDays} → ${newActiveDays}`)

        // Prepare update data
        const updateData: any = {
          [FIELD_NAMES.activeDays]: newActiveDays,
          [FIELD_NAMES.lastActiveDaysUpdate]: Timestamp.fromDate(today)
        }

        // Handle quick video advantage decrement
        const currentQuickAdvantage = userData[FIELD_NAMES.quickVideoAdvantage] || false
        const currentRemainingDays = userData[FIELD_NAMES.quickVideoAdvantageRemainingDays] || 0

        if (currentQuickAdvantage && currentRemainingDays > 0) {
          const newRemainingDays = currentRemainingDays - 1
          updateData[FIELD_NAMES.quickVideoAdvantageRemainingDays] = newRemainingDays

          // If remaining days reach 0, disable quick video advantage
          if (newRemainingDays <= 0) {
            updateData[FIELD_NAMES.quickVideoAdvantage] = false
            updateData[FIELD_NAMES.quickVideoAdvantageExpiry] = null
            console.log(`⏰ Quick video advantage expired for user ${userId}`)

            // Add transaction record for expiry
            try {
              await addTransaction(userId, {
                type: 'quick_advantage_expired',
                amount: 0,
                description: 'Quick video advantage expired (time limit reached)'
              })
            } catch (transactionError) {
              console.error('Error adding expiry transaction:', transactionError)
            }
          } else {
            console.log(`⏰ Quick video advantage for user ${userId}: ${currentRemainingDays} → ${newRemainingDays} days remaining`)
          }
        }

        await updateDoc(doc(db, COLLECTIONS.users, userId), updateData)

        incrementedCount++
        console.log(`📅 Updated active days for user ${userId}: ${currentActiveDays} → ${newActiveDays}`)

      } catch (error) {
        console.error(`Error updating active days for user ${userDoc.id}:`, error)
        errorCount++
      }
    }

    console.log(`✅ Daily active days increment completed: ${incrementedCount} incremented, ${skippedCount} skipped, ${errorCount} errors`)
    return { incrementedCount, skippedCount, errorCount }
  } catch (error) {
    console.error('Error in daily active days increment:', error)
    throw error
  }
}

// Migrate quick video advantage from expiry date to remaining days system
export async function migrateQuickVideoAdvantageSystem() {
  try {
    console.log('🔄 Starting quick video advantage system migration...')
    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    let migratedCount = 0
    let skippedCount = 0
    let errorCount = 0

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userData = userDoc.data()
        const userId = userDoc.id

        // Skip if user doesn't have quick video advantage
        if (!userData[FIELD_NAMES.quickVideoAdvantage]) {
          skippedCount++
          continue
        }

        // Skip if already migrated (has remainingDays field)
        if (userData[FIELD_NAMES.quickVideoAdvantageRemainingDays] !== undefined) {
          skippedCount++
          continue
        }

        // Calculate remaining days from expiry date
        let remainingDays = 0
        const expiry = userData[FIELD_NAMES.quickVideoAdvantageExpiry]?.toDate()

        if (expiry) {
          const now = new Date()
          const timeDiff = expiry.getTime() - now.getTime()
          remainingDays = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)))
        }

        // Update user with remaining days
        const updateData: any = {
          [FIELD_NAMES.quickVideoAdvantageRemainingDays]: remainingDays
        }

        // If expired, disable the advantage
        if (remainingDays <= 0) {
          updateData[FIELD_NAMES.quickVideoAdvantage] = false
          updateData[FIELD_NAMES.quickVideoAdvantageExpiry] = null
        }

        await updateDoc(doc(db, COLLECTIONS.users, userId), updateData)
        migratedCount++

        console.log(`✅ Migrated user ${userId}: ${remainingDays} days remaining`)

      } catch (error) {
        console.error(`Error migrating user ${userDoc.id}:`, error)
        errorCount++
      }
    }

    console.log(`✅ Quick video advantage migration completed: ${migratedCount} migrated, ${skippedCount} skipped, ${errorCount} errors`)
    return { migratedCount, skippedCount, errorCount }
  } catch (error) {
    console.error('Error migrating quick video advantage system:', error)
    throw error
  }
}

// Fix all users' active days (admin function)
export async function fixAllUsersActiveDays() {
  try {
    console.log('🔧 Starting to fix all users active days...')
    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    let fixedCount = 0
    let errorCount = 0

    for (const userDoc of usersSnapshot.docs) {
      try {
        await updateUserActiveDays(userDoc.id, true) // Force update
        fixedCount++
      } catch (error) {
        console.error(`Error fixing active days for user ${userDoc.id}:`, error)
        errorCount++
      }
    }

    console.log(`✅ Fixed active days for ${fixedCount} users, ${errorCount} errors`)
    return { fixedCount, errorCount }
  } catch (error) {
    console.error('Error fixing all users active days:', error)
    throw error
  }
}

// Recalculate active days for all users using centralized calculation (admin function)
export async function recalculateAllUsersActiveDays() {
  try {
    console.log('🔄 Starting to recalculate all users active days with centralized formula...')
    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    let recalculatedCount = 0
    let errorCount = 0

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userData = userDoc.data()
        const userId = userDoc.id

        // Use centralized calculation for correct active days
        const correctActiveDays = await calculateUserActiveDays(userId)

        // Update only if different from current value
        const currentActiveDays = userData.activeDays || 0
        if (correctActiveDays !== currentActiveDays) {
          await updateDoc(doc(db, COLLECTIONS.users, userId), {
            [FIELD_NAMES.activeDays]: correctActiveDays,
            [FIELD_NAMES.manuallySetActiveDays]: false // Reset manual flag
          })

          console.log(`📅 Recalculated active days for user ${userId}: ${currentActiveDays} → ${correctActiveDays}`)
          recalculatedCount++
        }

      } catch (error) {
        console.error(`Error recalculating active days for user ${userDoc.id}:`, error)
        errorCount++
      }
    }

    console.log(`✅ Recalculated active days for ${recalculatedCount} users, ${errorCount} errors`)
    return { recalculatedCount, errorCount }
  } catch (error) {
    console.error('Error recalculating all users active days:', error)
    throw error
  }
}

// Force daily process to run for all missed days (admin function)
export async function forceDailyProcessCatchup() {
  try {
    console.log('🚀 Starting forced daily process catchup for all users...')

    // Get the last process date from system
    const globalResetDoc = await getDoc(doc(db, 'system', 'dailyReset'))
    const lastProcessDate = globalResetDoc.exists() ? globalResetDoc.data()?.lastResetDate : null

    console.log('Last daily process date:', lastProcessDate)

    // Force run the daily process regardless of last run date
    const result = await dailyActiveDaysIncrement()

    // Update the system tracking to today
    const today = new Date().toDateString()
    const globalResetDocRef = doc(db, 'system', 'dailyReset')
    await setDoc(globalResetDocRef, {
      lastResetDate: today,
      lastResetTimestamp: Timestamp.now(),
      lastResult: result,
      forcedCatchup: true,
      forcedCatchupTimestamp: Timestamp.now()
    }, { merge: true })

    console.log('✅ Forced daily process catchup completed:', result)
    return result
  } catch (error) {
    console.error('Error in forced daily process catchup:', error)
    throw error
  }
}

// Reset daily increment tracking to allow re-running today (admin function)
export async function resetDailyIncrementTracking() {
  try {
    console.log('🔄 Resetting daily increment tracking...')

    // Reset system tracking
    const globalResetDocRef = doc(db, 'system', 'dailyReset')
    await setDoc(globalResetDocRef, {
      lastResetDate: null,
      lastResetTimestamp: null,
      resetBy: 'admin_manual_reset',
      resetTime: Timestamp.now()
    }, { merge: true })

    // Reset all users' lastActiveDaysUpdate field
    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    const batch = writeBatch(db)
    let resetCount = 0

    for (const userDoc of usersSnapshot.docs) {
      batch.update(userDoc.ref, {
        [FIELD_NAMES.lastActiveDaysUpdate]: null
      })
      resetCount++
    }

    await batch.commit()

    console.log(`✅ Reset daily increment tracking for ${resetCount} users`)
    return { success: true, resetCount }

  } catch (error) {
    console.error('Error resetting daily increment tracking:', error)
    throw error
  }
}

// Reset all users' lastActiveDaysUpdate to force fresh daily increment (admin function)
export async function resetAllUsersLastUpdate() {
  try {
    console.log('🔄 Resetting all users lastActiveDaysUpdate field...')
    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    let resetCount = 0
    let errorCount = 0

    for (const userDoc of usersSnapshot.docs) {
      try {
        const userId = userDoc.id

        // Reset the lastActiveDaysUpdate field
        await updateDoc(doc(db, COLLECTIONS.users, userId), {
          [FIELD_NAMES.lastActiveDaysUpdate]: null
        })

        resetCount++
        console.log(`✅ Reset lastActiveDaysUpdate for user ${userId}`)

      } catch (error) {
        console.error(`Error resetting lastActiveDaysUpdate for user ${userDoc.id}:`, error)
        errorCount++
      }
    }

    console.log(`✅ Reset lastActiveDaysUpdate for ${resetCount} users, ${errorCount} errors`)
    return { resetCount, errorCount }
  } catch (error) {
    console.error('Error resetting all users lastActiveDaysUpdate:', error)
    throw error
  }
}

// Update wallet balance
export async function updateWalletBalance(userId: string, amount: number) {
  try {
    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.wallet]: increment(amount)
    })
  } catch (error) {
    console.error('Error updating wallet balance:', error)
    throw error
  }
}

// Bank details interface
export interface BankDetails {
  accountHolderName: string
  accountNumber: string
  ifscCode: string
  bankName: string
}

// Save bank details
export async function saveBankDetails(userId: string, bankDetails: BankDetails) {
  try {
    if (!userId || typeof userId !== 'string') {
      throw new Error('Invalid userId provided')
    }

    // Validate bank details
    validateBankDetails(bankDetails)

    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),
      [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),
      [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),
      [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),
      [FIELD_NAMES.bankDetailsUpdated]: Timestamp.now()
    })

    console.log('Bank details saved successfully for user:', userId)
  } catch (error) {
    console.error('Error saving bank details:', error)
    throw error
  }
}

// Get bank details
export async function getBankDetails(userId: string): Promise<BankDetails | null> {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getBankDetails:', userId)
      return null
    }

    const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
    if (userDoc.exists()) {
      const data = userDoc.data()

      // Check if bank details exist
      if (data[FIELD_NAMES.bankAccountNumber]) {
        const result: BankDetails = {
          accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),
          accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),
          ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),
          bankName: String(data[FIELD_NAMES.bankName] || '')
        }

        console.log('getBankDetails result found')
        return result
      }
    }

    console.log('No bank details found for user')
    return null
  } catch (error) {
    console.error('Error getting bank details:', error)
    return null
  }
}

// Get plan-based earning amount (per batch of 50 videos)
export function getPlanEarning(plan: string): number {
  const planEarnings: { [key: string]: number } = {
    'Trial': 10,
    'Starter': 25,
    'Basic': 75,
    'Premium': 150,
    'Gold': 200,
    'Platinum': 250,
    'Diamond': 400
  }

  return planEarnings[plan] || 10 // Default to trial earning (per batch of 50 videos)
}

// Get plan-based video duration (in seconds)
export function getPlanVideoDuration(plan: string): number {
  const planDurations: { [key: string]: number } = {
    'Trial': 30,      // 30 seconds
    'Starter': 300,   // 5 minutes (Rs 499 plan)
    'Basic': 300,     // 5 minutes (Rs 1499 plan)
    'Premium': 300,   // 5 minutes (Rs 2999 plan)
    'Gold': 180,      // 3 minutes (Rs 3999 plan)
    'Platinum': 120,  // 2 minutes (Rs 5999 plan)
    'Diamond': 60     // 1 minute (Rs 9999 plan)
  }

  return planDurations[plan] || 30 // Default to trial duration (30 seconds)
}

// Get plan validity duration in days
export function getPlanValidityDays(plan: string): number {
  const planValidityDays: { [key: string]: number } = {
    'Trial': 2,       // 2 days
    'Starter': 30,    // 30 days (Rs 499 plan)
    'Basic': 30,      // 30 days (Rs 1499 plan)
    'Premium': 30,    // 30 days (Rs 2999 plan)
    'Gold': 30,       // 30 days (Rs 3999 plan)
    'Platinum': 30,   // 30 days (Rs 5999 plan)
    'Diamond': 30,    // 30 days (Rs 9999 plan)
    '499': 30,        // Legacy plan mapping
    '1499': 30,       // Legacy plan mapping
    '2999': 30,       // Legacy plan mapping
    '3999': 30,       // Legacy plan mapping
    '5999': 30,       // Legacy plan mapping
    '9999': 30        // Legacy plan mapping
  }

  return planValidityDays[plan] || 2 // Default to trial duration (2 days)
}

// Check if user's plan is expired based on active days and plan validity
export async function isUserPlanExpired(userId: string): Promise<{ expired: boolean; reason?: string; daysLeft?: number; activeDays?: number }> {
  try {
    const userData = await getUserData(userId)
    if (!userData) {
      return { expired: true, reason: 'User data not found' }
    }

    // Use live active days from database (updated by daily process)
    const activeDays = await getLiveActiveDays(userId)

    // If user is on Trial plan, check expiry based on active days
    if (userData.plan === 'Trial') {
      // Trial expires on 3rd day and above (activeDays >= 3)
      // Day 1: activeDays=1, can work
      // Day 2: activeDays=2, can work
      // Day 3: activeDays=3, expired
      const trialDaysLeft = Math.max(0, 3 - activeDays)

      return {
        expired: activeDays >= 3, // Expires on 3rd day and above (activeDays >= 3)
        reason: activeDays >= 3 ? 'Trial period expired' : undefined,
        daysLeft: trialDaysLeft,
        activeDays: activeDays
      }
    }

    // For paid plans, check if planExpiry is set
    if (userData.planExpiry) {
      const today = new Date()
      const expired = today > userData.planExpiry
      const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

      return {
        expired,
        reason: expired ? 'Plan subscription expired' : undefined,
        daysLeft,
        activeDays: activeDays
      }
    }

    // If planExpiry is not set, calculate based on active days and plan validity
    // Other plans expire from active days 31 and above (activeDays >= 31)
    // Day 1: activeDays=1, can work
    // Day 30: activeDays=30, can work
    // Day 31: activeDays=31, expired
    const daysLeft = Math.max(0, 31 - activeDays)
    const expired = activeDays >= 31 // Expires from active days 31 and above

    return {
      expired,
      reason: expired ? `Plan validity period exceeded - active days limit (31) reached` : undefined,
      daysLeft,
      activeDays: activeDays
    }
  } catch (error) {
    console.error('Error checking plan expiry:', error)
    return { expired: true, reason: 'Error checking plan status' }
  }
}

// Update user's plan expiry when admin changes plan
export async function updateUserPlanExpiry(userId: string, newPlan: string, customExpiryDate?: Date) {
  try {
    const userRef = doc(db, COLLECTIONS.users, userId)

    if (newPlan === 'Trial') {
      // Trial plan doesn't have expiry, it's based on joined date
      await updateDoc(userRef, {
        [FIELD_NAMES.planExpiry]: null
      })
    } else {
      // Set expiry date for paid plans
      let expiryDate: Date

      if (customExpiryDate) {
        expiryDate = customExpiryDate
      } else {
        // Calculate expiry based on plan validity
        const validityDays = getPlanValidityDays(newPlan)
        const today = new Date()
        expiryDate = new Date(today.getTime() + (validityDays * 24 * 60 * 60 * 1000))
      }

      await updateDoc(userRef, {
        [FIELD_NAMES.planExpiry]: Timestamp.fromDate(expiryDate)
      })

      console.log(`Updated plan expiry for user ${userId} to ${expiryDate.toDateString()}`)
    }
  } catch (error) {
    console.error('Error updating plan expiry:', error)
    throw error
  }
}

// Get referral bonus based on plan
export function getReferralBonus(plan: string): number {
  const referralBonuses: { [key: string]: number } = {
    'Trial': 0,     // Trial plan -> No bonus
    '499': 50,      // Rs 499 plan -> Rs 50 bonus
    '1499': 150,    // Rs 1499 plan -> Rs 150 bonus
    '2999': 300,    // Rs 2999 plan -> Rs 300 bonus
    '3999': 400,    // Rs 3999 plan -> Rs 400 bonus
    '5999': 700,    // Rs 5999 plan -> Rs 700 bonus
    '9999': 1200,   // Rs 9999 plan -> Rs 1200 bonus
    'Starter': 50,  // Legacy plan mapping
    'Basic': 150,   // Legacy plan mapping
    'Premium': 300, // Legacy plan mapping
    'Gold': 400,
    'Platinum': 700,
    'Diamond': 1200
  }

  return referralBonuses[plan] || 0
}

// Process referral bonus when admin upgrades user from Trial to paid plan
export async function processReferralBonus(userId: string, oldPlan: string, newPlan: string) {
  try {
    // Only process bonus when upgrading FROM Trial TO a paid plan
    if (oldPlan !== 'Trial' || newPlan === 'Trial') {
      console.log('Referral bonus only applies when upgrading from Trial to paid plan')
      return
    }

    console.log(`Processing referral bonus for user ${userId} upgrading from ${oldPlan} to ${newPlan}`)

    // Get the user's data to find their referral info
    const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
    if (!userDoc.exists()) {
      console.log('User not found')
      return
    }

    const userData = userDoc.data()
    const referredBy = userData[FIELD_NAMES.referredBy]
    const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited]

    if (!referredBy) {
      console.log('User was not referred by anyone, skipping bonus processing')
      return
    }

    if (alreadyCredited) {
      console.log('Referral bonus already credited for this user, skipping')
      return
    }

    console.log('Finding referrer with code:', referredBy)

    // Find the referrer by referral code
    const q = query(
      collection(db, COLLECTIONS.users),
      where(FIELD_NAMES.referralCode, '==', referredBy),
      limit(1)
    )

    const querySnapshot = await getDocs(q)

    if (querySnapshot.empty) {
      console.log('Referral code not found:', referredBy)
      return
    }

    const referrerDoc = querySnapshot.docs[0]
    const referrerId = referrerDoc.id
    const bonusAmount = getReferralBonus(newPlan)

    console.log(`Found referrer: ${referrerId}, bonus amount: ₹${bonusAmount}`)

    if (bonusAmount > 0) {
      // Add bonus to referrer's wallet
      await updateWalletBalance(referrerId, bonusAmount)

      // Add 50 videos to referrer's total video count
      const referrerRef = doc(db, COLLECTIONS.users, referrerId)
      await updateDoc(referrerRef, {
        [FIELD_NAMES.totalVideos]: increment(50)
      })

      // Mark referral bonus as credited for this user
      const userRef = doc(db, COLLECTIONS.users, userId)
      await updateDoc(userRef, {
        [FIELD_NAMES.referralBonusCredited]: true
      })

      // Add transaction record for referral bonus
      await addTransaction(referrerId, {
        type: 'referral_bonus',
        amount: bonusAmount,
        description: `Referral bonus for ${newPlan} plan upgrade + 50 bonus videos (User: ${userData[FIELD_NAMES.name]})`
      })

      console.log(`✅ Referral bonus processed: ₹${bonusAmount} + 50 videos for referrer ${referrerId}`)
    } else {
      console.log('No bonus amount calculated, skipping')
    }
  } catch (error) {
    console.error('❌ Error processing referral bonus:', error)
    // Don't throw error to avoid breaking plan update
  }
}

// Get user video settings (duration and earning per batch)
export async function getUserVideoSettings(userId: string) {
  try {
    const userData = await getUserData(userId)
    if (!userData) {
      return {
        videoDuration: 30, // Default 30 seconds for trial
        earningPerBatch: 10, // Default trial earning per batch of 50 videos
        plan: 'Trial',
        hasQuickAdvantage: false
      }
    }

    // Check if user has active quick video advantage
    const hasActiveQuickAdvantage = checkQuickVideoAdvantageActive(userData)

    let videoDuration = userData.videoDuration

    // If user has active quick video advantage, use custom seconds or default to 30
    if (hasActiveQuickAdvantage) {
      videoDuration = userData.quickVideoAdvantageSeconds || 30 // Use custom duration or default to 30 seconds
    } else {
      // Use plan-based video duration, but allow admin overrides for non-trial users
      if (!videoDuration || userData.plan === 'Trial') {
        videoDuration = getPlanVideoDuration(userData.plan)
      }
    }

    return {
      videoDuration: videoDuration,
      earningPerBatch: getPlanEarning(userData.plan), // Earning per batch of 50 videos
      plan: userData.plan,
      hasQuickAdvantage: hasActiveQuickAdvantage,
      quickAdvantageExpiry: userData.quickVideoAdvantageExpiry
    }
  } catch (error) {
    console.error('Error getting user video settings:', error)
    return {
      videoDuration: 30, // Default 30 seconds for trial
      earningPerBatch: 10, // Default trial earning per batch of 50 videos
      plan: 'Trial',
      hasQuickAdvantage: false
    }
  }
}

// Check if user has active quick video advantage
export function checkQuickVideoAdvantageActive(userData: any): boolean {
  if (!userData.quickVideoAdvantage) {
    return false
  }

  // Use remaining days if available (new system), otherwise fall back to expiry date (legacy)
  if (userData.quickVideoAdvantageRemainingDays !== undefined) {
    return userData.quickVideoAdvantageRemainingDays > 0
  }

  // Legacy fallback for existing users
  if (userData.quickVideoAdvantageExpiry) {
    const now = new Date()
    return now < userData.quickVideoAdvantageExpiry
  }

  return false
}

// Grant quick video advantage to user (admin function)
export async function grantQuickVideoAdvantage(userId: string, days: number, grantedBy: string, seconds: number = 30) {
  try {
    if (days <= 0 || days > 365) {
      throw new Error('Days must be between 1 and 365')
    }

    if (seconds < 1 || seconds > 420) {
      throw new Error('Seconds must be between 1 and 420 (7 minutes)')
    }

    const now = new Date()
    const expiry = new Date(now.getTime() + (days * 24 * 60 * 60 * 1000))

    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.quickVideoAdvantage]: true,
      [FIELD_NAMES.quickVideoAdvantageExpiry]: Timestamp.fromDate(expiry), // Keep for legacy compatibility
      [FIELD_NAMES.quickVideoAdvantageDays]: days,
      [FIELD_NAMES.quickVideoAdvantageRemainingDays]: days, // Set remaining days to granted days
      [FIELD_NAMES.quickVideoAdvantageSeconds]: seconds,
      [FIELD_NAMES.quickVideoAdvantageGrantedBy]: grantedBy,
      [FIELD_NAMES.quickVideoAdvantageGrantedAt]: Timestamp.fromDate(now)
    })

    console.log(`Granted quick video advantage to user ${userId} for ${days} days until ${expiry.toDateString()}`)

    // Add transaction record
    await addTransaction(userId, {
      type: 'quick_advantage_granted',
      amount: 0,
      description: `Quick video advantage granted for ${days} days by ${grantedBy}`
    })

    return { success: true, expiry }
  } catch (error) {
    console.error('Error granting quick video advantage:', error)
    throw error
  }
}

// Remove quick video advantage from user (admin function)
export async function removeQuickVideoAdvantage(userId: string, removedBy: string) {
  try {
    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.quickVideoAdvantage]: false,
      [FIELD_NAMES.quickVideoAdvantageExpiry]: null,
      [FIELD_NAMES.quickVideoAdvantageDays]: 0,
      [FIELD_NAMES.quickVideoAdvantageRemainingDays]: 0,
      [FIELD_NAMES.quickVideoAdvantageSeconds]: 30,
      [FIELD_NAMES.quickVideoAdvantageGrantedBy]: '',
      [FIELD_NAMES.quickVideoAdvantageGrantedAt]: null
    })

    console.log(`Removed quick video advantage from user ${userId}`)

    // Add transaction record
    await addTransaction(userId, {
      type: 'quick_advantage_removed',
      amount: 0,
      description: `Quick video advantage removed by ${removedBy}`
    })

    return { success: true }
  } catch (error) {
    console.error('Error removing quick video advantage:', error)
    throw error
  }
}

// Update user video duration (admin function)
export async function updateUserVideoDuration(userId: string, durationInSeconds: number) {
  try {
    // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)
    const isQuickDuration = [1, 10, 30].includes(durationInSeconds)
    const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420

    if (!isQuickDuration && !isStandardDuration) {
      throw new Error('Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration')
    }

    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.videoDuration]: durationInSeconds
    })

    console.log(`Updated video duration for user ${userId} to ${durationInSeconds} seconds`)
  } catch (error) {
    console.error('Error updating user video duration:', error)
    throw error
  }
}

// Validate bank details
function validateBankDetails(bankDetails: BankDetails) {
  const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails

  if (!accountHolderName || accountHolderName.trim().length < 2) {
    throw new Error('Account holder name must be at least 2 characters long')
  }

  if (!accountNumber || !/^\d{9,18}$/.test(accountNumber.trim())) {
    throw new Error('Account number must be 9-18 digits')
  }

  if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {
    throw new Error('Invalid IFSC code format (e.g., SBIN0001234)')
  }

  if (!bankName || bankName.trim().length < 2) {
    throw new Error('Bank name must be at least 2 characters long')
  }
}

// Notification interface
export interface Notification {
  id?: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  targetUsers: 'all' | 'specific'
  userIds?: string[]
  createdAt: Date
  createdBy: string
  // All notifications are now blocking by default - no need for isBlocking field
}

// Add notification (admin function) - All notifications are now blocking
export async function addNotification(notification: Omit<Notification, 'id' | 'createdAt'>) {
  try {
    const notificationData = {
      title: notification.title,
      message: notification.message,
      type: notification.type,
      targetUsers: notification.targetUsers,
      userIds: notification.userIds || [],
      createdAt: Timestamp.now(),
      createdBy: notification.createdBy
    }

    console.log('Adding notification to Firestore:', notificationData)

    const docRef = await addDoc(collection(db, COLLECTIONS.notifications), notificationData)
    console.log('Notification added successfully with ID:', docRef.id)

    // Verify the notification was added
    const addedDoc = await getDoc(docRef)
    if (addedDoc.exists()) {
      console.log('Notification verified in database:', addedDoc.data())
    } else {
      console.warn('Notification not found after adding')
    }

    return docRef.id
  } catch (error) {
    console.error('Error adding notification:', error)
    throw error
  }
}

// Get notifications for a user
export async function getUserNotifications(userId: string, limitCount = 20) {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getUserNotifications:', userId)
      return []
    }

    console.log(`Loading notifications for user: ${userId}`)

    // Try to get notifications with fallback for indexing issues
    let allUsersSnapshot, specificUserSnapshot

    try {
      // Get notifications targeted to all users
      const allUsersQuery = query(
        collection(db, COLLECTIONS.notifications),
        where('targetUsers', '==', 'all'),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      )

      allUsersSnapshot = await getDocs(allUsersQuery)
      console.log(`Found ${allUsersSnapshot.docs.length} notifications for all users`)
    } catch (error) {
      console.warn('Error querying all users notifications, trying without orderBy:', error)
      // Fallback without orderBy if index is not ready
      const allUsersQuery = query(
        collection(db, COLLECTIONS.notifications),
        where('targetUsers', '==', 'all'),
        limit(limitCount)
      )
      allUsersSnapshot = await getDocs(allUsersQuery)
    }

    try {
      // Get notifications targeted to specific user
      const specificUserQuery = query(
        collection(db, COLLECTIONS.notifications),
        where('targetUsers', '==', 'specific'),
        where('userIds', 'array-contains', userId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      )

      specificUserSnapshot = await getDocs(specificUserQuery)
      console.log(`Found ${specificUserSnapshot.docs.length} notifications for specific user`)
    } catch (error) {
      console.warn('Error querying specific user notifications, trying without orderBy:', error)
      // Fallback without orderBy if index is not ready
      const specificUserQuery = query(
        collection(db, COLLECTIONS.notifications),
        where('targetUsers', '==', 'specific'),
        where('userIds', 'array-contains', userId),
        limit(limitCount)
      )
      specificUserSnapshot = await getDocs(specificUserQuery)
    }

    const notifications: Notification[] = []

    // Process all users notifications
    allUsersSnapshot.docs.forEach(doc => {
      notifications.push({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      } as Notification)
    })

    // Process specific user notifications
    specificUserSnapshot.docs.forEach(doc => {
      notifications.push({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      } as Notification)
    })

    // Sort by creation date (newest first)
    notifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())

    const finalNotifications = notifications.slice(0, limitCount)
    console.log(`Returning ${finalNotifications.length} total notifications for user`)

    return finalNotifications
  } catch (error) {
    console.error('Error getting user notifications:', error)
    return []
  }
}

// Get all notifications (admin function)
export async function getAllNotifications(limitCount = 50) {
  try {
    const q = query(
      collection(db, COLLECTIONS.notifications),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    )

    const querySnapshot = await getDocs(q)
    const notifications = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date()
    })) as Notification[]

    return notifications
  } catch (error) {
    console.error('Error getting all notifications:', error)
    return []
  }
}

// Delete notification (admin function)
export async function deleteNotification(notificationId: string) {
  try {
    if (!notificationId || typeof notificationId !== 'string') {
      throw new Error('Invalid notification ID provided')
    }

    console.log('Deleting notification:', notificationId)
    await deleteDoc(doc(db, COLLECTIONS.notifications, notificationId))
    console.log('Notification deleted successfully')
  } catch (error) {
    console.error('Error deleting notification:', error)
    throw error
  }
}

// Mark notification as read
export async function markNotificationAsRead(notificationId: string, userId: string) {
  try {
    // For now, we'll store read status in localStorage since it's user-specific
    const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]')
    if (!readNotifications.includes(notificationId)) {
      readNotifications.push(notificationId)
      localStorage.setItem(`read_notifications_${userId}`, JSON.stringify(readNotifications))
    }
  } catch (error) {
    console.error('Error marking notification as read:', error)
  }
}

// Check if notification is read
export function isNotificationRead(notificationId: string, userId: string): boolean {
  try {
    const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]')
    return readNotifications.includes(notificationId)
  } catch (error) {
    console.error('Error checking notification read status:', error)
    return false
  }
}

// Get unread notification count
export function getUnreadNotificationCount(notifications: Notification[], userId: string): number {
  try {
    const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]')
    return notifications.filter(notification => !readNotifications.includes(notification.id)).length
  } catch (error) {
    console.error('Error getting unread notification count:', error)
    return 0
  }
}

// Get unread notifications - All notifications are now blocking
export async function getUnreadNotifications(userId: string): Promise<Notification[]> {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getUnreadNotifications:', userId)
      return []
    }

    console.log(`Loading unread notifications for user: ${userId}`)

    // Get all notifications for the user
    const allNotifications = await getUserNotifications(userId, 50)

    // Filter for unread notifications
    const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]')
    const unreadNotifications = allNotifications.filter(notification =>
      notification.id &&
      !readNotifications.includes(notification.id)
    )

    console.log(`Found ${unreadNotifications.length} unread notifications`)
    return unreadNotifications
  } catch (error) {
    console.error('Error getting unread notifications:', error)
    return []
  }
}

// Check if user has unread notifications
export async function hasUnreadNotifications(userId: string): Promise<boolean> {
  try {
    const unreadNotifications = await getUnreadNotifications(userId)
    return unreadNotifications.length > 0
  } catch (error) {
    console.error('Error checking for unread notifications:', error)
    return false
  }
}

// Check if user has pending withdrawals
export async function hasPendingWithdrawals(userId: string): Promise<boolean> {
  try {
    const q = query(
      collection(db, COLLECTIONS.withdrawals),
      where('userId', '==', userId),
      where('status', '==', 'pending'),
      limit(1)
    )

    const snapshot = await getDocs(q)
    return !snapshot.empty
  } catch (error) {
    console.error('Error checking pending withdrawals:', error)
    return false
  }
}

// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)
export async function checkWithdrawalAllowed(userId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Check user plan first
    const userData = await getUserData(userId)
    if (!userData) {
      return {
        allowed: false,
        reason: 'Unable to verify user information. Please try again.'
      }
    }

    // Check if user is on trial plan
    if (userData.plan === 'Trial') {
      return {
        allowed: false,
        reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'
      }
    }

    // Check if plan is expired
    const planStatus = await isUserPlanExpired(userId)
    if (planStatus.expired) {
      return {
        allowed: false,
        reason: `Your plan has expired and withdrawals are disabled. ${planStatus.reason}`
      }
    }

    // Check if user has pending withdrawals
    const hasPending = await hasPendingWithdrawals(userId)
    if (hasPending) {
      return {
        allowed: false,
        reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'
      }
    }

    const now = new Date()
    const currentHour = now.getHours()

    // Check time restrictions (10 AM to 6 PM)
    if (currentHour < 10 || currentHour >= 18) {
      return {
        allowed: false,
        reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'
      }
    }

    // Check admin leave day
    const { isAdminLeaveDay } = await import('./leaveService')
    const isAdminLeave = await isAdminLeaveDay(now)
    if (isAdminLeave) {
      return {
        allowed: false,
        reason: 'Withdrawals are not allowed on admin leave/holiday days'
      }
    }

    // Check user leave day
    const { isUserOnLeave } = await import('./leaveService')
    const isUserLeave = await isUserOnLeave(userId, now)
    if (isUserLeave) {
      return {
        allowed: false,
        reason: 'Withdrawals are not allowed on your leave days'
      }
    }

    return { allowed: true }
  } catch (error) {
    console.error('Error checking withdrawal allowed:', error)
    return { allowed: false, reason: 'Unable to verify withdrawal eligibility. Please try again.' }
  }
}

// Create withdrawal request with atomic operations to prevent race conditions
export async function createWithdrawalRequest(userId: string, amount: number, bankDetails: any) {
  try {
    // Check minimum withdrawal amount
    if (amount < 50) {
      throw new Error('Minimum withdrawal amount is ₹50')
    }

    // Use Firestore transaction to ensure atomic operations
    const result = await runTransaction(db, async (transaction) => {
      // Get user document reference
      const userRef = doc(db, COLLECTIONS.users, userId)
      const userDoc = await transaction.get(userRef)

      if (!userDoc.exists()) {
        throw new Error('User not found')
      }

      const userData = userDoc.data()
      const currentWallet = userData[FIELD_NAMES.wallet] || 0

      // Check if withdrawal is allowed (this includes pending withdrawal check)
      const withdrawalCheck = await checkWithdrawalAllowed(userId)
      if (!withdrawalCheck.allowed) {
        throw new Error(withdrawalCheck.reason)
      }

      // Check if user has sufficient balance
      if (currentWallet < amount) {
        throw new Error(`Insufficient wallet balance. Available: ₹${currentWallet}, Requested: ₹${amount}`)
      }

      // Debit the amount from user's wallet atomically
      const newWalletBalance = currentWallet - amount
      transaction.update(userRef, {
        [FIELD_NAMES.wallet]: newWalletBalance
      })

      // Create withdrawal document
      const withdrawalData = {
        userId,
        amount,
        bankDetails,
        status: 'pending',
        date: Timestamp.now(),
        createdAt: Timestamp.now()
      }

      const withdrawalRef = doc(collection(db, COLLECTIONS.withdrawals))
      transaction.set(withdrawalRef, withdrawalData)

      // Create transaction record
      const transactionData = {
        userId,
        type: 'withdrawal_request',
        amount: -amount,
        description: `Withdrawal request submitted - ₹${amount} debited from wallet`,
        date: Timestamp.now(),
        status: 'pending',
        balanceAfter: newWalletBalance
      }

      const transactionRef = doc(collection(db, COLLECTIONS.transactions))
      transaction.set(transactionRef, transactionData)

      return withdrawalRef.id
    })

    console.log(`✅ Withdrawal request created successfully: ${result}`)
    return result

  } catch (error) {
    console.error('Error creating withdrawal request:', error)
    throw error
  }
}

// Get user withdrawals
export async function getUserWithdrawals(userId: string, limitCount = 20) {
  try {
    const q = query(
      collection(db, COLLECTIONS.withdrawals),
      where('userId', '==', userId),
      orderBy('date', 'desc'),
      limit(limitCount)
    )

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date?.toDate()
    }))
  } catch (error) {
    console.error('Error getting user withdrawals:', error)
    return []
  }
}

// Generate unique referral code with MYN prefix and sequential numbering
export async function generateUniqueReferralCode(): Promise<string> {
  try {
    // Try to get count from server for sequential numbering
    try {
      const usersCollection = collection(db, COLLECTIONS.users)
      const snapshot = await getCountFromServer(usersCollection)
      const count = snapshot.data().count
      const sequentialNumber = (count + 1).toString().padStart(4, '0')
      return `MYN${sequentialNumber}`
    } catch (countError) {
      console.warn('Failed to get count from server, using fallback method:', countError)
      // Fallback to timestamp-based generation
      const timestamp = Date.now().toString().slice(-4)
      const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase()
      return `MYN${timestamp}${randomPart}`
    }
  } catch (error) {
    console.error('Error generating unique referral code:', error)
    // Final fallback
    const timestamp = Date.now().toString().slice(-4)
    return `MYN${timestamp}`
  }
}

// Generate sequential referral code (alias for generateUniqueReferralCode)
export async function generateSequentialReferralCode(): Promise<string> {
  return generateUniqueReferralCode()
}


