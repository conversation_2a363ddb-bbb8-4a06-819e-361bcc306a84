(()=>{var e={};e.id=6669,e.ids=[6669],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29785:(e,t,r)=>{Promise.resolve().then(r.bind(r,80079))},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{Cn:()=>d,db:()=>c,j2:()=>l});var s=r(67989),i=r(63385),a=r(75535),o=r(70146),n=r(24791);let u=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,i.xI)(u),c=(0,a.aU)(u);(0,o.c7)(u);let d=(0,n.Uz)(u,"us-central1")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},34683:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),u={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>n[e]);r.d(t,u);let l={children:["",{children:["test-reg-simple",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80079)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-reg-simple\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-reg-simple\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-reg-simple/page",pathname:"/test-reg-simple",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},42937:(e,t,r)=>{Promise.resolve().then(r.bind(r,92325))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80079:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-reg-simple\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-reg-simple\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92325:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),a=r(63385),o=r(75535),n=r(33784);function u(){let[e,t]=(0,i.useState)(""),[r,u]=(0,i.useState)(!1),l=e=>{t(t=>t+e+"\n")},c=async()=>{t(""),u(!0);let e=null;try{l("\uD83E\uDDEA Testing Simple Registration Process...\n"),l("=== STEP 1: Creating Firebase Auth User ===");let t=`test${Date.now()}@example.com`;e=(await (0,a.eJ)(n.j2,t,"test123456")).user,l(`✅ Auth user created: ${e.uid}`),l(`   Email: ${e.email}`),l("\n=== STEP 2: Waiting for Auth State ==="),await new Promise(e=>setTimeout(e,2e3)),l(`Current auth user: ${n.j2.currentUser?.uid}`),l(`Auth state matches: ${n.j2.currentUser?.uid===e.uid}`),l("\n=== STEP 3: Creating User Document ===");let r=Date.now().toString().slice(-4),s=Math.random().toString(36).substring(2,4).toUpperCase(),i=`MY${r}${s}`;l(`Generated referral code: ${i}`);let u={name:"Test User",email:t.toLowerCase(),mobile:"9876543210",referralCode:i,referredBy:"",referralBonusCredited:!1,plan:"Trial",planExpiry:null,activeDays:1,joinedDate:o.Dc.now(),wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null,videoDuration:30,status:"active"};l(`Document path: users/${e.uid}`),l(`Data fields: ${Object.keys(u).length}`),l("\n=== STEP 4: Creating Document ===");let c=(0,o.H9)(n.db,"users",e.uid);l("Attempting setDoc..."),await (0,o.BN)(c,u),l("✅ setDoc completed successfully"),l("\n=== STEP 5: Verifying Document ===");let d=await (0,o.x7)(c);if(d.exists()){let e=d.data();l("✅ Document verification successful"),l(`   Name: ${e.name}`),l(`   Email: ${e.email}`),l(`   Plan: ${e.plan}`),l(`   Referral Code: ${e.referralCode}`),l(`   Wallet: ${e.wallet}`),l(`   Fields count: ${Object.keys(e).length}`),l("\n\uD83C\uDF89 SUCCESS: Registration process works perfectly!"),l("The issue might be in the registration form logic or error handling.")}else l("❌ Document not found after creation"),l("This indicates a serious Firestore issue")}catch(e){l(`❌ Test failed: ${e.message}`),l(`   Error code: ${e.code}`),l(`   Error name: ${e.name}`),"permission-denied"===e.code?(l("\n\uD83D\uDD27 PERMISSION DENIED ANALYSIS:"),l("   - Firestore security rules are blocking the write"),l("   - Check if user authentication is properly recognized"),l("   - Verify rules allow authenticated users to create documents")):"unavailable"===e.code?(l("\n\uD83D\uDD27 FIRESTORE UNAVAILABLE:"),l("   - Check internet connection"),l("   - Verify Firestore is enabled in Firebase console")):"auth/email-already-in-use"===e.code&&(l("\n\uD83D\uDD27 EMAIL ALREADY IN USE:"),l("   - This is expected if testing multiple times"),l("   - Try with a different email or wait a moment")),l(`
   Full error details:`),l(`   ${JSON.stringify(e,null,2)}`)}finally{if(e)try{l("\n=== CLEANUP ==="),await (0,a.hG)(e),l("✅ Test user deleted")}catch(e){l(`⚠️ User deletion failed: ${e.message}`)}try{await (0,a.CI)(n.j2),l("✅ Signed out")}catch(e){l(`⚠️ Sign out failed: ${e.message}`)}u(!1)}},d=async()=>{t(""),u(!0);try{l("\uD83D\uDD27 Testing Firebase Basics...\n"),l("=== TEST 1: Firebase Instances ==="),l(`Auth: ${n.j2?"✅ Initialized":"❌ Not initialized"}`),l(`Firestore: ${n.db?"✅ Initialized":"❌ Not initialized"}`),l(`Current user: ${n.j2.currentUser?.uid||"None"}`),l("\n=== TEST 2: Basic Firestore Write ===");let e=(0,o.H9)(n.db,"test_basic",`test_${Date.now()}`);await (0,o.BN)(e,{test:!0,timestamp:o.Dc.now()}),l("✅ Basic write successful"),l("\n=== TEST 3: Basic Firestore Read ===");let t=await (0,o.x7)(e);t.exists()?(l("✅ Basic read successful"),l(`   Data: ${JSON.stringify(t.data())}`)):l("❌ Basic read failed"),l("\n✅ Firebase basics are working correctly")}catch(e){l(`❌ Firebase basics test failed: ${e.message}`),l(`   Error code: ${e.code}`)}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Simple Registration"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,s.jsx)("button",{onClick:d,disabled:r,className:"btn-primary",children:r?"Testing...":"Test Firebase Basics"}),(0,s.jsx)("button",{onClick:c,disabled:r,className:"btn-primary",children:r?"Testing...":"Test Registration Process"})]}),(0,s.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,s.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||"Click a test button to start..."})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,6958,8441],()=>r(34683));module.exports=s})();