(()=>{var e={};e.id=9206,e.ids=[9206],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},701:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=r(65239),i=r(48088),o=r(88170),n=r.n(o),d=r(30893),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);r.d(t,a);let c={children:["",{children:["debug-dates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,28048)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-dates\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-dates\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/debug-dates/page",pathname:"/debug-dates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2652:(e,t,r)=>{Promise.resolve().then(r.bind(r,41202))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28048:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-dates\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-dates\\page.tsx","default")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41202:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(60687),i=r(43210),o=r(87979),n=r(75535),d=r(33784);function a(){let{user:e,loading:t,isAdmin:r}=(0,o.wC)(),[a,c]=(0,i.useState)([]),[u,l]=(0,i.useState)(!1),p=async()=>{l(!0);try{let e=(0,n.P)((0,n.collection)(d.db,"transactions"),(0,n.My)("date","desc"),(0,n.AB)(20)),t=(await (0,n.getDocs)(e)).docs.map(e=>{let t=e.data(),r=t.date&&"function"==typeof t.date.toDate?t.date.toDate():null;return{id:e.id,rawDate:t.date,dateType:typeof t.date,hasToDate:t.date&&"function"==typeof t.date.toDate,convertedDate:r,dateString:r?r.toLocaleString():"Cannot convert",daysAgo:r?Math.floor((new Date().getTime()-r.getTime())/864e5):"N/A",description:t.description,amount:t.amount,type:t.type}});t.sort((e,t)=>e.convertedDate&&t.convertedDate?e.convertedDate.getTime()-t.convertedDate.getTime():0),c(t),console.log("\uD83D\uDD0D Raw Firestore data (oldest first):",t)}catch(e){console.error("Error checking Firestore data:",e)}l(!1)};return t?(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):r?(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 py-8",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\uD83D\uDD0D Debug Firestore Dates"}),(0,s.jsx)("button",{onClick:p,disabled:u,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg mb-6 disabled:opacity-50",children:u?"Checking...":"Check Firestore Data"}),a.length>0&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold",children:"Raw Firestore Transaction Data (Oldest First):"}),a.map((e,t)=>(0,s.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,s.jsxs)("h3",{className:"font-semibold text-blue-600",children:["Transaction ",t+1,": ",e.id]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Description:"})," ",e.description]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Amount:"})," ₹",e.amount]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Type:"})," ",e.type]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Date Type:"})," ",e.dateType]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Days Ago:"})," ",e.daysAgo]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Has toDate():"})," ",e.hasToDate?"Yes":"No"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Raw Date:"})," ",String(e.rawDate)]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)("strong",{children:"Converted Date:"})," ",e.dateString]})]})]},e.id))]})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600",children:"You need admin privileges to access this page."})]})})}},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92924:(e,t,r)=>{Promise.resolve().then(r.bind(r,28048))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,6958,7567,8441,7979],()=>r(701));module.exports=s})();