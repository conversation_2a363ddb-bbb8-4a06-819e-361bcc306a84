(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{1469:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return o},getImageProps:function(){return r}});let s=a(8229),i=a(8883),n=a(3063),l=s._(a(1193));function r(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let o=n.Image},2751:(e,t,a)=>{Promise.resolve().then(a.bind(a,3282))},3282:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(5155),i=a(2115),n=a(6874),l=a.n(n),r=a(6766),o=a(6681),c=a(7460),d=a(3592),m=a(6273),x=a(12);function u(e){let{userId:t,isOpen:a,onClose:n}=e,[l,r]=(0,i.useState)([]),[o,c]=(0,i.useState)(!0);(0,i.useEffect)(()=>{a&&t&&m()},[a,t]);let m=async()=>{try{c(!0);let e=await (0,d.Ss)(t,20);r(e)}catch(e){console.error("Error loading notifications:",e)}finally{c(!1)}},x=e=>{e.id&&!(0,d.mv)(e.id,t)&&((0,d.bA)(e.id,t),r([...l]))},u=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},f=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return"".concat(e," minute").concat(e>1?"s":""," ago")}if(t<86400){let e=Math.floor(t/3600);return"".concat(e," hour").concat(e>1?"s":""," ago")}{let e=Math.floor(t/86400);return"".concat(e," day").concat(e>1?"s":""," ago")}};return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-16 z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-gray-900",children:[(0,s.jsx)("i",{className:"fas fa-bell mr-2"}),"Notifications"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:m,disabled:o,className:"text-gray-500 hover:text-gray-700 transition-colors p-1",title:"Refresh notifications",children:(0,s.jsx)("i",{className:"fas fa-sync-alt ".concat(o?"animate-spin":"")})}),(0,s.jsx)("button",{onClick:n,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,s.jsx)("i",{className:"fas fa-times text-xl"})})]})]}),(0,s.jsx)("div",{className:"overflow-y-auto max-h-[60vh]",children:o?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsx)("div",{className:"spinner w-8 h-8"})}):0===l.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No notifications yet"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"You'll see important updates here"})]}):(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:l.map(e=>{let a=!!e.id&&(0,d.mv)(e.id,t);return(0,s.jsx)("div",{onClick:()=>x(e),className:"p-4 cursor-pointer hover:bg-gray-50 transition-colors ".concat(a?"":"bg-blue-50 border-l-4 border-l-blue-500"),children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("i",{className:u(e.type)})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h4",{className:"text-sm font-medium ".concat(a?"text-gray-700":"text-gray-900"),children:e.title}),!a&&(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,s.jsx)("p",{className:"text-sm mt-1 ".concat(a?"text-gray-600":"text-gray-800"),children:e.message}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:f(e.createdAt)})]})]})},e.id)})})}),l.length>0&&(0,s.jsx)("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:(0,s.jsx)("button",{onClick:()=>{l.forEach(e=>{e.id&&!(0,d.mv)(e.id,t)&&(0,d.bA)(e.id,t)}),r([...l])},className:"w-full text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Mark all as read"})})]})}):null}function f(e){let{userId:t,onClick:a}=e,[n,l]=(0,i.useState)([]),[r,o]=(0,i.useState)(0),[c,m]=(0,i.useState)(!1);(0,i.useEffect)(()=>{if(t){x();let e=setInterval(x,15e3);return()=>clearInterval(e)}},[t]);let x=async()=>{try{m(!0);let e=await (0,d.Ss)(t,20);l(e);let a=(0,d.ul)(e,t);if(a>r&&r>0){let e=document.querySelector(".notification-bell");e&&(e.classList.add("animate-bounce"),setTimeout(()=>{e.classList.remove("animate-bounce")},1e3))}o(a),console.log("Loaded ".concat(e.length," notifications, ").concat(a," unread"))}catch(e){console.error("Error loading notifications for bell:",e)}finally{m(!1)}};return(0,s.jsxs)("button",{onClick:a,className:"relative p-2 text-white hover:text-yellow-300 transition-colors",title:"".concat(r," unread notifications"),children:[(0,s.jsx)("i",{className:"fas fa-bell text-xl notification-bell ".concat(c?"animate-pulse":"")}),r>0&&(0,s.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse",children:r>9?"9+":r}),c&&(0,s.jsx)("span",{className:"absolute -bottom-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-3 w-3 flex items-center justify-center",children:(0,s.jsx)("i",{className:"fas fa-sync-alt text-xs animate-spin"})})]})}var h=a(8647),g=a(4752),p=a.n(g);function v(e){let{userId:t,currentMonth:n,usedLeaves:l,maxLeaves:r,onLeaveCountChange:o}=e,[c,d]=(0,i.useState)([]),[m,x]=(0,i.useState)(!1),[u,f]=(0,i.useState)(!1),[h,g]=(0,i.useState)(""),[v,b]=(0,i.useState)({date:"",reason:""});(0,i.useEffect)(()=>{j(),w()},[t]);let w=async()=>{try{let{getUserData:e,getPlanValidityDays:s,calculateUserActiveDays:i}=await Promise.resolve().then(a.bind(a,3592)),n=await e(t);if(n){let e,a=new Date;if("Trial"===n.plan){let t=n.joinedDate||new Date;e=new Date(t.getTime()+1728e5)}else if(n.planExpiry)e=n.planExpiry;else{let l=s(n.plan),r=await i(t),o=Math.max(0,l-r);e=new Date(a.getTime()+24*o*36e5)}let l=new Date(a.getTime()+2592e6),r=e<l?e:l;g(r.toISOString().split("T")[0])}}catch(t){console.error("Error calculating max date:",t);let e=new Date;e.setDate(e.getDate()+30),g(e.toISOString().split("T")[0])}},j=async()=>{try{let{getUserLeaves:e}=await a.e(9567).then(a.bind(a,9567)),s=await e(t);d(s)}catch(e){console.error("Error loading user leaves:",e),d([])}},y=async()=>{try{if(!v.date||!v.reason.trim())return void p().fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let e=new Date(v.date),s=new Date;s.setHours(0,0,0,0);let i=new Date(s);if(i.setDate(i.getDate()+1),e<=s)return void p().fire({icon:"error",title:"Invalid Date",text:"Cannot apply leave for today or past dates. Please select a future date."});try{let{isUserPlanExpired:i}=await Promise.resolve().then(a.bind(a,3592));if((await i(t)).expired)return void p().fire({icon:"error",title:"Plan Expired",text:"Your plan has expired. Cannot apply for leave."});let{getUserData:n,getPlanValidityDays:l,calculateUserActiveDays:r}=await Promise.resolve().then(a.bind(a,3592)),o=await n(t);if(o){let a;if("Trial"===o.plan){let e=o.joinedDate||new Date;a=new Date(e.getTime()+1728e5)}else if(o.planExpiry)a=o.planExpiry;else{let e=l(o.plan),i=await r(t),n=Math.max(0,e-i);a=new Date(s.getTime()+24*n*36e5)}if(e>a)return void p().fire({icon:"error",title:"Date Outside Plan Period",text:"Cannot apply leave beyond your plan expiry date (".concat(a.toLocaleDateString(),").")})}}catch(e){console.error("Error checking plan expiry:",e)}if(l>=r)return void p().fire({icon:"error",title:"Leave Limit Exceeded",text:"You have already used all ".concat(r," leaves for this month.")});if(c.find(t=>t.date.toDateString()===e.toDateString()))return void p().fire({icon:"error",title:"Duplicate Application",text:"You have already applied for leave on this date."});f(!0);let{applyUserLeave:n}=await a.e(9567).then(a.bind(a,9567)),d=await n({userId:t,date:e,reason:v.reason.trim()});await j(),o&&o(),d.autoApproved?p().fire({icon:"success",title:"✅ Leave Auto-Approved!",html:'\n            <div class="text-left">\n              <p><strong>Your leave has been automatically approved!</strong></p>\n              <br>\n              <p><strong>Date:</strong> '.concat(e.toLocaleDateString(),"</p>\n              <p><strong>Reason:</strong> ").concat(v.reason.trim(),'</p>\n              <br>\n              <p class="text-green-600"><strong>Leave Quota:</strong> ').concat(d.usedLeaves,"/").concat(d.maxLeaves,' used this month</p>\n              <p class="text-blue-600"><strong>Status:</strong> Approved automatically</p>\n            </div>\n          '),timer:6e3,showConfirmButton:!0,confirmButtonText:"Great!"}):p().fire({icon:"warning",title:"⏳ Leave Pending Approval",html:'\n            <div class="text-left">\n              <p><strong>Your leave application has been submitted.</strong></p>\n              <br>\n              <p><strong>Date:</strong> '.concat(e.toLocaleDateString(),"</p>\n              <p><strong>Reason:</strong> ").concat(v.reason.trim(),'</p>\n              <br>\n              <p class="text-orange-600"><strong>Status:</strong> Pending admin approval (quota exceeded)</p>\n              <p class="text-gray-600"><strong>Leave Quota:</strong> ').concat(d.maxLeaves,"/").concat(d.maxLeaves," used this month</p>\n            </div>\n          "),timer:6e3,showConfirmButton:!0,confirmButtonText:"Understood"}),b({date:"",reason:""}),x(!1)}catch(e){console.error("Error applying leave:",e),p().fire({icon:"error",title:"Application Failed",text:"Failed to apply for leave. Please try again."})}finally{f(!1)}},N=async e=>{try{let t=c.find(t=>t.id===e);if(!t||"pending"!==t.status)return void p().fire({icon:"error",title:"Cannot Cancel",text:"Only pending leave applications can be cancelled."});if((await p().fire({title:"Cancel Leave Application",text:"Are you sure you want to cancel this leave application?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Cancel",cancelButtonText:"Keep Application"})).isConfirmed){let{cancelUserLeave:t}=await a.e(9567).then(a.bind(a,9567));await t(e),await j(),o&&o(),p().fire({icon:"success",title:"Application Cancelled",text:"Your leave application has been cancelled.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error cancelling leave:",e),p().fire({icon:"error",title:"Cancellation Failed",text:"Failed to cancel leave application. Please try again."})}},D=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},A=(e,t)=>{switch(e){case"approved":return"system"===t?"fas fa-magic text-green-500":"fas fa-check-circle text-green-500";case"rejected":return"fas fa-times-circle text-red-500";case"pending":return"fas fa-clock text-yellow-500";default:return"fas fa-question-circle text-gray-500"}},C=r-l;return(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-calendar-times mr-2"}),"Leave Management"]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"text-sm text-white/80",children:[n," Leaves"]}),(0,s.jsxs)("div",{className:"text-lg font-bold text-white",children:[C,"/",r," Available"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"bg-green-500/20 p-3 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-green-400",children:r}),(0,s.jsx)("div",{className:"text-xs text-white/80",children:"Monthly Quota"})]}),(0,s.jsxs)("div",{className:"bg-yellow-500/20 p-3 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-yellow-400",children:l}),(0,s.jsx)("div",{className:"text-xs text-white/80",children:"Used"})]}),(0,s.jsxs)("div",{className:"bg-blue-500/20 p-3 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-blue-400",children:C}),(0,s.jsx)("div",{className:"text-xs text-white/80",children:"Remaining"})]})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("button",{onClick:()=>x(!0),disabled:C<=0,className:"w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),C>0?"Apply for Leave":"No Leaves Available"]})}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white/80",children:"Recent Applications"}),0===c.length?(0,s.jsxs)("div",{className:"text-center py-4 text-white/60",children:[(0,s.jsx)("i",{className:"fas fa-calendar-check text-2xl mb-2"}),(0,s.jsx)("p",{className:"text-sm",children:"No leave applications yet"})]}):(0,s.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:c.slice(-5).reverse().map(e=>(0,s.jsx)("div",{className:"bg-white/10 p-3 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-white font-medium",children:e.date.toLocaleDateString()}),(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ".concat(D(e.status)),children:[(0,s.jsx)("i",{className:"".concat(A(e.status,e.reviewedBy)," mr-1")}),e.status?e.status.charAt(0).toUpperCase()+e.status.slice(1):"Unknown","system"===e.reviewedBy&&"approved"===e.status&&(0,s.jsx)("span",{className:"ml-1",title:"Auto-approved",children:"⚡"})]})]}),(0,s.jsxs)("div",{className:"text-sm text-white/70 mt-1",children:[e.reason,"system"===e.reviewedBy&&"approved"===e.status&&(0,s.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:[(0,s.jsx)("i",{className:"fas fa-magic mr-1"}),"Auto-approved (within quota)"]}),e.reviewNotes&&"system"!==e.reviewedBy&&(0,s.jsxs)("div",{className:"text-xs text-blue-400 mt-1",children:[(0,s.jsx)("i",{className:"fas fa-comment mr-1"}),e.reviewNotes]})]})]}),"pending"===e.status&&(0,s.jsx)("button",{onClick:()=>N(e.id),className:"text-red-400 hover:text-red-300 text-sm",children:(0,s.jsx)("i",{className:"fas fa-times"})})]})},e.id))})]}),m&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",style:{zIndex:99999},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Apply for Leave"}),(0,s.jsx)("button",{onClick:()=>x(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times text-xl"})})]}),h&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),(0,s.jsxs)("span",{className:"text-sm",children:["Leave applications are allowed until ",new Date(h).toLocaleDateString()]})]})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,s.jsx)("input",{type:"date",value:v.date,onChange:e=>b(t=>({...t,date:e.target.value})),min:(()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]})(),max:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Leave can only be applied for future dates within your plan period"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,s.jsx)("textarea",{value:v.reason,onChange:e=>b(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]}),(0,s.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,s.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"You have ",C," leave(s) remaining for ",n,"."]}),C>0&&(0,s.jsxs)("div",{className:"text-sm text-green-700 mt-2",children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,s.jsx)("strong",{children:"Auto-Approval:"})," Your leave will be automatically approved since you have available quota."]}),C<=0&&(0,s.jsxs)("div",{className:"text-sm text-orange-700 mt-2",children:[(0,s.jsx)("i",{className:"fas fa-clock mr-2"}),(0,s.jsx)("strong",{children:"Manual Review:"})," Leave will require admin approval as quota is exceeded."]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,s.jsx)("button",{onClick:()=>x(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,s.jsx)("button",{onClick:y,disabled:u,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:u?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Applying..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Apply Leave"]})})]})]})})]})}function b(){let{user:e,loading:t}=(0,o.Nu)(),{hasBlockingNotifications:n,isChecking:g,markAllAsRead:p}=(0,c.J)((null==e?void 0:e.uid)||null),[b,w]=(0,i.useState)(null),[j,y]=(0,i.useState)(null),[N,D]=(0,i.useState)(null),[A,C]=(0,i.useState)(!0),[E,k]=(0,i.useState)(!1),[S,z]=(0,i.useState)(0),[L,I]=(0,i.useState)(0);(0,i.useEffect)(()=>{(0,x.G9)(),(async()=>{try{let{checkVersionAndClearCache:e}=await a.e(5897).then(a.bind(a,8278));await e()}catch(e){console.error("Silent version check failed:",e)}})(),e&&T()},[e]);let T=async()=>{try{C(!0);try{console.log("\uD83D\uDE80 Loading dashboard with optimized functions...");let t=await m.x8.getDashboardData(e.uid);w(t.userData),y(t.walletData),D(t.videoData),console.log("✅ Dashboard loaded via optimized functions")}catch(i){console.warn("⚠️ Optimized functions failed, using fallback:",i);let[t,a,s]=await Promise.all([(0,d.getUserData)(e.uid),(0,d.getWalletData)(e.uid),(0,d.getVideoCountData)(e.uid)]);w(t),y(a),D(s)}if(await P(),b)try{let{updateUserActiveDays:t}=await Promise.resolve().then(a.bind(a,3592));await t(e.uid);let s=await (0,d.getUserData)(e.uid);w(s);let{getLiveActiveDays:i}=await Promise.resolve().then(a.bind(a,3592)),n=await i(e.uid);I(n)}catch(e){console.error("Error updating active days:",e)}}catch(e){console.error("Error loading dashboard data:",e)}finally{C(!1)}},P=async()=>{try{let{getUserMonthlyLeaveCount:t}=await a.e(9567).then(a.bind(a,9567)),s=new Date,i=s.getFullYear(),n=s.getMonth()+1,l=await t(e.uid,i,n);return z(l),console.log("User ".concat(e.uid," has used ").concat(l," leaves this month")),l}catch(e){return console.error("Error loading user leave count:",e),z(0),0}};return t||A||g?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:t?"Loading...":g?"Checking notifications...":"Loading dashboard..."})]})}):n&&e?(0,s.jsx)(h.A,{userId:e.uid,onAllRead:p}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(r.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:40,height:40,className:"mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"MyTube Dashboard"}),(0,s.jsxs)("p",{className:"text-white/80",children:["Welcome back, ",(null==b?void 0:b.name)||"User"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,s.jsx)(f,{userId:e.uid,onClick:()=>k(!0)}),(0,s.jsxs)("button",{onClick:()=>{(0,x._f)(null==e?void 0:e.uid,"/login")},className:"glass-button px-4 py-2 text-white hover:bg-red-500/20 transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-sign-out-alt mr-2"}),"Logout"]})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6",children:[(0,s.jsxs)(l(),{href:"/work",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-play-circle text-3xl text-youtube-red mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Watch Videos"})]}),(0,s.jsxs)(l(),{href:"/wallet",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-wallet text-3xl text-green-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Wallet"})]}),(0,s.jsxs)(l(),{href:"/transactions",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-history text-3xl text-orange-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Transactions"})]}),(0,s.jsxs)(l(),{href:"/refer",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-users text-3xl text-blue-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Refer & Earn"})]}),(0,s.jsxs)(l(),{href:"/profile",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-user text-3xl text-purple-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Profile"})]}),(0,s.jsxs)(l(),{href:"/plans",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-crown text-3xl text-yellow-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Plans"})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-wallet mr-2"}),"Wallet Overview"]}),(0,s.jsxs)("div",{className:"bg-green-500/20 p-6 rounded-lg text-center",children:[(0,s.jsx)("h3",{className:"text-green-400 font-semibold mb-2",children:"My Wallet"}),(0,s.jsxs)("p",{className:"text-4xl font-bold text-white mb-2",children:["₹",((null==j?void 0:j.wallet)||0).toFixed(2)]}),(0,s.jsx)("p",{className:"text-white/60",children:"Total available balance"}),(null==b?void 0:b.plan)==="Trial"&&(0,s.jsxs)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,s.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,s.jsx)("span",{className:"text-red-400 font-medium text-sm",children:"Withdrawal Restricted"})]}),(0,s.jsx)("p",{className:"text-white/80 text-xs mb-3",children:"Trial users cannot withdraw funds. Upgrade to enable withdrawals."}),(0,s.jsxs)(l(),{href:"/plans",className:"btn-secondary text-xs px-3 py-1",children:[(0,s.jsx)("i",{className:"fas fa-arrow-up mr-1"}),"Upgrade Plan"]})]}),(0,s.jsxs)(l(),{href:"/wallet",className:"btn-primary mt-4 inline-block",children:[(0,s.jsx)("i",{className:"fas fa-eye mr-2"}),"View Details"]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-video mr-2"}),"Today's Progress"]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-youtube-red",children:(null==N?void 0:N.todayVideos)||0}),(0,s.jsx)("p",{className:"text-white/80",children:"Videos Watched"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-yellow-400",children:(null==N?void 0:N.remainingVideos)||0}),(0,s.jsx)("p",{className:"text-white/80",children:"Remaining"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-green-400",children:(null==N?void 0:N.totalVideos)||0}),(0,s.jsx)("p",{className:"text-white/80",children:"Total Videos"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-blue-400",children:L}),(0,s.jsx)("p",{className:"text-white/80",children:"Active Days"})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("div",{className:"bg-white/20 rounded-full h-3",children:(0,s.jsx)("div",{className:"bg-youtube-red h-3 rounded-full transition-all duration-300",style:{width:"".concat(N?N.todayVideos/50*100:0,"%")}})}),(0,s.jsxs)("p",{className:"text-white/80 text-sm mt-2 text-center",children:[(null==N?void 0:N.todayVideos)||0," / 50 videos completed today"]})]})]}),e&&(0,s.jsx)(v,{userId:e.uid,currentMonth:new Date().toLocaleDateString("en-US",{month:"long",year:"numeric"}),usedLeaves:S,maxLeaves:4,onLeaveCountChange:P}),e&&(0,s.jsx)(u,{userId:e.uid,isOpen:E,onClose:()=>k(!1)})]})}},6273:(e,t,a)=>{"use strict";a.d(t,{x8:()=>p});var s=a(2144),i=a(6104);let n=(0,s.Qg)(i.Cn,"getUserDashboardData"),l=(0,s.Qg)(i.Cn,"submitVideoBatch"),r=(0,s.Qg)(i.Cn,"processWithdrawalRequest"),o=(0,s.Qg)(i.Cn,"getUserNotifications"),c=(0,s.Qg)(i.Cn,"getUserTransactions"),d=(0,s.Qg)(i.Cn,"getAdminWithdrawals"),m=(0,s.Qg)(i.Cn,"getAdminDashboardStats"),x=(0,s.Qg)(i.Cn,"getAdminUsers"),u=(0,s.Qg)(i.Cn,"getAdminNotifications"),f=(0,s.Qg)(i.Cn,"createAdminNotification");async function h(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await n({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}}async function g(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await m({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let p={getDashboardData:async function(e){try{return await h(e)}catch(o){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:s,getVideoCountData:i}=await a.e(3592).then(a.bind(a,3592)),[n,l,r]=await Promise.all([t(e),s(e),i(e)]);return{userData:n,walletData:l,videoData:r}}},submitVideoBatch:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await l({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await r(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await o({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{console.log("\uD83D\uDE80 Using optimized transactions function...");let s=await c({userId:e,limit:t,type:a});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}},getAdminDashboardStats:async function(){try{return await g()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3592),a.e(6779)]).then(a.bind(a,6779));return await e()}},getAdminUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await x(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await u({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await f(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",i.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",i.Cn.region);let e=await n({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),!1}}}},6766:(e,t,a)=>{"use strict";a.d(t,{default:()=>i.a});var s=a(1469),i=a.n(s)},7460:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var s=a(2115),i=a(3592);function n(e){let[t,a]=(0,s.useState)(!1),[n,l]=(0,s.useState)(!0);(0,s.useEffect)(()=>{e?r():l(!1)},[e]);let r=async()=>{try{l(!0);let t=await (0,i.iA)(e);a(t)}catch(e){console.error("Error checking for blocking notifications:",e),a(!1)}finally{l(!1)}};return{hasBlockingNotifications:t,isChecking:n,checkForBlockingNotifications:r,markAllAsRead:()=>{a(!1)}}}},8647:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var s=a(5155),i=a(2115),n=a(3592);function l(e){let{userId:t,onAllRead:a}=e,[l,r]=(0,i.useState)([]),[o,c]=(0,i.useState)(0),[d,m]=(0,i.useState)(!0);(0,i.useEffect)(()=>{t&&x()},[t]);let x=async()=>{try{m(!0);let e=await (0,n.AX)(t);r(e),0===e.length&&a()}catch(e){console.error("Error loading notifications:",e),a()}finally{m(!1)}},u=async()=>{let e=l[o];(null==e?void 0:e.id)&&(await (0,n.bA)(e.id,t),o<l.length-1?c(o+1):a())};if(d)return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,s.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===l.length)return null;let f=l[o];return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(f.type)}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,s.jsxs)("p",{className:"text-blue-100 text-sm",children:[o+1," of ",l.length," notifications"]})]})]}),(0,s.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,s.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:f.title}),(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,s.jsx)("p",{className:"text-gray-800 leading-relaxed",children:f.message})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,s.jsxs)("span",{children:["From: ",f.createdBy]}),(0,s.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?"".concat(Math.floor(t/60)," minutes ago"):t<86400?"".concat(Math.floor(t/3600)," hours ago"):"".concat(Math.floor(t/86400)," days ago")})(f.createdAt)})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,s.jsx)("span",{children:"Progress"}),(0,s.jsxs)("span",{children:[o+1,"/",l.length]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((o+1)/l.length*100,"%")}})})]}),(0,s.jsxs)("button",{onClick:u,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,s.jsx)("i",{className:"fas fa-check"}),(0,s.jsx)("span",{children:o<l.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,s.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,s.jsx)("i",{className:"fas fa-info-circle"}),(0,s.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3063,3592,6681,8441,1684,7358],()=>t(2751)),_N_E=e.O()}]);