(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9206],{4039:(e,t,s)=>{Promise.resolve().then(s.bind(s,9744))},9744:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(5155),r=s(2115),d=s(6681),n=s(5317),i=s(6104);function c(){let{user:e,loading:t,isAdmin:s}=(0,d.wC)(),[c,l]=(0,r.useState)([]),[o,h]=(0,r.useState)(!1),x=async()=>{h(!0);try{let e=(0,n.P)((0,n.collection)(i.db,"transactions"),(0,n.My)("date","desc"),(0,n.AB)(20)),t=(await (0,n.getDocs)(e)).docs.map(e=>{let t=e.data(),s=t.date&&"function"==typeof t.date.toDate?t.date.toDate():null;return{id:e.id,rawDate:t.date,dateType:typeof t.date,hasToDate:t.date&&"function"==typeof t.date.toDate,convertedDate:s,dateString:s?s.toLocaleString():"Cannot convert",daysAgo:s?Math.floor((new Date().getTime()-s.getTime())/864e5):"N/A",description:t.description,amount:t.amount,type:t.type}});t.sort((e,t)=>e.convertedDate&&t.convertedDate?e.convertedDate.getTime()-t.convertedDate.getTime():0),l(t),console.log("\uD83D\uDD0D Raw Firestore data (oldest first):",t)}catch(e){console.error("Error checking Firestore data:",e)}h(!1)};return t?(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):s?(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 py-8",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto px-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\uD83D\uDD0D Debug Firestore Dates"}),(0,a.jsx)("button",{onClick:x,disabled:o,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg mb-6 disabled:opacity-50",children:o?"Checking...":"Check Firestore Data"}),c.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Raw Firestore Transaction Data (Oldest First):"}),c.map((e,t)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,a.jsxs)("h3",{className:"font-semibold text-blue-600",children:["Transaction ",t+1,": ",e.id]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Description:"})," ",e.description]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Amount:"})," ₹",e.amount]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Type:"})," ",e.type]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Date Type:"})," ",e.dateType]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Days Ago:"})," ",e.daysAgo]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Has toDate():"})," ",e.hasToDate?"Yes":"No"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Raw Date:"})," ",String(e.rawDate)]}),(0,a.jsxs)("div",{className:"col-span-2",children:[(0,a.jsx)("strong",{children:"Converted Date:"})," ",e.dateString]})]})]},e.id))]})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You need admin privileges to access this page."})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6681,8441,1684,7358],()=>t(4039)),_N_E=e.O()}]);