{"/admin/page": "/admin", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/daily-active-days/page": "/admin/daily-active-days", "/_not-found/page": "/_not-found", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/settings/page": "/admin/settings", "/admin/transactions/page": "/admin/transactions", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/users/page": "/admin/users", "/admin/withdrawals/page": "/admin/withdrawals", "/clear-cache/page": "/clear-cache", "/dashboard/page": "/dashboard", "/debug-firestore/page": "/debug-firestore", "/admin/setup/page": "/admin/setup", "/debug-registration-simple/page": "/debug-registration-simple", "/admin/notifications/page": "/admin/notifications", "/login/page": "/login", "/debug-firestore-issue/page": "/debug-firestore-issue", "/forgot-password/page": "/forgot-password", "/page": "/", "/plans/page": "/plans", "/register/page": "/register", "/refer/page": "/refer", "/profile/page": "/profile", "/debug-registration/page": "/debug-registration", "/reset-password/page": "/reset-password", "/admin/upload-users/page": "/admin/upload-users", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firestore/page": "/test-firestore", "/support/page": "/support", "/test-firebase/page": "/test-firebase", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/wallet/page": "/wallet", "/test-registration/page": "/test-registration", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/test-functions/page": "/test-functions", "/test-reg-simple/page": "/test-reg-simple", "/transactions/page": "/transactions", "/work/page": "/work"}