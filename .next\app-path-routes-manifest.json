{"/_not-found/page": "/_not-found", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/leaves/page": "/admin/leaves", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/page": "/admin", "/admin/login/page": "/admin/login", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/transactions/page": "/admin/transactions", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/users/page": "/admin/users", "/dashboard/page": "/dashboard", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/upload-users/page": "/admin/upload-users", "/clear-cache/page": "/clear-cache", "/debug-firestore/page": "/debug-firestore", "/login/page": "/login", "/forgot-password/page": "/forgot-password", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-registration/page": "/debug-registration", "/refer/page": "/refer", "/plans/page": "/plans", "/page": "/", "/profile/page": "/profile", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-functions/page": "/test-functions", "/test-firestore/page": "/test-firestore", "/test-registration/page": "/test-registration", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}