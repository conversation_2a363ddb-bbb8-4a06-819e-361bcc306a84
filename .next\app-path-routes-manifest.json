{"/admin/leaves/page": "/admin/leaves", "/_not-found/page": "/_not-found", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/notifications/page": "/admin/notifications", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/settings/page": "/admin/settings", "/admin/upload-users/page": "/admin/upload-users", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/setup/page": "/admin/setup", "/clear-cache/page": "/clear-cache", "/admin/transactions/page": "/admin/transactions", "/debug-firestore/page": "/debug-firestore", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-registration-simple/page": "/debug-registration-simple", "/admin/users/page": "/admin/users", "/debug-registration/page": "/debug-registration", "/dashboard/page": "/dashboard", "/plans/page": "/plans", "/debug-firestore-issue/page": "/debug-firestore-issue", "/forgot-password/page": "/forgot-password", "/login/page": "/login", "/refer/page": "/refer", "/registration-diagnostics/page": "/registration-diagnostics", "/page": "/", "/profile/page": "/profile", "/register/page": "/register", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-simple-registration/page": "/test-simple-registration", "/test-reg-simple/page": "/test-reg-simple", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-videos/page": "/test-videos", "/test-registration/page": "/test-registration", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}