{"/admin/fix-active-days/page": "/admin/fix-active-days", "/_not-found/page": "/_not-found", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/leaves/page": "/admin/leaves", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/login/page": "/admin/login", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/page": "/admin", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/setup/page": "/admin/setup", "/admin/users/page": "/admin/users", "/dashboard/page": "/dashboard", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/transactions/page": "/admin/transactions", "/admin/upload-users/page": "/admin/upload-users", "/debug-dates/page": "/debug-dates", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/withdrawals/page": "/admin/withdrawals", "/clear-cache/page": "/clear-cache", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/login/page": "/login", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/page": "/", "/refer/page": "/refer", "/plans/page": "/plans", "/profile/page": "/profile", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-functions/page": "/test-functions", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work", "/test-registration/page": "/test-registration"}