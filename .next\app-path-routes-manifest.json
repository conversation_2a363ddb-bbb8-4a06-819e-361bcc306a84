{"/_not-found/page": "/_not-found", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/login/page": "/admin/login", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/page": "/admin", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/settings/page": "/admin/settings", "/admin/transactions/page": "/admin/transactions", "/admin/users/page": "/admin/users", "/admin/withdrawals/page": "/admin/withdrawals", "/dashboard/page": "/dashboard", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/setup/page": "/admin/setup", "/admin/upload-users/page": "/admin/upload-users", "/clear-cache/page": "/clear-cache", "/debug-firestore/page": "/debug-firestore", "/debug-firestore-issue/page": "/debug-firestore-issue", "/login/page": "/login", "/forgot-password/page": "/forgot-password", "/plans/page": "/plans", "/page": "/", "/profile/page": "/profile", "/refer/page": "/refer", "/debug-registration/page": "/debug-registration", "/register/page": "/register", "/debug-registration-simple/page": "/debug-registration-simple", "/reset-password/page": "/reset-password", "/support/page": "/support", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-registration/page": "/test-registration", "/test-firebase/page": "/test-firebase", "/test-functions/page": "/test-functions", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/work/page": "/work", "/wallet/page": "/wallet"}