{"/_not-found/page": "/_not-found", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/leaves/page": "/admin/leaves", "/admin/page": "/admin", "/admin/login/page": "/admin/login", "/admin/notifications/page": "/admin/notifications", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/users/page": "/admin/users", "/clear-cache/page": "/clear-cache", "/admin/upload-users/page": "/admin/upload-users", "/admin/transactions/page": "/admin/transactions", "/debug-firestore-issue/page": "/debug-firestore-issue", "/dashboard/page": "/dashboard", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/admin/withdrawals/page": "/admin/withdrawals", "/login/page": "/login", "/debug-registration/page": "/debug-registration", "/forgot-password/page": "/forgot-password", "/plans/page": "/plans", "/page": "/", "/profile/page": "/profile", "/registration-diagnostics/page": "/registration-diagnostics", "/refer/page": "/refer", "/register/page": "/register", "/support/page": "/support", "/reset-password/page": "/reset-password", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-registration/page": "/test-registration", "/test-functions/page": "/test-functions", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}