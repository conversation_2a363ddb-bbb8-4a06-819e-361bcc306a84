{"/_not-found/page": "/_not-found", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/setup/page": "/admin/setup", "/admin/settings/page": "/admin/settings", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/transactions/page": "/admin/transactions", "/admin/users/page": "/admin/users", "/admin/upload-users/page": "/admin/upload-users", "/clear-cache/page": "/clear-cache", "/dashboard/page": "/dashboard", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/plans/page": "/plans", "/page": "/", "/profile/page": "/profile", "/forgot-password/page": "/forgot-password", "/refer/page": "/refer", "/register/page": "/register", "/reset-password/page": "/reset-password", "/registration-diagnostics/page": "/registration-diagnostics", "/support/page": "/support", "/test-firebase/page": "/test-firebase", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase-connection/page": "/test-firebase-connection", "/test-registration/page": "/test-registration", "/test-functions/page": "/test-functions", "/test-reg-simple/page": "/test-reg-simple", "/test-firestore/page": "/test-firestore", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/transactions/page": "/transactions", "/work/page": "/work"}