{"/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/login/page": "/admin/login", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/fix-permissions/page": "/admin/fix-permissions", "/_not-found/page": "/_not-found", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/transactions/page": "/admin/transactions", "/debug-dates/page": "/debug-dates", "/dashboard/page": "/dashboard", "/clear-cache/page": "/clear-cache", "/forgot-password/page": "/forgot-password", "/admin/upload-users/page": "/admin/upload-users", "/debug-firestore/page": "/debug-firestore", "/login/page": "/login", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-registration/page": "/debug-registration", "/plans/page": "/plans", "/profile/page": "/profile", "/page": "/", "/reset-password/page": "/reset-password", "/debug-registration-simple/page": "/debug-registration-simple", "/register/page": "/register", "/support/page": "/support", "/refer/page": "/refer", "/test-firebase-connection/page": "/test-firebase-connection", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-firebase/page": "/test-firebase", "/test-registration/page": "/test-registration", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/transactions/page": "/transactions", "/test-simple-registration/page": "/test-simple-registration", "/work/page": "/work", "/test-functions/page": "/test-functions", "/test-videos/page": "/test-videos", "/admin/users/page": "/admin/users", "/wallet/page": "/wallet"}