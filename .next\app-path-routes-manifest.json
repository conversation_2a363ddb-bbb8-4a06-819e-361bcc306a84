{"/_not-found/page": "/_not-found", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/leaves/page": "/admin/leaves", "/admin/login/page": "/admin/login", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/transactions/page": "/admin/transactions", "/clear-cache/page": "/clear-cache", "/admin/upload-users/page": "/admin/upload-users", "/admin/users/page": "/admin/users", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore-issue/page": "/debug-firestore-issue", "/dashboard/page": "/dashboard", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/login/page": "/login", "/debug-registration/page": "/debug-registration", "/forgot-password/page": "/forgot-password", "/plans/page": "/plans", "/refer/page": "/refer", "/register/page": "/register", "/page": "/", "/profile/page": "/profile", "/registration-diagnostics/page": "/registration-diagnostics", "/reset-password/page": "/reset-password", "/test-firebase-connection/page": "/test-firebase-connection", "/support/page": "/support", "/test-firebase/page": "/test-firebase", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-registration/page": "/test-registration", "/test-simple-registration/page": "/test-simple-registration", "/test-reg-simple/page": "/test-reg-simple", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}