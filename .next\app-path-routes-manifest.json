{"/_not-found/page": "/_not-found", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/settings/page": "/admin/settings", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/notifications/page": "/admin/notifications", "/admin/setup/page": "/admin/setup", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/page": "/admin", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/transactions/page": "/admin/transactions", "/dashboard/page": "/dashboard", "/admin/upload-users/page": "/admin/upload-users", "/clear-cache/page": "/clear-cache", "/admin/users/page": "/admin/users", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore/page": "/debug-firestore", "/debug-registration/page": "/debug-registration", "/debug-firestore-issue/page": "/debug-firestore-issue", "/login/page": "/login", "/debug-registration-simple/page": "/debug-registration-simple", "/forgot-password/page": "/forgot-password", "/admin/daily-active-days/page": "/admin/daily-active-days", "/page": "/", "/register/page": "/register", "/profile/page": "/profile", "/plans/page": "/plans", "/refer/page": "/refer", "/registration-diagnostics/page": "/registration-diagnostics", "/support/page": "/support", "/reset-password/page": "/reset-password", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-registration/page": "/test-registration", "/test-firebase/page": "/test-firebase", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/transactions/page": "/transactions", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/work/page": "/work"}