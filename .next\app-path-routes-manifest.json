{"/_not-found/page": "/_not-found", "/admin/leaves/page": "/admin/leaves", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/login/page": "/admin/login", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/transactions/page": "/admin/transactions", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/users/page": "/admin/users", "/clear-cache/page": "/clear-cache", "/admin/upload-users/page": "/admin/upload-users", "/dashboard/page": "/dashboard", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-registration/page": "/debug-registration", "/forgot-password/page": "/forgot-password", "/profile/page": "/profile", "/login/page": "/login", "/refer/page": "/refer", "/page": "/", "/plans/page": "/plans", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-reg-simple/page": "/test-reg-simple", "/test-registration/page": "/test-registration", "/test-simple-registration/page": "/test-simple-registration", "/work/page": "/work", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet"}