{"/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/leaves/page": "/admin/leaves", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/page": "/admin", "/admin/login/page": "/admin/login", "/_not-found/page": "/_not-found", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/setup/page": "/admin/setup", "/admin/upload-users/page": "/admin/upload-users", "/admin/users/page": "/admin/users", "/admin/transactions/page": "/admin/transactions", "/admin/withdrawals/page": "/admin/withdrawals", "/dashboard/page": "/dashboard", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-firestore-issue/page": "/debug-firestore-issue", "/clear-cache/page": "/clear-cache", "/debug-firestore/page": "/debug-firestore", "/forgot-password/page": "/forgot-password", "/page": "/", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/register/page": "/register", "/profile/page": "/profile", "/plans/page": "/plans", "/test-firebase/page": "/test-firebase", "/reset-password/page": "/reset-password", "/support/page": "/support", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase-connection/page": "/test-firebase-connection", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/test-registration/page": "/test-registration", "/work/page": "/work", "/test-firestore/page": "/test-firestore", "/transactions/page": "/transactions", "/test-functions/page": "/test-functions", "/test-reg-simple/page": "/test-reg-simple", "/refer/page": "/refer", "/wallet/page": "/wallet"}