{"/admin/fix-permissions/page": "/admin/fix-permissions", "/_not-found/page": "/_not-found", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/leaves/page": "/admin/leaves", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/transactions/page": "/admin/transactions", "/admin/upload-users/page": "/admin/upload-users", "/admin/users/page": "/admin/users", "/dashboard/page": "/dashboard", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/fix-active-days/page": "/admin/fix-active-days", "/forgot-password/page": "/forgot-password", "/debug-registration-simple/page": "/debug-registration-simple", "/admin/withdrawals/page": "/admin/withdrawals", "/login/page": "/login", "/plans/page": "/plans", "/page": "/", "/profile/page": "/profile", "/refer/page": "/refer", "/debug-firestore/page": "/debug-firestore", "/register/page": "/register", "/reset-password/page": "/reset-password", "/debug-registration/page": "/debug-registration", "/clear-cache/page": "/clear-cache", "/registration-diagnostics/page": "/registration-diagnostics", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase/page": "/test-firebase", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-registration/page": "/test-registration", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}