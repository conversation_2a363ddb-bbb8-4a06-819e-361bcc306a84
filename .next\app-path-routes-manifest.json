{"/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/_not-found/page": "/_not-found", "/admin/page": "/admin", "/admin/leaves/page": "/admin/leaves", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/notifications/page": "/admin/notifications", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/transactions/page": "/admin/transactions", "/admin/test-blocking/page": "/admin/test-blocking", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/users/page": "/admin/users", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-registration/page": "/debug-registration", "/debug-firestore/page": "/debug-firestore", "/clear-cache/page": "/clear-cache", "/forgot-password/page": "/forgot-password", "/dashboard/page": "/dashboard", "/page": "/", "/refer/page": "/refer", "/register/page": "/register", "/plans/page": "/plans", "/login/page": "/login", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/reset-password/page": "/reset-password", "/test-firebase/page": "/test-firebase", "/admin/upload-users/page": "/admin/upload-users", "/profile/page": "/profile", "/test-registration/page": "/test-registration", "/admin/login/page": "/admin/login", "/support/page": "/support", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-functions/page": "/test-functions", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/transactions/page": "/transactions", "/work/page": "/work", "/wallet/page": "/wallet"}