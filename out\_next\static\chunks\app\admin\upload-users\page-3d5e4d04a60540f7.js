(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2475],{1187:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>j});var t=s(5155),i=s(2115),l=s(6874),r=s.n(l),n=s(8999),o=s(6681),d=s(3004),c=s(5317),u=s(6104),m=s(3592);function h(e){let a=[];if((!e.name||e.name.trim().length<2)&&a.push("Name is required and must be at least 2 characters"),e.email&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||a.push("Valid email address is required"),e.mobile&&/^[6-9]\d{9}$/.test(e.mobile)||a.push("Valid 10-digit mobile number is required"),(!e.password||e.password.length<6)&&a.push("Password is required and must be at least 6 characters"),e.plan&&!["Trial","Starter","Basic","Premium","Gold","Platinum","Diamond"].includes(e.plan)&&a.push("Plan must be one of: Trial, Starter, Basic, Premium, Gold, Platinum, Diamond"),e.activeDays&&(e.activeDays<0||e.activeDays>365)&&a.push("Active days must be between 0 and 365"),e.wallet&&e.wallet<0&&a.push("Wallet balance cannot be negative"),e.totalVideos&&e.totalVideos<0&&a.push("Total videos cannot be negative"),e.referralCode&&(/^MYN\d+$/.test(e.referralCode)||a.push("Referral code must follow format MYN0001, MYN0002, etc.")),void 0!==e.quickVideoAdvantage&&"boolean"!=typeof e.quickVideoAdvantage&&a.push("Quick video advantage must be true or false"),e.quickVideoAdvantageDays&&(e.quickVideoAdvantageDays<1||e.quickVideoAdvantageDays>365)&&a.push("Quick video advantage days must be between 1 and 365"),e.quickVideoAdvantageSeconds&&(e.quickVideoAdvantageSeconds<1||e.quickVideoAdvantageSeconds>600)&&a.push("Quick video advantage seconds must be between 1 and 600 (10 minutes)"),e.videoDuration){let s=[1,10,30].includes(e.videoDuration),t=e.videoDuration>=60&&e.videoDuration<=600;s||t||a.push("Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-10 minutes (60-600 seconds) for standard duration")}if(!0!==e.quickVideoAdvantage||e.quickVideoAdvantageDays||a.push("Quick video advantage days must be provided when quick advantage is enabled"),!0===e.quickVideoAdvantage&&e.quickVideoAdvantageSeconds){let a=[1,10,30,60,120,180,240,300,360,420,600];a.includes(e.quickVideoAdvantageSeconds)||console.warn("Quick video advantage seconds ".concat(e.quickVideoAdvantageSeconds," is not a common duration. Valid options: ").concat(a.join(", ")))}return a}async function p(e,a){try{let s=(0,c.P)((0,c.collection)(u.db,m.COLLECTIONS.users),(0,c._M)(m.FIELD_NAMES.email,"==",e));if(!(await (0,c.getDocs)(s)).empty)return console.log("Email ".concat(e," already exists in Firestore")),!0;let t=(0,c.P)((0,c.collection)(u.db,m.COLLECTIONS.users),(0,c._M)(m.FIELD_NAMES.mobile,"==",a));if(!(await (0,c.getDocs)(t)).empty)return console.log("Mobile ".concat(a," already exists in Firestore")),!0;return!1}catch(e){return console.error("Error checking user existence:",e),!1}}async function x(e){try{let a=h(e);if(a.length>0)return{success:!1,error:a.join(", ")};if(await p(e.email,e.mobile))return{success:!1,error:"User with this email or mobile already exists (duplicate)"};let s=(await (0,d.eJ)(u.j2,e.email,e.password)).user,t=e.referralCode;if(t){let e=(0,c.P)((0,c.collection)(u.db,m.COLLECTIONS.users),(0,c._M)(m.FIELD_NAMES.referralCode,"==",t));if(!(await (0,c.getDocs)(e)).empty)throw Error("Referral code ".concat(t," already exists. Please use a unique referral code."))}else t=await (0,m.x4)();let i=null;if(e.quickVideoAdvantage&&e.quickVideoAdvantageDays){let a=new Date;i=c.Dc.fromDate(new Date(a.getTime()+24*e.quickVideoAdvantageDays*36e5))}let l={[m.FIELD_NAMES.name]:e.name.trim(),[m.FIELD_NAMES.email]:e.email.toLowerCase(),[m.FIELD_NAMES.mobile]:e.mobile,[m.FIELD_NAMES.referralCode]:t,[m.FIELD_NAMES.referredBy]:e.referredBy||"",[m.FIELD_NAMES.referralBonusCredited]:!1,[m.FIELD_NAMES.plan]:e.plan||"Trial",[m.FIELD_NAMES.planExpiry]:null,[m.FIELD_NAMES.activeDays]:e.activeDays||1,[m.FIELD_NAMES.joinedDate]:e.joinedDate?new Date(e.joinedDate):c.Dc.now(),[m.FIELD_NAMES.wallet]:e.wallet||0,[m.FIELD_NAMES.totalVideos]:e.totalVideos||0,[m.FIELD_NAMES.todayVideos]:0,[m.FIELD_NAMES.lastVideoDate]:null,[m.FIELD_NAMES.videoDuration]:e.videoDuration||300,status:"active",[m.FIELD_NAMES.quickVideoAdvantage]:e.quickVideoAdvantage||!1,[m.FIELD_NAMES.quickVideoAdvantageExpiry]:i,[m.FIELD_NAMES.quickVideoAdvantageDays]:e.quickVideoAdvantageDays||0,[m.FIELD_NAMES.quickVideoAdvantageSeconds]:e.quickVideoAdvantageSeconds||30,[m.FIELD_NAMES.quickVideoAdvantageGrantedBy]:e.quickVideoAdvantageGrantedBy||"",[m.FIELD_NAMES.quickVideoAdvantageGrantedAt]:e.quickVideoAdvantage?c.Dc.now():null};return await (0,c.BN)((0,c.H9)(u.db,m.COLLECTIONS.users,s.uid),l),{success:!0}}catch(a){console.error("Error creating user:",a);let e="Unknown error occurred";return"auth/email-already-in-use"===a.code?e="Email address is already in use (duplicate)":"auth/invalid-email"===a.code?e="Invalid email address":"auth/weak-password"===a.code?e="Password is too weak":a.message&&(e=a.message),{success:!1,error:e}}}async function f(e){let a={success:0,failed:0,errors:[],duplicates:0};try{let i=(await e.text()).split("\n").filter(e=>e.trim());if(i.length<2)throw Error("CSV file must have at least a header row and one data row");let l=i[0],r=l.includes("	")?"	":",",n=l.split(r).map(e=>e.trim().replace(/"/g,"")),o=[];for(let e=1;e<i.length;e++){let a=i[e].split(r).map(e=>e.trim().replace(/"/g,"")),s={};n.forEach((e,t)=>{s[e]=a[t]||""}),s.activeDays&&(s.activeDays=parseInt(s.activeDays)||0),s.wallet&&(s.wallet=parseFloat(s.wallet)||0),s.totalVideos&&(s.totalVideos=parseInt(s.totalVideos)||0),s.videoDuration&&(s.videoDuration=parseInt(s.videoDuration)||0),s.quickVideoAdvantage&&(s.quickVideoAdvantage="true"===s.quickVideoAdvantage.toLowerCase()),s.quickVideoAdvantageDays&&(s.quickVideoAdvantageDays=parseInt(s.quickVideoAdvantageDays)||0),s.quickVideoAdvantageSeconds&&(s.quickVideoAdvantageSeconds=parseInt(s.quickVideoAdvantageSeconds)||30),o.push(s)}for(let e=0;e<o.length;e++){let i=o[e],l="Processing user ".concat(e+1," of ").concat(o.length,": ").concat(i.name||i.email);console.log(l),window.updateUploadProgress&&window.updateUploadProgress(l);try{let l=await x(i);if(l.success)a.success++,console.log("✅ Created user: ".concat(i.email));else{var s,t;a.failed++,(null==(s=l.error)?void 0:s.includes("already exists"))||(null==(t=l.error)?void 0:t.includes("duplicate"))?(a.duplicates++,console.log("⚠️ Skipped duplicate: ".concat(i.email))):console.log("❌ Failed to create: ".concat(i.email," - ").concat(l.error)),a.errors.push("Row ".concat(e+2,": ").concat(l.error))}}catch(s){a.failed++,console.log("❌ Error creating: ".concat(i.email," - ").concat(s.message)),a.errors.push("Row ".concat(e+2,": ").concat(s.message||"Unknown error"))}e%5==0?await new Promise(e=>setTimeout(e,500)):await new Promise(e=>setTimeout(e,200))}return a}catch(e){throw console.error("Error uploading users from CSV:",e),Error("Failed to process CSV file: ".concat(e.message))}}async function g(e){let a={success:0,failed:0,errors:[],duplicates:0};try{let i=await e.text(),l=JSON.parse(i);if(!Array.isArray(l))throw Error("JSON file must contain an array of user objects");for(let e=0;e<l.length;e++){let i=l[e];try{let l=await x(i);if(l.success)a.success++;else{var s,t;a.failed++,((null==(s=l.error)?void 0:s.includes("already exists"))||(null==(t=l.error)?void 0:t.includes("duplicate")))&&a.duplicates++,a.errors.push("User ".concat(e+1," (").concat(i.email,"): ").concat(l.error))}}catch(s){a.failed++,a.errors.push("User ".concat(e+1," (").concat(i.email,"): ").concat(s.message||"Unknown error"))}e%5==0?await new Promise(e=>setTimeout(e,500)):await new Promise(e=>setTimeout(e,200))}return a}catch(e){throw console.error("Error uploading users from JSON:",e),Error("Failed to process JSON file: ".concat(e.message))}}var v=s(4752),w=s.n(v);function j(){let{user:e,loading:a,isAdmin:s}=(0,o.wC)();(0,n.useRouter)();let[l,d]=(0,i.useState)(!1),[c,u]=(0,i.useState)(null),[m,p]=(0,i.useState)(null),[x,v]=(0,i.useState)("csv"),[j,N]=(0,i.useState)([]),[b,y]=(0,i.useState)(!1),[k,A]=(0,i.useState)("");(0,i.useEffect)(()=>{let e=e=>{if(l)return e.preventDefault(),"Upload is in progress. Are you sure you want to leave?"};return window.addEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}},[l]);let D=async()=>{if(m)try{d(!0);let e=await m.text(),a=[];if("csv"===x){let s=e.split("\n").filter(e=>e.trim());if(s.length<2)throw Error("CSV file must have at least a header row and one data row");let t=s[0],i=t.includes("	")?"	":",",l=t.split(i).map(e=>e.trim().replace(/"/g,""));a=s.slice(1).map(e=>{let a=e.split(i).map(e=>e.trim().replace(/"/g,"")),s={};return l.forEach((e,t)=>{s[e]=a[t]||""}),s})}else if(a=JSON.parse(e),!Array.isArray(a))throw Error("JSON file must contain an array of user objects");let s=a.slice(0,5),t=[];s.forEach((e,a)=>{let s=h(e);s.length>0&&t.push("Row ".concat(a+1,": ").concat(s.join(", ")))}),N(s),y(!0),t.length>0&&w().fire({icon:"warning",title:"Validation Issues Found",html:'<div class="text-left"><p>Issues found in preview data:</p><ul>'.concat(t.map(e=>"<li>".concat(e,"</li>")).join(""),"</ul></div>"),confirmButtonText:"Continue Anyway",showCancelButton:!0,cancelButtonText:"Fix Data First"})}catch(e){console.error("Error previewing file:",e),w().fire({icon:"error",title:"Preview Failed",text:e.message||"Failed to preview file. Please check the format."})}finally{d(!1)}},V=async()=>{if(m&&(await w().fire({icon:"question",title:"Confirm User Upload",html:'\n        <div class="text-left">\n          <p><strong>Are you sure you want to upload users from this file?</strong></p>\n          <br>\n          <p>This will:</p>\n          <ul>\n            <li>Create Firebase Authentication accounts</li>\n            <li>Create user documents in Firestore</li>\n            <li>Use provided referral codes or assign new sequential ones</li>\n            <li>Set up wallet and transaction data</li>\n            <li>Apply quick video advantage if specified</li>\n            <li>Validate referral code uniqueness</li>\n          </ul>\n          <br>\n          <p class="text-red-600"><strong>Warning:</strong> This action cannot be undone!</p>\n        </div>\n      ',showCancelButton:!0,confirmButtonText:"Yes, Upload Users",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)try{let e;d(!0),u(null),A("Starting upload..."),w().fire({title:"Uploading Users",html:'\n          <div class="text-center">\n            <div class="spinner mx-auto mb-4"></div>\n            <p id="upload-progress">Starting upload...</p>\n            <p class="text-sm text-gray-600 mt-2">Please do not close this page or navigate away.</p>\n          </div>\n        ',allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1,didOpen:()=>{window.updateUploadProgress=e=>{let a=document.getElementById("upload-progress");a&&(a.textContent=e)}}}),e="csv"===x?await f(m):await g(m),w().close(),u(e),e.success>0?w().fire({icon:e.failed>0?"warning":"success",title:"Upload Complete",html:'\n            <div class="text-left">\n              <p><strong>Upload Summary:</strong></p>\n              <ul>\n                <li class="text-green-600">✓ Successfully created: '.concat(e.success," users</li>\n                ").concat(e.duplicates>0?'<li class="text-yellow-600">⚠ Skipped duplicates: '.concat(e.duplicates," users</li>"):"","\n                ").concat(e.failed>0?'<li class="text-red-600">✗ Failed: '.concat(e.failed," users</li>"):"","\n              </ul>\n              ").concat(e.errors.length>0?"<br><p><strong>Errors:</strong></p><ul>".concat(e.errors.slice(0,5).map(e=>'<li class="text-red-600">'.concat(e,"</li>")).join(""),"</ul>"):"","\n            </div>\n          "),timer:e.failed>0?void 0:5e3,showConfirmButton:e.failed>0}):w().fire({icon:"error",title:"Upload Failed",text:"No users were successfully created. Please check your data and try again."})}catch(e){console.error("Error uploading users:",e),w().fire({icon:"error",title:"Upload Failed",text:e.message||"Failed to upload users. Please try again."})}finally{d(!1)}};return a?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Upload Users"}),(0,t.jsx)("p",{className:"text-white/80",children:"Transfer existing users from old platform"})]}),l?(0,t.jsxs)("button",{disabled:!0,className:"btn-secondary opacity-50 cursor-not-allowed",title:"Upload in progress - navigation disabled",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]}):(0,t.jsxs)(r(),{href:"/admin/users",className:"btn-secondary",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]})]}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload User Data"]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Upload Format"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"radio",value:"csv",checked:"csv"===x,onChange:e=>v(e.target.value),className:"mr-2"}),(0,t.jsx)("span",{className:"text-white",children:"CSV/TSV File"})]}),(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"radio",value:"json",checked:"json"===x,onChange:e=>v(e.target.value),className:"mr-2"}),(0,t.jsx)("span",{className:"text-white",children:"JSON File"})]})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Sample Files"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("button",{onClick:()=>{let e=new Blob(["name,email,mobile,password,plan,activeDays,wallet,totalVideos,referredBy,referralCode,quickVideoAdvantage,quickVideoAdvantageDays,quickVideoAdvantageSeconds,quickVideoAdvantageGrantedBy,videoDuration\nJohn Doe,<EMAIL>,9876543210,password123,Basic,30,2000,100,MYN0001,MYN1001,true,7,10,<EMAIL>,300\nJane Smith,<EMAIL>,9876543211,password456,Premium,25,5000,150,MYN0002,MYN1002,false,,,,60\nMike Johnson,<EMAIL>,9876543212,password789,Starter,30,1000,50,,MYN1003,true,14,30,<EMAIL>,600\nSarah Wilson,<EMAIL>,9876543213,password321,Gold,20,8000,200,MYN0001,MYN1004,true,3,1,<EMAIL>,180"],{type:"text/csv"}),a=URL.createObjectURL(e),s=document.createElement("a");s.href=a,s.download="sample-users.csv",s.click(),URL.revokeObjectURL(a)},className:"btn-secondary text-sm",children:[(0,t.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample CSV"]}),(0,t.jsxs)("button",{onClick:()=>{let e=new Blob([JSON.stringify([{name:"John Doe",email:"<EMAIL>",mobile:"9876543210",password:"password123",plan:"Basic",activeDays:30,wallet:2e3,totalVideos:100,referredBy:"MYN0001",referralCode:"MYN1001",quickVideoAdvantage:!0,quickVideoAdvantageDays:7,quickVideoAdvantageSeconds:10,quickVideoAdvantageGrantedBy:"<EMAIL>"},{name:"Jane Smith",email:"<EMAIL>",mobile:"9876543211",password:"password456",plan:"Premium",activeDays:25,wallet:5e3,totalVideos:150,referredBy:"MYN0002",referralCode:"MYN1002",quickVideoAdvantage:!1},{name:"Mike Johnson",email:"<EMAIL>",mobile:"9876543212",password:"password789",plan:"Starter",activeDays:30,wallet:1e3,totalVideos:50,referralCode:"MYN1003",quickVideoAdvantage:!0,quickVideoAdvantageDays:14,quickVideoAdvantageSeconds:30,quickVideoAdvantageGrantedBy:"<EMAIL>"},{name:"Sarah Wilson",email:"<EMAIL>",mobile:"9876543213",password:"password321",plan:"Gold",activeDays:20,wallet:8e3,totalVideos:200,referredBy:"MYN0001",referralCode:"MYN1004",quickVideoAdvantage:!0,quickVideoAdvantageDays:3,quickVideoAdvantageSeconds:1,quickVideoAdvantageGrantedBy:"<EMAIL>"}],null,2)],{type:"application/json"}),a=URL.createObjectURL(e),s=document.createElement("a");s.href=a,s.download="sample-users.json",s.click(),URL.revokeObjectURL(a)},className:"btn-secondary text-sm",children:[(0,t.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample JSON"]})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select File"}),(0,t.jsx)("input",{type:"file",accept:"csv"===x?".csv,.tsv,.txt":".json",onChange:e=>{var a;let s=null==(a=e.target.files)?void 0:a[0];s&&(p(s),N([]),y(!1),u(null))},className:"form-input"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)("button",{onClick:D,disabled:!m||l,className:"btn-secondary",children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-eye mr-2"}),"Preview Data"]})}),(0,t.jsx)("button",{onClick:V,disabled:!m||l||!b,className:"btn-primary",children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Uploading..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]})})]}),l&&(0,t.jsx)("div",{className:"bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex items-center text-yellow-300",children:[(0,t.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),(0,t.jsxs)("span",{className:"text-sm",children:[(0,t.jsx)("strong",{children:"Upload in progress!"})," Please do not close this page or navigate away until the upload is complete."]})]})})]})]}),b&&j.length>0&&(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-table mr-2"}),"Data Preview (First 5 Records)"]}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full text-white",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-white/20",children:[(0,t.jsx)("th",{className:"text-left p-2",children:"Name"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Email"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Mobile"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Plan"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Active Days"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Wallet"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Total Videos"})]})}),(0,t.jsx)("tbody",{children:j.map((e,a)=>(0,t.jsxs)("tr",{className:"border-b border-white/10",children:[(0,t.jsx)("td",{className:"p-2",children:e.name||"N/A"}),(0,t.jsx)("td",{className:"p-2",children:e.email||"N/A"}),(0,t.jsx)("td",{className:"p-2",children:e.mobile||"N/A"}),(0,t.jsx)("td",{className:"p-2",children:e.plan||"Trial"}),(0,t.jsx)("td",{className:"p-2",children:e.activeDays||0}),(0,t.jsxs)("td",{className:"p-2",children:["₹",e.wallet||0]}),(0,t.jsx)("td",{className:"p-2",children:e.totalVideos||0})]},a))})]})})]}),c&&(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Upload Results"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-green-400 text-2xl font-bold",children:c.success}),(0,t.jsx)("div",{className:"text-green-300 text-sm",children:"Successfully Created"})]}),c.duplicates>0&&(0,t.jsxs)("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-yellow-400 text-2xl font-bold",children:c.duplicates}),(0,t.jsx)("div",{className:"text-yellow-300 text-sm",children:"Skipped (Duplicates)"})]}),c.failed>0&&(0,t.jsxs)("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-red-400 text-2xl font-bold",children:c.failed}),(0,t.jsx)("div",{className:"text-red-300 text-sm",children:"Failed"})]})]}),c.errors.length>0&&(0,t.jsxs)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-red-400 font-bold mb-2",children:"Errors:"}),(0,t.jsxs)("ul",{className:"text-red-300 text-sm space-y-1",children:[c.errors.slice(0,10).map((e,a)=>(0,t.jsxs)("li",{children:["• ",e]},a)),c.errors.length>10&&(0,t.jsxs)("li",{className:"text-red-400",children:["... and ",c.errors.length-10," more errors"]})]})]})]}),(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Upload Instructions"]}),(0,t.jsxs)("div",{className:"text-white/80 space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-white mb-2",children:"Required Fields:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"name:"})," User's full name"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"email:"})," Valid email address (must be unique)"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"mobile:"})," 10-digit mobile number (must be unique)"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"password:"})," Password for the user account (min 6 characters)"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-white mb-2",children:"Optional Fields:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"plan:"})," User's plan (Trial, Starter, Basic, Premium, Gold, Platinum, Diamond) - defaults to Trial"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"activeDays:"})," Number of active days remaining - defaults to 1"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"wallet:"})," Wallet balance in rupees - defaults to 0"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"totalVideos:"})," Total videos watched - defaults to 0"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"referredBy:"})," Referral code of the person who referred this user"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"referralCode:"})," User's own referral code (MYN0001, MYN0002, etc.) - auto-generated if not provided"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"quickVideoAdvantage:"})," Whether user has quick video advantage (true/false) - defaults to false"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"quickVideoAdvantageDays:"})," Number of days for quick advantage (1-365) - only if quickVideoAdvantage is true"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"quickVideoAdvantageSeconds:"})," Video duration in seconds during advantage (1-420) - defaults to 30"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"quickVideoAdvantageGrantedBy:"})," Admin who granted the advantage - optional"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-white mb-2",children:"Important Notes:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsx)("li",{children:"Each user will get a sequential referral code (MYN0001, MYN0002, etc.)"}),(0,t.jsx)("li",{children:"Firebase Authentication accounts will be created automatically"}),(0,t.jsx)("li",{children:"Duplicate emails or mobile numbers will be skipped"}),(0,t.jsx)("li",{children:"Users can login immediately with their email and password"}),(0,t.jsx)("li",{children:"All wallet balances and video counts will be preserved"})]})]})]})]})]})})}},8418:(e,a,s)=>{Promise.resolve().then(s.bind(s,1187))}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>a(8418)),_N_E=e.O()}]);