(()=>{var e={};e.id=6664,e.ids=[6664],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},9704:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\reset-daily-tracking\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\reset-daily-tracking\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12309:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(65239),i=s(48088),n=s(88170),o=s.n(n),a=s(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(r,l);let d={children:["",{children:["admin",{children:["reset-daily-tracking",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9704)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\reset-daily-tracking\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\reset-daily-tracking\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/reset-daily-tracking/page",pathname:"/admin/reset-daily-tracking",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,r,s)=>{"use strict";s.d(r,{Cn:()=>u,db:()=>c,j2:()=>d});var t=s(67989),i=s(63385),n=s(75535),o=s(70146),a=s(24791);let l=(0,t.Dk)().length?(0,t.Sx)():(0,t.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),d=(0,i.xI)(l),c=(0,n.aU)(l);(0,o.c7)(l);let u=(0,a.Uz)(l,"us-central1")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53631:(e,r,s)=>{Promise.resolve().then(s.bind(s,9704))},53934:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o});var t=s(60687),i=s(43210),n=s(3582);function o(){let[e,r]=(0,i.useState)(!1),[s,o]=(0,i.useState)(!1),[a,l]=(0,i.useState)(null),d=async()=>{if(confirm("Are you sure you want to reset daily increment tracking? This will allow the daily increment to run again today.")){r(!0),l(null);try{let e=await (0,n.PU)();l({type:"reset",...e})}catch(e){console.error("Error resetting tracking:",e),l({type:"reset",success:!1,error:e.message})}finally{r(!1)}}},c=async()=>{if(confirm("Are you sure you want to run the daily active days increment now?")){o(!0),l(null);try{let e=await (0,n.Oe)();l({type:"run",...e})}catch(e){console.error("Error running daily increment:",e),l({type:"run",success:!1,error:e.message})}finally{o(!1)}}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Reset Daily Increment Tracking"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-yellow-800 mb-2",children:"⚠️ Admin Tool - Use with Caution"}),(0,t.jsx)("p",{className:"text-yellow-700",children:"This tool resets the daily increment tracking system. Use this if the daily active days increment ran but didn't actually increment users' active days (showing \"0 incremented, X skipped\")."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-blue-800 mb-3",children:"Step 1: Reset Tracking"}),(0,t.jsx)("p",{className:"text-blue-700 mb-4",children:"Reset the daily increment tracking to allow it to run again today."}),(0,t.jsx)("button",{onClick:d,disabled:e,className:"w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:e?"Resetting...":"Reset Tracking"})]}),(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-green-800 mb-3",children:"Step 2: Run Daily Increment"}),(0,t.jsx)("p",{className:"text-green-700 mb-4",children:"Manually trigger the daily active days increment process."}),(0,t.jsx)("button",{onClick:c,disabled:s,className:"w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Running...":"Run Daily Increment"})]})]}),a&&(0,t.jsxs)("div",{className:`border rounded-lg p-4 ${a.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:[(0,t.jsxs)("h3",{className:`text-lg font-semibold mb-2 ${a.success?"text-green-800":"text-red-800"}`,children:["reset"===a.type?"Reset":"Daily Increment"," Result"]}),a.success?(0,t.jsx)("div",{className:a.success?"text-green-700":"text-red-700",children:"reset"===a.type?(0,t.jsxs)("p",{children:["✅ Successfully reset tracking for ",a.resetCount," users"]}):(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{children:"✅ Daily increment completed:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside mt-2",children:[(0,t.jsxs)("li",{children:["Incremented: ",a.incrementedCount," users"]}),(0,t.jsxs)("li",{children:["Skipped: ",a.skippedCount," users"]}),(0,t.jsxs)("li",{children:["Errors: ",a.errorCount," users"]})]})]})}):(0,t.jsxs)("p",{className:"text-red-700",children:["❌ Error: ",a.error]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"\uD83D\uDCCB Instructions"}),(0,t.jsxs)("ol",{className:"list-decimal list-inside text-gray-700 space-y-1",children:[(0,t.jsx)("li",{children:'First, click "Reset Tracking" to clear today\'s tracking data'}),(0,t.jsx)("li",{children:'Then, click "Run Daily Increment" to manually trigger the process'}),(0,t.jsx)("li",{children:"Check the result to see how many users were actually incremented"}),(0,t.jsx)("li",{children:'If still showing "0 incremented", check the console logs for more details'})]})]})]})]})})})}},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},90583:(e,r,s)=>{Promise.resolve().then(s.bind(s,53934))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6204,6958,8441,3582],()=>s(12309));module.exports=t})();