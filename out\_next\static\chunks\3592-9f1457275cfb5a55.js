"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3592],{3592:(e,t,a)=>{a.d(t,{AX:()=>Q,COLLECTIONS:()=>i,FIELD_NAMES:()=>n,GA:()=>b,Gl:()=>H,HY:()=>v,I0:()=>g,II:()=>I,IK:()=>T,Kc:()=>A,Oe:()=>h,PU:()=>V,Q6:()=>R,QD:()=>Y,Ss:()=>P,_f:()=>G,addTransaction:()=>u,b6:()=>d,bA:()=>j,calculateUserActiveDays:()=>m,checkAndRunDailyProcess:()=>D,fP:()=>Z,getLiveActiveDays:()=>w,getPlanValidityDays:()=>N,getUserData:()=>c,getVideoCountData:()=>l,getWalletData:()=>s,gj:()=>k,gx:()=>E,i8:()=>J,iA:()=>O,isUserPlanExpired:()=>B,mm:()=>S,mv:()=>F,pl:()=>y,pu:()=>$,submitBatchVideos:()=>f,ul:()=>L,updateUserActiveDays:()=>p,updateWalletBalance:()=>x,w1:()=>C,wD:()=>q,wT:()=>_,x4:()=>K,xj:()=>z,z8:()=>M,zb:()=>U});var r=a(5317),o=a(6104);let n={name:"name",email:"email",mobile:"mobile",referralCode:"referralCode",referredBy:"referredBy",referralBonusCredited:"referralBonusCredited",plan:"plan",planExpiry:"planExpiry",activeDays:"activeDays",joinedDate:"joinedDate",wallet:"wallet",bankAccountHolderName:"bankAccountHolderName",bankAccountNumber:"bankAccountNumber",bankIfscCode:"bankIfscCode",bankName:"bankName",bankDetailsUpdated:"bankDetailsUpdated",totalVideos:"totalVideos",todayVideos:"todayVideos",lastVideoDate:"lastVideoDate",videoDuration:"videoDuration",quickVideoAdvantage:"quickVideoAdvantage",quickVideoAdvantageExpiry:"quickVideoAdvantageExpiry",quickVideoAdvantageDays:"quickVideoAdvantageDays",quickVideoAdvantageRemainingDays:"quickVideoAdvantageRemainingDays",quickVideoAdvantageSeconds:"quickVideoAdvantageSeconds",quickVideoAdvantageGrantedBy:"quickVideoAdvantageGrantedBy",quickVideoAdvantageGrantedAt:"quickVideoAdvantageGrantedAt",manuallySetActiveDays:"manuallySetActiveDays",lastActiveDaysUpdate:"lastActiveDaysUpdate",type:"type",amount:"amount",date:"date",status:"status",description:"description",userId:"userId"},i={users:"users",transactions:"transactions",withdrawals:"withdrawals",plans:"plans",settings:"settings",notifications:"notifications",adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function c(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserData:",e),null;let l=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(l.exists()){var t,a,c,s;let e=l.data(),r={name:String(e[n.name]||""),email:String(e[n.email]||""),mobile:String(e[n.mobile]||""),referralCode:String(e[n.referralCode]||""),referredBy:String(e[n.referredBy]||""),plan:String(e[n.plan]||"Trial"),planExpiry:(null==(t=e[n.planExpiry])?void 0:t.toDate())||null,activeDays:Number(e[n.activeDays]||0),joinedDate:(null==(a=e[n.joinedDate])?void 0:a.toDate())||new Date,videoDuration:Number(e[n.videoDuration]||("Trial"===e[n.plan]?30:300)),quickVideoAdvantage:!!e[n.quickVideoAdvantage],quickVideoAdvantageExpiry:(null==(c=e[n.quickVideoAdvantageExpiry])?void 0:c.toDate())||null,quickVideoAdvantageDays:Number(e[n.quickVideoAdvantageDays]||0),quickVideoAdvantageRemainingDays:Number(e[n.quickVideoAdvantageRemainingDays]||0),quickVideoAdvantageSeconds:Number(e[n.quickVideoAdvantageSeconds]||30),quickVideoAdvantageGrantedBy:String(e[n.quickVideoAdvantageGrantedBy]||""),quickVideoAdvantageGrantedAt:(null==(s=e[n.quickVideoAdvantageGrantedAt])?void 0:s.toDate())||null};return console.log("getUserData result:",r),r}return null}catch(e){return console.error("Error getting user data:",e),null}}async function s(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getWalletData:",e),{wallet:0};let t=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(t.exists()){let e=t.data(),a={wallet:Number(e[n.wallet]||0)};return console.log("getWalletData result:",a),a}return{wallet:0}}catch(e){return console.error("Error getting wallet data:",e),{wallet:0}}}async function l(e){try{let a=(0,r.H9)(o.db,i.users,e),c=await (0,r.x7)(a);if(c.exists()){var t;let o=c.data(),i=o[n.totalVideos]||0,s=o[n.todayVideos]||0,l=null==(t=o[n.lastVideoDate])?void 0:t.toDate(),d=new Date;if((!l||l.toDateString()!==d.toDateString())&&s>0){console.log("\uD83D\uDD04 Resetting daily video count for user ".concat(e," (was ").concat(s,")")),await (0,r.mZ)(a,{[n.todayVideos]:0}),s=0;try{await p(e)}catch(e){console.error("Error updating active days during daily reset:",e)}try{await D()}catch(e){console.error("Error in daily process check:",e)}}return{totalVideos:i,todayVideos:s,remainingVideos:Math.max(0,50-s)}}return{totalVideos:0,todayVideos:0,remainingVideos:50}}catch(e){throw console.error("Error getting video count data:",e),e}}async function d(e,t){try{await (0,r.mZ)((0,r.H9)(o.db,i.users,e),t)}catch(e){throw console.error("Error updating user data:",e),e}}async function u(e,t){try{let a={[n.userId]:e,[n.type]:t.type,[n.amount]:t.amount,[n.description]:t.description,[n.status]:t.status||"completed",[n.date]:r.Dc.now()};await (0,r.gS)((0,r.collection)(o.db,i.transactions),a)}catch(e){throw console.error("Error adding transaction:",e),e}}async function g(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getTransactions:",e),[];let a=(0,r.P)((0,r.collection)(o.db,i.transactions),(0,r._M)(n.userId,"==",e),(0,r.AB)(t)),c=(await (0,r.getDocs)(a)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[n.date])?void 0:t.toDate()}});return c.sort((e,t)=>{let a=e.date||new Date(0);return(t.date||new Date(0)).getTime()-a.getTime()}),c}catch(e){return console.error("Error getting transactions:",e),[]}}async function y(e){try{let t=(0,r.P)((0,r.collection)(o.db,i.users),(0,r._M)(n.referredBy,"==",e));return(await (0,r.getDocs)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.joinedDate])?void 0:t.toDate()}})}catch(e){throw console.error("Error getting referrals:",e),e}}async function f(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{var a;let c,s;if(50!==t)throw Error("Invalid batch size: ".concat(t,". Expected exactly 50 videos."));let l=new Date,d=(0,r.H9)(o.db,i.users,e),u=await (0,r.x7)(d);if(!u.exists())throw Error("User not found");let g=u.data(),y=null==(a=g[n.lastVideoDate])?void 0:a.toDate(),f=g[n.todayVideos]||0,v=g[n.totalVideos]||0;if(f>=50)throw Error("Daily video limit already reached. Cannot submit more videos today.");return(!y||y.toDateString()!==l.toDateString())&&f>0?(console.log("\uD83D\uDD04 Resetting daily count and submitting batch for user ".concat(e)),c=50):c=Math.min(f+50,50),s=v+50,await (0,r.mZ)(d,{[n.totalVideos]:s,[n.todayVideos]:c,[n.lastVideoDate]:r.Dc.fromDate(l)}),console.log("✅ Batch submission successful for user ".concat(e,": +50 videos (Total: ").concat(s,", Today: ").concat(c,")")),{totalVideos:s,todayVideos:c,videosAdded:50}}catch(e){throw console.error("Error submitting batch videos:",e),e}}async function v(e){try{let t=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(t,{[n.todayVideos]:0}),console.log("✅ Reset daily video count for user ".concat(e))}catch(e){throw console.error("Error resetting daily video count:",e),e}}async function w(e){try{console.log("\uD83D\uDD0D Getting live active days for user ".concat(e));let t=await c(e);if(!t)return console.error("User data not found for live active days:",e),1;let a=t.activeDays||1;return console.log("\uD83D\uDCCA Live active days for user ".concat(e,": ").concat(a)),a}catch(e){return console.error("Error getting live active days:",e),1}}async function m(e){try{let t=await c(e);if(!t)return console.error("User data not found for active days calculation:",e),1;let r=new Date,o=1;if("Trial"===t.plan){let a=t.joinedDate;a?"function"==typeof a.toDate?a=a.toDate():a instanceof Date||(a=new Date(a),isNaN(a.getTime())&&(console.warn("User ".concat(e," has invalid joinedDate, using current date")),a=new Date)):(console.warn("User ".concat(e," has no joinedDate, using current date")),a=new Date);let n=Math.floor((r.getTime()-a.getTime())/864e5);o=Math.max(1,n+1),console.log("\uD83D\uDCC5 Trial user ".concat(e," active days calculation:"),{joinedDate:a.toISOString(),today:r.toISOString(),daysDifference:n,activeDays:o})}else{let n=t.planExpiry?new Date(t.planExpiry.getTime()-24*N(t.plan)*36e5):t.joinedDate||new Date,i=Math.floor((r.getTime()-n.getTime())/864e5),{isAdminLeaveDay:c,isUserOnLeave:s}=await a.e(9567).then(a.bind(a,9567)),l=0;for(let t=0;t<=i;t++){let a=new Date(n.getTime()+24*t*36e5),r=await c(a),o=await s(e,a);(r||o)&&l++}o=Math.max(1,i-l+1)}return o}catch(e){return console.error("Error calculating user active days:",e),1}}async function p(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let a,s=await c(e);if(!s)return console.error("User data not found for active days update:",e),1;let l=(0,r.H9)(o.db,i.users,e),d=(await (0,r.x7)(l)).data(),u=(null==d?void 0:d.manuallySetActiveDays)||!1;if(u&&!t)return console.log("⏭️ Skipping active days auto-update for user ".concat(e," - manually set by admin (current: ").concat(s.activeDays,")")),s.activeDays||1;u&&t?(console.log("⚠️ Force update requested but active days manually set for user ".concat(e," - keeping current value")),a=s.activeDays||1):a=await m(e);let g=s.activeDays||0;return a!==g&&(console.log("\uD83D\uDCC5 Updating active days for user ".concat(e,": ").concat(g," → ").concat(a)),await (0,r.mZ)(l,{[n.activeDays]:a})),a}catch(e){throw console.error("Error updating user active days:",e),e}}async function D(){try{var e;let t=new Date().toDateString(),a=await (0,r.x7)((0,r.H9)(o.db,"system","dailyReset")),n=a.exists()?null==(e=a.data())?void 0:e.lastResetDate:null;if(n&&n===t)return console.log("⏭️ Daily process already completed today"),null;{let e=1;if(n){let t=new Date(n),a=new Date().getTime()-t.getTime();e=Math.floor(a/864e5)}console.log("\uD83C\uDF05 Running daily process for all users (".concat(e," day(s) catchup)..."));let a=await h();try{let n=(0,r.H9)(o.db,"system","dailyReset");await (0,r.BN)(n,{lastResetDate:t,lastResetTimestamp:r.Dc.now(),lastResult:a,daysCaughtUp:e,triggeredBy:"automatic_daily_check"},{merge:!0}),console.log("✅ Daily process completed for ".concat(e," day(s):"),a)}catch(e){console.error("Error updating daily process tracking:",e)}return a}}catch(e){throw console.error("Error in centralized daily process:",e),e}}async function h(){try{console.log("\uD83C\uDF05 Starting daily active days increment...");let t=new Date,c=t.toDateString(),{isAdminLeaveDay:s}=await a.e(9567).then(a.bind(a,9567));if(await s(t))return console.log("⏸️ Skipping active days increment - Admin leave day"),{incrementedCount:0,skippedCount:0,errorCount:0,reason:"Admin leave day"};let l=await (0,r.getDocs)((0,r.collection)(o.db,i.users)),d=0,g=0,y=0;for(let s of l.docs)try{var e;let l=s.data(),y=s.id,f=null==(e=l[n.lastActiveDaysUpdate])?void 0:e.toDate();if(f&&f.toDateString()===c){console.log("⏭️ Skipping user ".concat(y," - Already updated today (").concat(f.toDateString(),")")),g++;continue}let{isUserOnLeave:v}=await a.e(9567).then(a.bind(a,9567));if(await v(y,t)){console.log("⏸️ Skipping active days increment for user ".concat(y," - User leave day")),await (0,r.mZ)((0,r.H9)(o.db,i.users,y),{[n.lastActiveDaysUpdate]:r.Dc.fromDate(t)}),g++;continue}let w=l[n.activeDays]||1,m=w+1;console.log("\uD83D\uDCC5 Daily active days increment for user ".concat(y,": ").concat(w," → ").concat(m));let p={[n.activeDays]:m,[n.lastActiveDaysUpdate]:r.Dc.fromDate(t)},D=l[n.quickVideoAdvantage]||!1,h=l[n.quickVideoAdvantageRemainingDays]||0;if(D&&h>0){let e=h-1;if(p[n.quickVideoAdvantageRemainingDays]=e,e<=0){p[n.quickVideoAdvantage]=!1,p[n.quickVideoAdvantageExpiry]=null,console.log("⏰ Quick video advantage expired for user ".concat(y));try{await u(y,{type:"quick_advantage_expired",amount:0,description:"Quick video advantage expired (time limit reached)"})}catch(e){console.error("Error adding expiry transaction:",e)}}else console.log("⏰ Quick video advantage for user ".concat(y,": ").concat(h," → ").concat(e," days remaining"))}await (0,r.mZ)((0,r.H9)(o.db,i.users,y),p),d++,console.log("\uD83D\uDCC5 Updated active days for user ".concat(y,": ").concat(w," → ").concat(m))}catch(e){console.error("Error updating active days for user ".concat(s.id,":"),e),y++}return console.log("✅ Daily active days increment completed: ".concat(d," incremented, ").concat(g," skipped, ").concat(y," errors")),{incrementedCount:d,skippedCount:g,errorCount:y}}catch(e){throw console.error("Error in daily active days increment:",e),e}}async function b(){try{console.log("\uD83D\uDD04 Starting quick video advantage system migration...");let t=await (0,r.getDocs)((0,r.collection)(o.db,i.users)),a=0,c=0,s=0;for(let l of t.docs)try{var e;let t=l.data(),s=l.id;if(!t[n.quickVideoAdvantage]||void 0!==t[n.quickVideoAdvantageRemainingDays]){c++;continue}let d=0,u=null==(e=t[n.quickVideoAdvantageExpiry])?void 0:e.toDate();if(u){let e=new Date,t=u.getTime()-e.getTime();d=Math.max(0,Math.ceil(t/864e5))}let g={[n.quickVideoAdvantageRemainingDays]:d};d<=0&&(g[n.quickVideoAdvantage]=!1,g[n.quickVideoAdvantageExpiry]=null),await (0,r.mZ)((0,r.H9)(o.db,i.users,s),g),a++,console.log("✅ Migrated user ".concat(s,": ").concat(d," days remaining"))}catch(e){console.error("Error migrating user ".concat(l.id,":"),e),s++}return console.log("✅ Quick video advantage migration completed: ".concat(a," migrated, ").concat(c," skipped, ").concat(s," errors")),{migratedCount:a,skippedCount:c,errorCount:s}}catch(e){throw console.error("Error migrating quick video advantage system:",e),e}}async function k(){try{console.log("\uD83D\uDD27 Starting to fix all users active days...");let e=await (0,r.getDocs)((0,r.collection)(o.db,i.users)),t=0,a=0;for(let r of e.docs)try{await p(r.id,!0),t++}catch(e){console.error("Error fixing active days for user ".concat(r.id,":"),e),a++}return console.log("✅ Fixed active days for ".concat(t," users, ").concat(a," errors")),{fixedCount:t,errorCount:a}}catch(e){throw console.error("Error fixing all users active days:",e),e}}async function A(){try{console.log("\uD83D\uDD04 Starting to recalculate all users active days with centralized formula...");let e=await (0,r.getDocs)((0,r.collection)(o.db,i.users)),t=0,a=0;for(let c of e.docs)try{let e=c.data(),a=c.id,s=await m(a),l=e.activeDays||0;s!==l&&(await (0,r.mZ)((0,r.H9)(o.db,i.users,a),{[n.activeDays]:s,[n.manuallySetActiveDays]:!1}),console.log("\uD83D\uDCC5 Recalculated active days for user ".concat(a,": ").concat(l," → ").concat(s)),t++)}catch(e){console.error("Error recalculating active days for user ".concat(c.id,":"),e),a++}return console.log("✅ Recalculated active days for ".concat(t," users, ").concat(a," errors")),{recalculatedCount:t,errorCount:a}}catch(e){throw console.error("Error recalculating all users active days:",e),e}}async function E(){try{var e;console.log("\uD83D\uDE80 Starting forced daily process catchup for all users...");let t=await (0,r.x7)((0,r.H9)(o.db,"system","dailyReset")),a=t.exists()?null==(e=t.data())?void 0:e.lastResetDate:null;console.log("Last daily process date:",a);let n=await h(),i=new Date().toDateString(),c=(0,r.H9)(o.db,"system","dailyReset");return await (0,r.BN)(c,{lastResetDate:i,lastResetTimestamp:r.Dc.now(),lastResult:n,forcedCatchup:!0,forcedCatchupTimestamp:r.Dc.now()},{merge:!0}),console.log("✅ Forced daily process catchup completed:",n),n}catch(e){throw console.error("Error in forced daily process catchup:",e),e}}async function V(){try{console.log("\uD83D\uDD04 Resetting daily increment tracking...");let e=(0,r.H9)(o.db,"system","dailyReset");await (0,r.BN)(e,{lastResetDate:null,lastResetTimestamp:null,resetBy:"admin_manual_reset",resetTime:r.Dc.now()},{merge:!0});let t=await (0,r.getDocs)((0,r.collection)(o.db,i.users)),a=(0,r.wP)(o.db),c=0;for(let e of t.docs)a.update(e.ref,{[n.lastActiveDaysUpdate]:null}),c++;return await a.commit(),console.log("✅ Reset daily increment tracking for ".concat(c," users")),{success:!0,resetCount:c}}catch(e){throw console.error("Error resetting daily increment tracking:",e),e}}async function q(){try{console.log("\uD83D\uDD04 Resetting all users lastActiveDaysUpdate field...");let e=await (0,r.getDocs)((0,r.collection)(o.db,i.users)),t=0,a=0;for(let c of e.docs)try{let e=c.id;await (0,r.mZ)((0,r.H9)(o.db,i.users,e),{[n.lastActiveDaysUpdate]:null}),t++,console.log("✅ Reset lastActiveDaysUpdate for user ".concat(e))}catch(e){console.error("Error resetting lastActiveDaysUpdate for user ".concat(c.id,":"),e),a++}return console.log("✅ Reset lastActiveDaysUpdate for ".concat(t," users, ").concat(a," errors")),{resetCount:t,errorCount:a}}catch(e){throw console.error("Error resetting all users lastActiveDaysUpdate:",e),e}}async function x(e,t){try{let a=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(a,{[n.wallet]:(0,r.GV)(t)})}catch(e){throw console.error("Error updating wallet balance:",e),e}}async function S(e,t){try{if(!e||"string"!=typeof e)throw Error("Invalid userId provided");var a=t;let{accountHolderName:c,accountNumber:s,ifscCode:l,bankName:d}=a;if(!c||c.trim().length<2)throw Error("Account holder name must be at least 2 characters long");if(!s||!/^\d{9,18}$/.test(s.trim()))throw Error("Account number must be 9-18 digits");if(!l||!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(l.trim().toUpperCase()))throw Error("Invalid IFSC code format (e.g., SBIN0001234)");if(!d||d.trim().length<2)throw Error("Bank name must be at least 2 characters long");let u=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(u,{[n.bankAccountHolderName]:t.accountHolderName.trim(),[n.bankAccountNumber]:t.accountNumber.trim(),[n.bankIfscCode]:t.ifscCode.trim().toUpperCase(),[n.bankName]:t.bankName.trim(),[n.bankDetailsUpdated]:r.Dc.now()}),console.log("Bank details saved successfully for user:",e)}catch(e){throw console.error("Error saving bank details:",e),e}}async function U(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getBankDetails:",e),null;let t=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(t.exists()){let e=t.data();if(e[n.bankAccountNumber]){let t={accountHolderName:String(e[n.bankAccountHolderName]||""),accountNumber:String(e[n.bankAccountNumber]||""),ifscCode:String(e[n.bankIfscCode]||""),bankName:String(e[n.bankName]||"")};return console.log("getBankDetails result found"),t}}return console.log("No bank details found for user"),null}catch(e){return console.error("Error getting bank details:",e),null}}function N(e){return({Trial:2,Starter:30,Basic:30,Premium:30,Gold:30,Platinum:30,Diamond:30,499:30,1499:30,2999:30,3999:30,5999:30,9999:30})[e]||2}async function B(e){try{let t=await c(e);if(!t)return{expired:!0,reason:"User data not found"};let a=await w(e);if("Trial"===t.plan){let e=Math.max(0,3-a);return{expired:a>=3,reason:a>=3?"Trial period expired":void 0,daysLeft:e,activeDays:a}}if(t.planExpiry){let e=new Date,r=e>t.planExpiry,o=r?0:Math.ceil((t.planExpiry.getTime()-e.getTime())/864e5);return{expired:r,reason:r?"Plan subscription expired":void 0,daysLeft:o,activeDays:a}}let r=Math.max(0,31-a),o=a>=31;return{expired:o,reason:o?"Plan validity period exceeded - active days limit (31) reached":void 0,daysLeft:r,activeDays:a}}catch(e){return console.error("Error checking plan expiry:",e),{expired:!0,reason:"Error checking plan status"}}}async function I(e,t,a){try{let c=(0,r.H9)(o.db,i.users,e);if("Trial"===t)await (0,r.mZ)(c,{[n.planExpiry]:null});else{let o;if(a)o=a;else{let e=N(t),a=new Date;o=new Date(a.getTime()+24*e*36e5)}await (0,r.mZ)(c,{[n.planExpiry]:r.Dc.fromDate(o)}),console.log("Updated plan expiry for user ".concat(e," to ").concat(o.toDateString()))}}catch(e){throw console.error("Error updating plan expiry:",e),e}}async function T(e,t,a){try{if("Trial"!==t||"Trial"===a)return void console.log("Referral bonus only applies when upgrading from Trial to paid plan");console.log("Processing referral bonus for user ".concat(e," upgrading from ").concat(t," to ").concat(a));let c=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(!c.exists())return void console.log("User not found");let s=c.data(),l=s[n.referredBy],d=s[n.referralBonusCredited];if(!l)return void console.log("User was not referred by anyone, skipping bonus processing");if(d)return void console.log("Referral bonus already credited for this user, skipping");console.log("Finding referrer with code:",l);let g=(0,r.P)((0,r.collection)(o.db,i.users),(0,r._M)(n.referralCode,"==",l),(0,r.AB)(1)),y=await (0,r.getDocs)(g);if(y.empty)return void console.log("Referral code not found:",l);let f=y.docs[0].id,v={Trial:0,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200}[a]||0;if(console.log("Found referrer: ".concat(f,", bonus amount: ₹").concat(v)),v>0){await x(f,v);let t=(0,r.H9)(o.db,i.users,f);await (0,r.mZ)(t,{[n.totalVideos]:(0,r.GV)(50)});let c=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(c,{[n.referralBonusCredited]:!0}),await u(f,{type:"referral_bonus",amount:v,description:"Referral bonus for ".concat(a," plan upgrade + 50 bonus videos (User: ").concat(s[n.name],")")}),console.log("✅ Referral bonus processed: ₹".concat(v," + 50 videos for referrer ").concat(f))}else console.log("No bonus amount calculated, skipping")}catch(e){console.error("❌ Error processing referral bonus:",e)}}async function R(e){try{var t;let a=await c(e);if(!a)return{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1};let r=!!(t=a).quickVideoAdvantage&&(void 0!==t.quickVideoAdvantageRemainingDays?t.quickVideoAdvantageRemainingDays>0:!!t.quickVideoAdvantageExpiry&&new Date<t.quickVideoAdvantageExpiry),o=a.videoDuration;return r?o=a.quickVideoAdvantageSeconds||30:o&&"Trial"!==a.plan||(o=({Trial:30,Starter:300,Basic:300,Premium:300,Gold:180,Platinum:120,Diamond:60})[a.plan]||30),{videoDuration:o,earningPerBatch:({Trial:10,Starter:25,Basic:75,Premium:150,Gold:200,Platinum:250,Diamond:400})[a.plan]||10,plan:a.plan,hasQuickAdvantage:r,quickAdvantageExpiry:a.quickVideoAdvantageExpiry}}catch(e){return console.error("Error getting user video settings:",e),{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1}}}async function C(e,t,a){let c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30;try{if(t<=0||t>365)throw Error("Days must be between 1 and 365");if(c<1||c>420)throw Error("Seconds must be between 1 and 420 (7 minutes)");let s=new Date,l=new Date(s.getTime()+24*t*36e5),d=(0,r.H9)(o.db,i.users,e);return await (0,r.mZ)(d,{[n.quickVideoAdvantage]:!0,[n.quickVideoAdvantageExpiry]:r.Dc.fromDate(l),[n.quickVideoAdvantageDays]:t,[n.quickVideoAdvantageRemainingDays]:t,[n.quickVideoAdvantageSeconds]:c,[n.quickVideoAdvantageGrantedBy]:a,[n.quickVideoAdvantageGrantedAt]:r.Dc.fromDate(s)}),console.log("Granted quick video advantage to user ".concat(e," for ").concat(t," days until ").concat(l.toDateString())),await u(e,{type:"quick_advantage_granted",amount:0,description:"Quick video advantage granted for ".concat(t," days by ").concat(a)}),{success:!0,expiry:l}}catch(e){throw console.error("Error granting quick video advantage:",e),e}}async function _(e,t){try{let a=(0,r.H9)(o.db,i.users,e);return await (0,r.mZ)(a,{[n.quickVideoAdvantage]:!1,[n.quickVideoAdvantageExpiry]:null,[n.quickVideoAdvantageDays]:0,[n.quickVideoAdvantageRemainingDays]:0,[n.quickVideoAdvantageSeconds]:30,[n.quickVideoAdvantageGrantedBy]:"",[n.quickVideoAdvantageGrantedAt]:null}),console.log("Removed quick video advantage from user ".concat(e)),await u(e,{type:"quick_advantage_removed",amount:0,description:"Quick video advantage removed by ".concat(t)}),{success:!0}}catch(e){throw console.error("Error removing quick video advantage:",e),e}}async function H(e,t){try{let a=[1,10,30].includes(t),c=t>=60&&t<=420;if(!a&&!c)throw Error("Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration");let s=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(s,{[n.videoDuration]:t}),console.log("Updated video duration for user ".concat(e," to ").concat(t," seconds"))}catch(e){throw console.error("Error updating user video duration:",e),e}}async function M(e){try{let t={title:e.title,message:e.message,type:e.type,targetUsers:e.targetUsers,userIds:e.userIds||[],createdAt:r.Dc.now(),createdBy:e.createdBy};console.log("Adding notification to Firestore:",t);let a=await (0,r.gS)((0,r.collection)(o.db,i.notifications),t);console.log("Notification added successfully with ID:",a.id);let n=await (0,r.x7)(a);return n.exists()?console.log("Notification verified in database:",n.data()):console.warn("Notification not found after adding"),a.id}catch(e){throw console.error("Error adding notification:",e),e}}async function P(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;try{let a,n;if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserNotifications:",e),[];console.log("Loading notifications for user: ".concat(e));try{let e=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.My)("createdAt","desc"),(0,r.AB)(t));a=await (0,r.getDocs)(e),console.log("Found ".concat(a.docs.length," notifications for all users"))}catch(n){console.warn("Error querying all users notifications, trying without orderBy:",n);let e=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.AB)(t));a=await (0,r.getDocs)(e)}try{let a=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.My)("createdAt","desc"),(0,r.AB)(t));n=await (0,r.getDocs)(a),console.log("Found ".concat(n.docs.length," notifications for specific user"))}catch(c){console.warn("Error querying specific user notifications, trying without orderBy:",c);let a=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.AB)(t));n=await (0,r.getDocs)(a)}let c=[];a.docs.forEach(e=>{var t;c.push({id:e.id,...e.data(),createdAt:(null==(t=e.data().createdAt)?void 0:t.toDate())||new Date})}),n.docs.forEach(e=>{var t;c.push({id:e.id,...e.data(),createdAt:(null==(t=e.data().createdAt)?void 0:t.toDate())||new Date})}),c.sort((e,t)=>t.createdAt.getTime()-e.createdAt.getTime());let s=c.slice(0,t);return console.log("Returning ".concat(s.length," total notifications for user")),s}catch(e){return console.error("Error getting user notifications:",e),[]}}async function G(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{let t=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r.My)("createdAt","desc"),(0,r.AB)(e));return(await (0,r.getDocs)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),createdAt:(null==(t=e.data().createdAt)?void 0:t.toDate())||new Date}})}catch(e){return console.error("Error getting all notifications:",e),[]}}async function Z(e){try{if(!e||"string"!=typeof e)throw Error("Invalid notification ID provided");console.log("Deleting notification:",e),await (0,r.kd)((0,r.H9)(o.db,i.notifications,e)),console.log("Notification deleted successfully")}catch(e){throw console.error("Error deleting notification:",e),e}}async function j(e,t){try{let a=JSON.parse(localStorage.getItem("read_notifications_".concat(t))||"[]");a.includes(e)||(a.push(e),localStorage.setItem("read_notifications_".concat(t),JSON.stringify(a)))}catch(e){console.error("Error marking notification as read:",e)}}function F(e,t){try{return JSON.parse(localStorage.getItem("read_notifications_".concat(t))||"[]").includes(e)}catch(e){return console.error("Error checking notification read status:",e),!1}}function L(e,t){try{let a=JSON.parse(localStorage.getItem("read_notifications_".concat(t))||"[]");return e.filter(e=>!a.includes(e.id)).length}catch(e){return console.error("Error getting unread notification count:",e),0}}async function Q(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUnreadNotifications:",e),[];console.log("Loading unread notifications for user: ".concat(e));let t=await P(e,50),a=JSON.parse(localStorage.getItem("read_notifications_".concat(e))||"[]"),r=t.filter(e=>e.id&&!a.includes(e.id));return console.log("Found ".concat(r.length," unread notifications")),r}catch(e){return console.error("Error getting unread notifications:",e),[]}}async function O(e){try{return(await Q(e)).length>0}catch(e){return console.error("Error checking for unread notifications:",e),!1}}async function W(e){try{let t=(0,r.P)((0,r.collection)(o.db,i.withdrawals),(0,r._M)("userId","==",e),(0,r._M)("status","==","pending"),(0,r.AB)(1));return!(await (0,r.getDocs)(t)).empty}catch(e){return console.error("Error checking pending withdrawals:",e),!1}}async function Y(e){try{let t=await c(e);if(!t)return{allowed:!1,reason:"Unable to verify user information. Please try again."};if("Trial"===t.plan)return{allowed:!1,reason:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals."};let r=await B(e);if(r.expired)return{allowed:!1,reason:"Your plan has expired and withdrawals are disabled. ".concat(r.reason)};if(await W(e))return{allowed:!1,reason:"You have a pending withdrawal request. Please wait for it to be processed before submitting a new request."};let o=new Date,n=o.getHours();if(n<10||n>=18)return{allowed:!1,reason:"Withdrawals are only allowed between 10:00 AM to 6:00 PM"};let{isAdminLeaveDay:i}=await a.e(9567).then(a.bind(a,9567));if(await i(o))return{allowed:!1,reason:"Withdrawals are not allowed on admin leave/holiday days"};let{isUserOnLeave:s}=await a.e(9567).then(a.bind(a,9567));if(await s(e,o))return{allowed:!1,reason:"Withdrawals are not allowed on your leave days"};return{allowed:!0}}catch(e){return console.error("Error checking withdrawal allowed:",e),{allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."}}}async function z(e,t,a){try{if(t<50)throw Error("Minimum withdrawal amount is ₹50");let c=await (0,r.c4)(o.db,async c=>{let s=(0,r.H9)(o.db,i.users,e),l=await c.get(s);if(!l.exists())throw Error("User not found");let d=l.data()[n.wallet]||0,u=await Y(e);if(!u.allowed)throw Error(u.reason);if(d<t)throw Error("Insufficient wallet balance. Available: ₹".concat(d,", Requested: ₹").concat(t));let g=d-t;c.update(s,{[n.wallet]:g});let y={userId:e,amount:t,bankDetails:a,status:"pending",date:r.Dc.now(),createdAt:r.Dc.now()},f=(0,r.H9)((0,r.collection)(o.db,i.withdrawals));c.set(f,y);let v={userId:e,type:"withdrawal_request",amount:-t,description:"Withdrawal request submitted - ₹".concat(t," debited from wallet"),date:r.Dc.now(),status:"pending",balanceAfter:g},w=(0,r.H9)((0,r.collection)(o.db,i.transactions));return c.set(w,v),f.id});return console.log("✅ Withdrawal request created successfully: ".concat(c)),c}catch(e){throw console.error("Error creating withdrawal request:",e),e}}async function J(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;try{let a=(0,r.P)((0,r.collection)(o.db,i.withdrawals),(0,r._M)("userId","==",e),(0,r.My)("date","desc"),(0,r.AB)(t));return(await (0,r.getDocs)(a)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}})}catch(e){return console.error("Error getting user withdrawals:",e),[]}}async function K(){try{try{let e=(0,r.collection)(o.db,i.users),t=((await (0,r.d_)(e)).data().count+1).toString().padStart(4,"0");return"MYN".concat(t)}catch(a){console.warn("Failed to get count from server, using fallback method:",a);let e=Date.now().toString().slice(-4),t=Math.random().toString(36).substring(2,4).toUpperCase();return"MYN".concat(e).concat(t)}}catch(t){console.error("Error generating unique referral code:",t);let e=Date.now().toString().slice(-4);return"MYN".concat(e)}}async function $(){return K()}}}]);