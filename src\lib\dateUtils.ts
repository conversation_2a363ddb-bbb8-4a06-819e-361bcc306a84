/**
 * Utility functions for handling date conversions, especially Firestore Timestamps
 */

/**
 * Safely converts a Firestore Timestamp or any date-like value to a JavaScript Date object
 * @param dateValue - The date value to convert (Firestore Timestamp, Date, string, or number)
 * @returns A JavaScript Date object
 */
export function safeToDate(dateValue: any): Date {
  // Debug logging to understand what we're receiving
  console.log('safeToDate received:', dateValue, 'type:', typeof dateValue)

  // If it's already a Date object, return it
  if (dateValue instanceof Date) {
    console.log('✅ Valid Date object:', dateValue)
    return dateValue
  }

  // If it's a Firestore Timestamp with toDate method
  if (dateValue && typeof dateValue === 'object' && typeof dateValue.toDate === 'function') {
    try {
      const converted = dateValue.toDate()
      console.log('✅ Converted Firestore Timestamp:', converted)
      return converted
    } catch (error) {
      console.warn('❌ Failed to convert Firestore Timestamp:', error)
    }
  }

  // If it's a valid date string or number
  if (dateValue && (typeof dateValue === 'string' || typeof dateValue === 'number')) {
    const parsed = new Date(dateValue)
    if (!isNaN(parsed.getTime())) {
      console.log('✅ Parsed string/number to date:', parsed)
      return parsed
    }
  }

  // For null/undefined values, return current date temporarily for debugging
  // TODO: Change back to placeholder date once we identify the root cause
  if (dateValue === null || dateValue === undefined) {
    console.warn('⚠️ Null/undefined date value - using current date for debugging')
    return new Date() // Temporary: use current date to see if this fixes the display
  }

  // For other invalid values, also use current date temporarily
  console.warn('⚠️ Invalid date value:', dateValue, '- using current date for debugging')
  return new Date() // Temporary: use current date to see if this fixes the display
}

/**
 * Formats a date for display in local format
 * @param dateValue - The date value to format
 * @returns Formatted date string
 */
export function formatDisplayDate(dateValue: any): string {
  const date = safeToDate(dateValue)
  return date.toLocaleDateString()
}

/**
 * Formats a time for display in local format
 * @param dateValue - The date value to format
 * @returns Formatted time string
 */
export function formatDisplayTime(dateValue: any): string {
  const date = safeToDate(dateValue)
  return date.toLocaleTimeString()
}

/**
 * Formats a date and time for display in local format
 * @param dateValue - The date value to format
 * @returns Formatted date and time string
 */
export function formatDisplayDateTime(dateValue: any): string {
  const date = safeToDate(dateValue)
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`
}

/**
 * Converts an array of objects with date fields to ensure all dates are JavaScript Date objects
 * @param items - Array of objects that may contain date fields
 * @param dateFields - Array of field names that contain dates (defaults to common date field names)
 * @returns Array with converted date fields
 */
export function convertDatesInArray<T>(items: T[], dateFields: string[] = ['date', 'createdAt', 'updatedAt', 'requestDate']): T[] {
  return items.map(item => {
    const converted = { ...item } as any

    dateFields.forEach(field => {
      if (converted[field]) {
        converted[field] = safeToDate(converted[field])
      }
    })

    return converted
  })
}

/**
 * Safely formats a string field by ensuring it's not null/undefined
 * @param value - The string value to format
 * @param defaultValue - Default value if the input is null/undefined
 * @returns Formatted string
 */
export function safeStringFormat(value: any, defaultValue: string = 'Unknown'): string {
  if (!value || typeof value !== 'string') {
    return defaultValue
  }
  return value
}

/**
 * Safely capitalizes the first letter of a string
 * @param value - The string to capitalize
 * @param defaultValue - Default value if the input is null/undefined
 * @returns Capitalized string
 */
export function safeCapitalize(value: any, defaultValue: string = 'Unknown'): string {
  const str = safeStringFormat(value, defaultValue)
  if (str === defaultValue) {
    return str
  }
  return str.charAt(0).toUpperCase() + str.slice(1)
}
