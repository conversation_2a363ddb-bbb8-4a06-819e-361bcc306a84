(()=>{var e={};e.id=581,e.ids=[581],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10952:(e,t,a)=>{Promise.resolve().then(a.bind(a,67517))},12599:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\withdrawals\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\withdrawals\\page.tsx","default")},15256:(e,t,a)=>{"use strict";function s(e){if(e instanceof Date)return e;if(e&&"object"==typeof e&&"function"==typeof e.toDate)return e.toDate();if(e&&("string"==typeof e||"number"==typeof e)){let t=new Date(e);if(!isNaN(t.getTime()))return t}return console.warn("Invalid date value provided to safeToDate:",e),new Date}function r(e){return s(e).toLocaleDateString()}function i(e){return s(e).toLocaleTimeString()}function n(e,t="Unknown"){let a=function(e,t="Unknown"){return e&&"string"==typeof e?e:t}(e,t);return a===t?a:a.charAt(0).toUpperCase()+a.slice(1)}a.d(t,{NI:()=>i,cI:()=>n,g1:()=>r,xi:()=>s})},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26239:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=a(65239),r=a(48088),i=a(88170),n=a.n(i),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c={children:["",{children:["admin",{children:["withdrawals",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,12599)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\withdrawals\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\withdrawals\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/withdrawals/page",pathname:"/admin/withdrawals",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},47400:(e,t,a)=>{Promise.resolve().then(a.bind(a,12599))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67517:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(60687),r=a(43210),i=a(85814),n=a.n(i),o=a(87979),l=a(83475),c=a(3582),d=a(92617),u=a(15256),m=a(77567);function p(){console.log("\uD83D\uDD0D AdminWithdrawalsPage component loaded");let{user:e,loading:t,isAdmin:i}=(0,o.wC)(),[p,x]=(0,r.useState)([]),[h,g]=(0,r.useState)(!0),[f,w]=(0,r.useState)("pending"),[b,y]=(0,r.useState)(""),[j,N]=(0,r.useState)(null),[v,D]=(0,r.useState)(!1),[C,A]=(0,r.useState)([]),[k,S]=(0,r.useState)(!1),[E,q]=(0,r.useState)(""),[U,B]=(0,r.useState)(!1),[P,L]=(0,r.useState)(!1),I=async()=>{try{g(!0);try{console.log("\uD83D\uDE80 Loading withdrawals with optimized function...");let e=(await d.x8.getAdminWithdrawals(P)).map(e=>({id:e.id,userId:e.userId,userName:e.userName,userEmail:e.userEmail,userMobile:e.userMobile,userPlan:e.userPlan,userActiveDays:e.userActiveDays,walletBalance:e.walletBalance,amount:e.amount,bankDetails:e.bankDetails,requestDate:(0,u.xi)(e.requestDate),status:e.status,adminNotes:e.adminNotes}));x(e),console.log(`✅ Loaded ${e.length} withdrawals via optimized function`)}catch(i){let e;console.warn("⚠️ Optimized function failed, using fallback:",i);let{getAllPendingWithdrawals:t,getAllWithdrawals:s}=await a.e(3772).then(a.bind(a,91391));P?(console.log("\uD83D\uDCCB Loading ALL withdrawals (all statuses)..."),e=await s()):(console.log("⏳ Loading ALL PENDING withdrawals..."),e=await t());let r=[];for(let t of e)try{if(!t.userId||!t.amount){console.warn("Withdrawal missing required fields:",t);continue}let e=await (0,c.getUserData)(t.userId),s=await (0,c.getWalletData)(t.userId);if(e){let{calculateUserActiveDays:i}=await Promise.resolve().then(a.bind(a,3582)),n=await i(t.userId);r.push({id:t.id,userId:t.userId,userName:e.name,userEmail:e.email,userMobile:e.mobile||"",userPlan:e.plan,userActiveDays:n,walletBalance:s?.wallet||0,amount:t.amount,bankDetails:t.bankDetails||{accountHolderName:"",accountNumber:"",ifscCode:"",bankName:""},requestDate:(0,u.xi)(t.date),status:t.status||"pending",adminNotes:t.adminNotes})}}catch(e){console.error(`Error loading user data for withdrawal ${t.id}:`,e)}x(r),console.log(`✅ Loaded ${r.length} withdrawals with fallback method`)}}catch(e){console.error("Error loading withdrawals:",e),x([]),m.A.fire({icon:"error",title:"Error",text:"Failed to load withdrawals. Please try again."})}finally{g(!1)}},T=async(e,t,s)=>{try{let{updateWithdrawalStatus:r}=await a.e(3772).then(a.bind(a,91391));await r(e,t,s),x(a=>a.map(a=>a.id===e?{...a,status:t,adminNotes:s}:a)),m.A.fire({icon:"success",title:"Status Updated",text:`Withdrawal has been ${t}.`,timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error updating withdrawal status:",e),m.A.fire({icon:"error",title:"Update Failed",text:"Failed to update withdrawal status. Please try again."})}},z=e=>{m.A.fire({title:"Approve Withdrawal",text:`Approve withdrawal of ₹${e.amount} for ${e.userName}?`,icon:"question",showCancelButton:!0,confirmButtonColor:"#10b981",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Approve",cancelButtonText:"Cancel"}).then(t=>{t.isConfirmed&&T(e.id,"approved")})},M=e=>{m.A.fire({title:"Reject Withdrawal",text:"Please provide a reason for rejection:",input:"textarea",inputPlaceholder:"Enter rejection reason...",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Reject",cancelButtonText:"Cancel",inputValidator:e=>{if(!e)return"Please provide a reason for rejection"}}).then(t=>{t.isConfirmed&&T(e.id,"rejected",t.value)})},F=e=>{m.A.fire({title:"Mark as Completed",text:`Mark withdrawal of ₹${e.amount} as completed?`,icon:"question",showCancelButton:!0,confirmButtonColor:"#3b82f6",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Complete",cancelButtonText:"Cancel"}).then(t=>{t.isConfirmed&&T(e.id,"completed")})},V=e=>{C.includes(e)?(A(t=>t.filter(t=>t!==e)),S(!1)):(A(t=>[...t,e]),C.length+1===$.length&&S(!0))},R=async()=>{let e;if(0===C.length)return void m.A.fire({icon:"warning",title:"No Selection",text:"Please select at least one withdrawal to update."});if(!E)return void m.A.fire({icon:"warning",title:"No Action Selected",text:"Please select an action to perform."});let t="approved"===E?"approve":"rejected"===E?"reject":"completed"===E?"mark as completed":E;if((e="rejected"===E?await m.A.fire({title:`Bulk ${t.charAt(0).toUpperCase()+t.slice(1)}`,text:"Please provide a reason for rejection:",input:"textarea",inputPlaceholder:"Enter rejection reason...",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:`${t.charAt(0).toUpperCase()+t.slice(1)} ${C.length} withdrawals`,cancelButtonText:"Cancel",inputValidator:e=>{if(!e)return"Please provide a reason for rejection"}}):await m.A.fire({title:`Bulk ${t.charAt(0).toUpperCase()+t.slice(1)}`,text:`Are you sure you want to ${t} ${C.length} selected withdrawals?`,icon:"question",showCancelButton:!0,confirmButtonColor:"approved"===E?"#10b981":"#3b82f6",cancelButtonColor:"#6b7280",confirmButtonText:`Yes, ${t.charAt(0).toUpperCase()+t.slice(1)}`,cancelButtonText:"Cancel"})).isConfirmed)try{for(let t of(B(!0),C))await T(t,E,e.value);A([]),S(!1),q(""),m.A.fire({icon:"success",title:"Bulk Update Complete",text:`Successfully ${t}ed ${C.length} withdrawals.`,timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error in bulk update:",e),m.A.fire({icon:"error",title:"Bulk Update Failed",text:"Some withdrawals could not be updated. Please try again."})}finally{B(!1)}},$=p.filter(e=>{let t=!P||!f||e.status===f,a=!b||String(e.userName||"").toLowerCase().includes(b.toLowerCase())||String(e.userEmail||"").toLowerCase().includes(b.toLowerCase())||String(e.userMobile||"").toLowerCase().includes(b.toLowerCase());return t&&a}),O=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),W=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},_=e=>{switch(e.toLowerCase()){case"trial":default:return"bg-gray-100 text-gray-800";case"starter":return"bg-green-100 text-green-800";case"premium":return"bg-blue-100 text-blue-800";case"gold":return"bg-yellow-100 text-yellow-800";case"platinum":return"bg-purple-100 text-purple-800";case"diamond":return"bg-pink-100 text-pink-800"}};return(console.log("\uD83D\uDD0D Render check - loading:",t,"dataLoading:",h,"isAdmin:",i),t)?(console.log("\uD83D\uDD0D Showing auth loading screen"),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Checking authentication..."})]})})):i?h?(console.log("\uD83D\uDD0D Showing data loading screen"),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading withdrawals..."})]})})):(console.log("\uD83D\uDD0D Rendering main component"),(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(n(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Withdrawal Requests"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[P?"Viewing all withdrawals":"Viewing pending withdrawals only","- No pagination, all data loaded"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",$.length,C.length>0&&(0,s.jsxs)("span",{className:"ml-2 text-blue-600 font-medium",children:["(",C.length," selected)"]})]}),(0,s.jsxs)("button",{onClick:()=>{let e=p.filter(e=>"pending"===e.status);if(0===e.length)return void m.A.fire({icon:"warning",title:"No Pending Withdrawals",text:"No pending withdrawals to export."});let t=(0,l.dB)(e);(0,l.Bf)(t,"pending-withdrawals"),m.A.fire({icon:"success",title:"Export Complete",text:`Exported ${e.length} pending withdrawals to CSV file.`,timer:2e3,showConfirmButton:!1})},className:"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-clock mr-2"}),"Export Pending"]}),(0,s.jsxs)("button",{onClick:()=>{if(0===$.length)return void m.A.fire({icon:"warning",title:"No Data",text:"No withdrawals to export."});let e=(0,l.dB)($);(0,l.Bf)(e,"withdrawals"),m.A.fire({icon:"success",title:"Export Complete",text:`Exported ${$.length} withdrawals to CSV file.`,timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export All"]}),(0,s.jsxs)("button",{onClick:()=>{L(!P),w(P?"pending":""),A([]),S(!1)},className:`px-4 py-2 rounded-lg ${P?"bg-orange-500 hover:bg-orange-600 text-white":"bg-blue-500 hover:bg-blue-600 text-white"}`,children:[(0,s.jsx)("i",{className:`fas ${P?"fa-filter":"fa-list"} mr-2`}),P?"Show Pending Only":"Show All Withdrawals"]}),(0,s.jsxs)("button",{onClick:()=>I(),className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,s.jsxs)("div",{className:`grid gap-4 ${P?"grid-cols-1 md:grid-cols-3":"grid-cols-1 md:grid-cols-2"}`,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,s.jsx)("input",{type:"text",value:b,onChange:e=>y(e.target.value),placeholder:"Search user name, email, or mobile...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),P&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status Filter"}),(0,s.jsxs)("select",{value:f,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Status"}),(0,s.jsx)("option",{value:"pending",children:"Pending"}),(0,s.jsx)("option",{value:"approved",children:"Approved"}),(0,s.jsx)("option",{value:"rejected",children:"Rejected"}),(0,s.jsx)("option",{value:"completed",children:"Completed"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsxs)("button",{onClick:()=>{w(P?"":"pending"),y("")},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-eraser mr-2"}),"Clear Filters"]})})]}),!P&&(0,s.jsx)("div",{className:"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-info-circle text-yellow-600 mr-2"}),(0,s.jsxs)("span",{className:"text-sm text-yellow-800",children:["Currently showing ",(0,s.jsx)("strong",{children:"pending withdrawals only"}),'. Use the "Show All Withdrawals" button above to view all statuses.']})]})})]}),C.length>0&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mx-6 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-blue-800 font-medium",children:[(0,s.jsx)("i",{className:"fas fa-check-square mr-2"}),C.length," withdrawal",C.length>1?"s":""," selected"]}),(0,s.jsxs)("select",{value:E,onChange:e=>q(e.target.value),className:"px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Select Action"}),(0,s.jsx)("option",{value:"approved",children:"Approve Selected"}),(0,s.jsx)("option",{value:"rejected",children:"Reject Selected"}),(0,s.jsx)("option",{value:"completed",children:"Mark as Completed"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:R,disabled:!E||U,className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:U?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-bolt mr-2"}),"Apply Action"]})}),(0,s.jsxs)("button",{onClick:()=>{A([]),S(!1),q("")},className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-times mr-2"}),"Clear Selection"]})]})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:k,onChange:()=>{k?(A([]),S(!1)):(A($.map(e=>e.id)),S(!0))},className:"mr-2"}),"Select All"]})}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Days"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet Balance"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Holder"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bank Name"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Number"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"IFSC Code"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Request Date"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:$.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("input",{type:"checkbox",checked:C.includes(e.id),onChange:()=>V(e.id),className:"rounded"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.userMobile||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${_(e.userPlan)}`,children:e.userPlan})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.userActiveDays," days"]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:O(e.walletBalance)})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-lg font-bold text-green-600",children:O(e.amount)})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.bankDetails.accountHolderName||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.bankDetails.bankName||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900 font-mono",children:e.bankDetails.accountNumber||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900 font-mono",children:e.bankDetails.ifscCode||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,u.g1)(e.requestDate)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${W(e.status)}`,children:e.status?e.status.charAt(0).toUpperCase()+e.status.slice(1):"Unknown"})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{N(e),D(!0)},className:"text-blue-600 hover:text-blue-900",children:"View"}),"pending"===e.status&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>z(e),className:"text-green-600 hover:text-green-900",children:"Approve"}),(0,s.jsx)("button",{onClick:()=>M(e),className:"text-red-600 hover:text-red-900",children:"Reject"})]}),"approved"===e.status&&(0,s.jsx)("button",{onClick:()=>F(e),className:"text-blue-600 hover:text-blue-900",children:"Complete"})]})]},e.id))})]})}),(0,s.jsx)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 text-center text-sm text-gray-600",children:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,s.jsxs)("span",{children:["Showing ",p.length," ",P?"total":"pending"," withdrawals"]}),p.length>0&&(0,s.jsxs)("span",{className:"text-green-600 font-medium",children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-1"}),"All loaded in single page"]})]})})]})}),v&&j&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-bold",children:"Withdrawal Details"}),(0,s.jsx)("button",{onClick:()=>D(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times"})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:j.userName}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:j.userEmail})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Mobile Number"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:j.userMobile||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Plan"}),(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${_(j.userPlan)}`,children:j.userPlan})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Active Days"}),(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[j.userActiveDays," days"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Wallet Balance"}),(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:O(j.walletBalance)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Withdrawal Amount"}),(0,s.jsx)("p",{className:"text-lg font-bold text-green-600",children:O(j.amount)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Bank Details"}),(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Account Holder:"})," ",j.bankDetails.accountHolderName]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Bank:"})," ",j.bankDetails.bankName]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Account Number:"})," ",j.bankDetails.accountNumber]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"IFSC Code:"})," ",j.bankDetails.ifscCode]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${W(j.status)}`,children:j.status?j.status.charAt(0).toUpperCase()+j.status.slice(1):"Unknown"})]}),j.adminNotes&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Admin Notes"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:j.adminNotes})]})]})]})})]})):(console.log("\uD83D\uDD0D User is not admin"),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-gray-600",children:"Access denied. Admin privileges required."})})}))}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,a)=>{"use strict";function s(e,t,a){if(!e||0===e.length)return void alert("No data to export");let s=a||Object.keys(e[0]),r=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],i=new Blob(["\uFEFF"+[s.join(","),...e.map(e=>s.map(t=>{let a=e[t];if(null==a)return"";let s=r.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return`"${e}"`}return a instanceof Date?`"${a.toLocaleDateString()}"`:"object"==typeof a&&null!==a&&a.toDate?`"${a.toDate().toLocaleDateString()}"`:s&&("number"==typeof a||!isNaN(Number(a)))?`"${a}"`:"number"==typeof a?a.toString():`"${String(a)}"`}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let e=URL.createObjectURL(i);n.setAttribute("href",e),n.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)}}function r(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":String(e.bankDetails?.accountNumber||""),"IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}))}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>s,Fz:()=>r,Pe:()=>o,dB:()=>n,sL:()=>i})},91645:e=>{"use strict";e.exports=require("net")},92617:(e,t,a)=>{"use strict";a.d(t,{x8:()=>f});var s=a(24791),r=a(33784);let i=(0,s.Qg)(r.Cn,"getUserDashboardData"),n=(0,s.Qg)(r.Cn,"submitVideoBatch"),o=(0,s.Qg)(r.Cn,"processWithdrawalRequest"),l=(0,s.Qg)(r.Cn,"getUserNotifications"),c=(0,s.Qg)(r.Cn,"getUserTransactions"),d=(0,s.Qg)(r.Cn,"getAdminWithdrawals"),u=(0,s.Qg)(r.Cn,"getAdminDashboardStats"),m=(0,s.Qg)(r.Cn,"getAdminUsers"),p=(0,s.Qg)(r.Cn,"getAdminNotifications"),x=(0,s.Qg)(r.Cn,"createAdminNotification");async function h(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",r.Cn.app.options.projectId);let t=await i({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}}async function g(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let f={getDashboardData:async function(e){try{return await h(e)}catch(l){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:s,getVideoCountData:r}=await a.e(3582).then(a.bind(a,3582)),[i,n,o]=await Promise.all([t(e),s(e),r(e)]);return{userData:i,walletData:n,videoData:o}}},submitVideoBatch:async function(e,t=50){try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await n({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await o(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e,t=10){try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await l({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e,t=10,a="all"){try{console.log("\uD83D\uDE80 Using optimized transactions function...");let s=await c({userId:e,limit:t,type:a});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(e=!1){try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",r.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}},getAdminDashboardStats:async function(){try{return await g()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3582),a.e(1391)]).then(a.bind(a,91391));return await e()}},getAdminUsers:async function(e={}){try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await m(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(e=50,t="all"){try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await p({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await x(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",r.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",r.Cn.region);let e=await i({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),!1}}}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[6204,6958,7567,8441,3582,7979],()=>a(26239));module.exports=s})();