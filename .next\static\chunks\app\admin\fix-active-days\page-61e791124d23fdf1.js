(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1561],{1020:(e,s,t)=>{Promise.resolve().then(t.bind(t,6311))},6311:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var i=t(5155),a=t(2115),r=t(6681),n=t(3592),c=t(4752),l=t.n(c);function o(){let{user:e,loading:s}=(0,r.Nu)(),[c,o]=(0,a.useState)(!1),[d,m]=(0,a.useState)(!1),[x,h]=(0,a.useState)(null),u=(null==e?void 0:e.email)==="<EMAIL>",f=async()=>{if(!u)return void l().fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await l().fire({icon:"warning",title:"Fix All Users Active Days",text:"This will recalculate and update active days for all users. This may take a while. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Fix All",cancelButtonText:"Cancel"})).isConfirmed)try{o(!0);let e=await (0,n.gj)();h(e),l().fire({icon:"success",title:"Active Days Fixed!",html:'\n          <div class="text-left">\n            <p><strong>Fixed:</strong> '.concat(e.fixedCount," users</p>\n            <p><strong>Errors:</strong> ").concat(e.errorCount," users</p>\n          </div>\n        "),timer:5e3})}catch(e){console.error("Error fixing active days:",e),l().fire({icon:"error",title:"Error",text:"Failed to fix active days. Check console for details."})}finally{o(!1)}},j=async()=>{if(!u)return void l().fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await l().fire({icon:"warning",title:"Reset All Daily Video Counts",text:"This will reset today's video count to 0 for all users. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Reset All",cancelButtonText:"Cancel"})).isConfirmed)try{m(!0);let{getDocs:e,collection:s}=await Promise.resolve().then(t.bind(t,5317)),{db:i}=await Promise.resolve().then(t.bind(t,6104)),{COLLECTIONS:a}=await Promise.resolve().then(t.bind(t,3592)),r=await e(s(i,a.users)),c=0,o=0;for(let e of r.docs)try{await (0,n.HY)(e.id),c++}catch(s){console.error("Error resetting daily count for user ".concat(e.id,":"),s),o++}l().fire({icon:"success",title:"Daily Counts Reset!",html:'\n          <div class="text-left">\n            <p><strong>Reset:</strong> '.concat(c," users</p>\n            <p><strong>Errors:</strong> ").concat(o," users</p>\n          </div>\n        "),timer:5e3})}catch(e){console.error("Error resetting daily counts:",e),l().fire({icon:"error",title:"Error",text:"Failed to reset daily counts. Check console for details."})}finally{m(!1)}};return s?(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"spinner mb-4"}),(0,i.jsx)("p",{className:"text-white",children:"Loading..."})]})}):u?(0,i.jsx)("div",{className:"min-h-screen p-4",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,i.jsxs)("div",{className:"glass-card p-6",children:[(0,i.jsxs)("h1",{className:"text-2xl font-bold text-white mb-6",children:[(0,i.jsx)("i",{className:"fas fa-tools mr-2"}),"Fix Active Days & Daily Counts"]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,i.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,i.jsx)("i",{className:"fas fa-calendar-check mr-2"}),"Fix Active Days"]}),(0,i.jsx)("p",{className:"text-white/80 mb-4",children:"Recalculates and updates active days for all users based on their plan activation date and leave history."}),(0,i.jsx)("button",{onClick:f,disabled:c,className:"btn-primary ".concat(c?"opacity-50 cursor-not-allowed":""),children:c?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Fixing Active Days..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-wrench mr-2"}),"Fix All Users Active Days"]})})]}),(0,i.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,i.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,i.jsx)("i",{className:"fas fa-redo mr-2"}),"Reset Daily Video Counts"]}),(0,i.jsx)("p",{className:"text-white/80 mb-4",children:"Resets today's video count to 0 for all users. Use this if daily counts are showing incorrect values."}),(0,i.jsx)("button",{onClick:j,disabled:d,className:"btn-secondary ".concat(d?"opacity-50 cursor-not-allowed":""),children:d?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Resetting Daily Counts..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Reset All Daily Counts"]})})]}),x&&(0,i.jsxs)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-lg p-4",children:[(0,i.jsxs)("h3",{className:"text-lg font-semibold text-green-300 mb-2",children:[(0,i.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Last Operation Results"]}),(0,i.jsxs)("div",{className:"text-white",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Fixed:"})," ",x.fixedCount," users"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Errors:"})," ",x.errorCount," users"]})]})]}),(0,i.jsx)("div",{className:"text-center",children:(0,i.jsxs)("a",{href:"/admin",className:"btn-secondary",children:[(0,i.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin Dashboard"]})})]})]})})}):(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"Access Denied"}),(0,i.jsx)("p",{className:"text-white mb-4",children:"Only admin can access this page."}),(0,i.jsx)("a",{href:"/admin",className:"btn-primary",children:"Back to Admin"})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,3592,6681,8441,1684,7358],()=>s(1020)),_N_E=e.O()}]);