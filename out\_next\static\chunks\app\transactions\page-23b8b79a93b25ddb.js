(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3790],{2846:(e,t,a)=>{"use strict";function s(e){if(e instanceof Date)return e;if(e&&"object"==typeof e&&"function"==typeof e.toDate)return e.toDate();if(e&&("string"==typeof e||"number"==typeof e)){let t=new Date(e);if(!isNaN(t.getTime()))return t}return console.warn("Invalid date value provided to safeToDate:",e),new Date}function i(e){return s(e).toLocaleDateString()}function n(e){return s(e).toLocaleTimeString()}function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unknown",a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unknown";return e&&"string"==typeof e?e:t}(e,t);return a===t?a:a.charAt(0).toUpperCase()+a.slice(1)}a.d(t,{NI:()=>n,cI:()=>o,g1:()=>i,xi:()=>s})},3704:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var s=a(5155),i=a(2115),n=a(6874),o=a.n(n),r=a(6681),l=a(3592),c=a(6273),d=a(2846),m=a(4752),u=a.n(m);function h(){let{user:e,loading:t}=(0,r.Nu)(),[a,n]=(0,i.useState)([]),[m,h]=(0,i.useState)([]),[x,f]=(0,i.useState)(!0),[p,g]=(0,i.useState)(1),[w,j]=(0,i.useState)(1),[b,v]=(0,i.useState)({type:"",status:"",dateFrom:"",dateTo:"",searchTerm:""}),[y,N]=(0,i.useState)(!1);(0,i.useEffect)(()=>{e&&C()},[e]),(0,i.useEffect)(()=>{D()},[a,b]);let C=async()=>{try{f(!0);try{console.log("\uD83D\uDE80 Loading transactions with optimized function...");let t=(await c.x8.getUserTransactions(e.uid,100,"all")).map(e=>({...e,date:(0,d.xi)(e.date)}));n(t),console.log("✅ Transactions loaded via optimized function")}catch(a){console.warn("⚠️ Optimized function failed, using fallback:",a);let t=(await (0,l.I0)(e.uid,100)).map(e=>({...e,date:(0,d.xi)(e.date)}));n(t)}}catch(e){console.error("Error loading transactions:",e),u().fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{f(!1)}},D=()=>{let e=[...a];if(b.type&&(e=e.filter(e=>e.type===b.type)),b.status&&(e=e.filter(e=>e.status===b.status)),b.dateFrom){let t=new Date(b.dateFrom);e=e.filter(e=>e.date>=t)}if(b.dateTo){let t=new Date(b.dateTo);t.setHours(23,59,59,999),e=e.filter(e=>e.date<=t)}if(b.searchTerm){let t=b.searchTerm.toLowerCase();e=e.filter(e=>e.description.toLowerCase().includes(t)||e.type.toLowerCase().includes(t))}h(e),j(Math.ceil(e.length/20)),g(1)},E=(e,t)=>{v(a=>({...a,[e]:t}))},T=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),z=e=>{switch(e){case"video_earning":return"fas fa-play-circle text-green-400";case"withdrawal":return"fas fa-download text-red-400";case"bonus":return"fas fa-gift text-yellow-400";case"referral":return"fas fa-users text-blue-400";default:return"fas fa-exchange-alt text-white"}},F=e=>{if(!e||"string"!=typeof e)return"Unknown";switch(e){case"video_earning":return"Video Earning";case"withdrawal":return"Withdrawal";case"bonus":return"Bonus";case"referral":return"Referral";default:return e.charAt(0).toUpperCase()+e.slice(1)}},k=e=>(0,d.cI)(e),A=(p-1)*20,I=m.slice(A,A+20);return t||x?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-white",children:"Loading transactions..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(o(),{href:"/wallet",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Wallet"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Transaction History"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("button",{onClick:()=>N(!y),className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-filter mr-2"}),"Filters"]}),(0,s.jsxs)("button",{onClick:C,className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,s.jsx)("div",{className:"glass-card p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Total Transactions"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-white",children:m.length})]}),(0,s.jsx)("i",{className:"fas fa-list text-blue-400 text-2xl"})]})}),(0,s.jsx)("div",{className:"glass-card p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Total Earned"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-400",children:T(m.filter(e=>e.amount>0).reduce((e,t)=>e+t.amount,0))})]}),(0,s.jsx)("i",{className:"fas fa-arrow-up text-green-400 text-2xl"})]})}),(0,s.jsx)("div",{className:"glass-card p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Total Withdrawn"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-400",children:T(Math.abs(m.filter(e=>e.amount<0).reduce((e,t)=>e+t.amount,0)))})]}),(0,s.jsx)("i",{className:"fas fa-arrow-down text-red-400 text-2xl"})]})}),(0,s.jsx)("div",{className:"glass-card p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"This Month"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:m.filter(e=>{let t=new Date,a=new Date(e.date);return a.getMonth()===t.getMonth()&&a.getFullYear()===t.getFullYear()}).length})]}),(0,s.jsx)("i",{className:"fas fa-calendar text-yellow-400 text-2xl"})]})})]}),y&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-filter mr-2"}),"Filter Transactions"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Search"}),(0,s.jsx)("input",{type:"text",value:b.searchTerm,onChange:e=>E("searchTerm",e.target.value),placeholder:"Search description or type...",className:"form-input"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Type"}),(0,s.jsxs)("select",{value:b.type,onChange:e=>E("type",e.target.value),className:"form-input",children:[(0,s.jsx)("option",{value:"",children:"All Types"}),(0,s.jsx)("option",{value:"video_earning",children:"Video Earning"}),(0,s.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,s.jsx)("option",{value:"bonus",children:"Bonus"}),(0,s.jsx)("option",{value:"referral",children:"Referral"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Status"}),(0,s.jsxs)("select",{value:b.status,onChange:e=>E("status",e.target.value),className:"form-input",children:[(0,s.jsx)("option",{value:"",children:"All Status"}),(0,s.jsx)("option",{value:"completed",children:"Completed"}),(0,s.jsx)("option",{value:"pending",children:"Pending"}),(0,s.jsx)("option",{value:"failed",children:"Failed"}),(0,s.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"From Date"}),(0,s.jsx)("input",{type:"date",value:b.dateFrom,onChange:e=>E("dateFrom",e.target.value),className:"form-input"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"To Date"}),(0,s.jsx)("input",{type:"date",value:b.dateTo,onChange:e=>E("dateTo",e.target.value),className:"form-input"})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsxs)("button",{onClick:()=>{if(0===m.length)return void u().fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=new Blob([["Date,Type,Description,Amount,Status",...m.map(e=>[e.date instanceof Date?e.date.toLocaleDateString():new Date(e.date).toLocaleDateString(),e.type,'"'.concat(e.description,'"'),e.amount,e.status].join(","))].join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="transactions_".concat(new Date().toISOString().split("T")[0],".csv"),a.click(),window.URL.revokeObjectURL(t)},className:"btn-primary w-full",disabled:0===m.length,children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]})})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("button",{onClick:()=>{v({type:"",status:"",dateFrom:"",dateTo:"",searchTerm:""})},className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-times mr-2"}),"Clear Filters"]}),(0,s.jsxs)("span",{className:"text-white/60 text-sm flex items-center",children:["Showing ",m.length," of ",a.length," transactions"]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-history mr-2"}),"Transactions"]}),m.length>0&&(0,s.jsxs)("p",{className:"text-white/60 text-sm",children:["Page ",p," of ",w]})]}),0===m.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("i",{className:"fas fa-receipt text-white/30 text-6xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 text-lg mb-2",children:"No transactions found"}),(0,s.jsx)("p",{className:"text-white/40 text-sm",children:0===a.length?"You haven't made any transactions yet":"Try adjusting your filters"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"hidden md:block overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-white/20",children:[(0,s.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Date & Time"}),(0,s.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Type"}),(0,s.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Description"}),(0,s.jsx)("th",{className:"text-right text-white font-medium py-3 px-2",children:"Amount"}),(0,s.jsx)("th",{className:"text-center text-white font-medium py-3 px-2",children:"Status"})]})}),(0,s.jsx)("tbody",{children:I.map(e=>(0,s.jsxs)("tr",{className:"border-b border-white/10 hover:bg-white/5",children:[(0,s.jsxs)("td",{className:"py-4 px-2",children:[(0,s.jsx)("div",{className:"text-white text-sm",children:(0,d.g1)(e.date)}),(0,s.jsx)("div",{className:"text-white/60 text-xs",children:(0,d.NI)(e.date)})]}),(0,s.jsx)("td",{className:"py-4 px-2",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"".concat(z(e.type)," mr-2")}),(0,s.jsx)("span",{className:"text-white text-sm",children:F(e.type)})]})}),(0,s.jsx)("td",{className:"py-4 px-2",children:(0,s.jsx)("p",{className:"text-white text-sm",children:e.description})}),(0,s.jsx)("td",{className:"py-4 px-2 text-right",children:(0,s.jsxs)("p",{className:"font-bold text-sm ".concat(e.amount>0?"text-green-400":"text-red-400"),children:[e.amount>0?"+":"",T(e.amount)]})}),(0,s.jsx)("td",{className:"py-4 px-2 text-center",children:(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-400/20 text-green-400":"pending"===e.status?"bg-yellow-400/20 text-yellow-400":"failed"===e.status?"bg-red-400/20 text-red-400":"bg-gray-400/20 text-gray-400"),children:k(e.status)})})]},e.id))})]})}),(0,s.jsx)("div",{className:"md:hidden space-y-3",children:I.map(e=>(0,s.jsxs)("div",{className:"p-4 bg-white/10 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"".concat(z(e.type)," mr-2")}),(0,s.jsx)("span",{className:"text-white font-medium text-sm",children:F(e.type)})]}),(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-400/20 text-green-400":"pending"===e.status?"bg-yellow-400/20 text-yellow-400":"failed"===e.status?"bg-red-400/20 text-red-400":"bg-gray-400/20 text-gray-400"),children:k(e.status)})]}),(0,s.jsx)("p",{className:"text-white text-sm mb-2",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-white/60 text-xs",children:[(0,d.g1)(e.date)," ",(0,d.NI)(e.date)]}),(0,s.jsxs)("p",{className:"font-bold text-sm ".concat(e.amount>0?"text-green-400":"text-red-400"),children:[e.amount>0?"+":"",T(e.amount)]})]})]},e.id))}),w>1&&(0,s.jsxs)("div",{className:"flex items-center justify-center mt-6 gap-2",children:[(0,s.jsx)("button",{onClick:()=>g(e=>Math.max(1,e-1)),disabled:1===p,className:"glass-button px-3 py-2 text-white disabled:opacity-50",children:(0,s.jsx)("i",{className:"fas fa-chevron-left"})}),(0,s.jsx)("div",{className:"flex gap-1",children:Array.from({length:Math.min(5,w)},(e,t)=>{let a;return a=w<=5||p<=3?t+1:p>=w-2?w-4+t:p-2+t,(0,s.jsx)("button",{onClick:()=>g(a),className:"px-3 py-2 text-sm rounded ".concat(p===a?"bg-blue-500 text-white":"glass-button text-white"),children:a},a)})}),(0,s.jsx)("button",{onClick:()=>g(e=>Math.min(w,e+1)),disabled:p===w,className:"glass-button px-3 py-2 text-white disabled:opacity-50",children:(0,s.jsx)("i",{className:"fas fa-chevron-right"})})]})]})]})]})}},6273:(e,t,a)=>{"use strict";a.d(t,{x8:()=>g});var s=a(2144),i=a(6104);let n=(0,s.Qg)(i.Cn,"getUserDashboardData"),o=(0,s.Qg)(i.Cn,"submitVideoBatch"),r=(0,s.Qg)(i.Cn,"processWithdrawalRequest"),l=(0,s.Qg)(i.Cn,"getUserNotifications"),c=(0,s.Qg)(i.Cn,"getUserTransactions"),d=(0,s.Qg)(i.Cn,"getAdminWithdrawals"),m=(0,s.Qg)(i.Cn,"getAdminDashboardStats"),u=(0,s.Qg)(i.Cn,"getAdminUsers"),h=(0,s.Qg)(i.Cn,"getAdminNotifications"),x=(0,s.Qg)(i.Cn,"createAdminNotification");async function f(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await n({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}}async function p(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await m({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let g={getDashboardData:async function(e){try{return await f(e)}catch(l){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:s,getVideoCountData:i}=await a.e(3592).then(a.bind(a,3592)),[n,o,r]=await Promise.all([t(e),s(e),i(e)]);return{userData:n,walletData:o,videoData:r}}},submitVideoBatch:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await o({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await r(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await l({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{console.log("\uD83D\uDE80 Using optimized transactions function...");let s=await c({userId:e,limit:t,type:a});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}},getAdminDashboardStats:async function(){try{return await p()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3592),a.e(6779)]).then(a.bind(a,6779));return await e()}},getAdminUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await u(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await h({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await x(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",i.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",i.Cn.region);let e=await n({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),!1}}}},9137:(e,t,a)=>{Promise.resolve().then(a.bind(a,3704))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>t(9137)),_N_E=e.O()}]);