"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5897,8278],{6104:(e,a,t)=>{t.d(a,{Cn:()=>u,db:()=>h,j2:()=>s});var c=t(3915),o=t(3004),r=t(5317),n=t(858),l=t(2144);let i=(0,c.Dk)().length?(0,c.Sx)():(0,c.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),s=(0,o.xI)(i),h=(0,r.aU)(i);(0,n.c7)(i);let u=(0,l.Uz)(i,"us-central1")},8278:(e,a,t)=>{t.d(a,{checkVersionAndClearCache:()=>l});var c=t(5317),o=t(6104);let r="1.1.0",n="mytube_app_version";async function l(){try{console.log("\uD83D\uDD0D Silently checking app version for cache management...");let a=localStorage.getItem(n),t=r;try{let a=await (0,c.x7)((0,c.H9)(o.db,"system/version"));if(a.exists()){var e;t=(null==(e=a.data())?void 0:e.version)||r}}catch(e){console.warn("Could not fetch server version, using current version:",e)}if(!a||a!==t){console.log("\uD83D\uDD04 Version changed: ".concat(a||"none"," → ").concat(t," - Clearing cache silently..."));try{return await i(),localStorage.setItem(n,t),localStorage.setItem("mytube_last_cache_clear",new Date().toISOString()),console.log("✅ Cache cleared silently due to version update"),setTimeout(()=>{window.location.reload()},1e3),!0}catch(e){return console.error("Silent cache clear failed, continuing normally:",e),localStorage.setItem(n,t),!1}}return console.log("✅ Version unchanged, no cache clearing needed"),!1}catch(e){return console.error("Error checking version:",e),!1}}async function i(){try{console.log("\uD83D\uDD07 Silently clearing application cache...");let e={};if(["firebase:authUser","firebase:host"].forEach(a=>{Object.keys(localStorage).filter(e=>e.includes(a)).forEach(a=>{e[a]=localStorage.getItem(a)})}),localStorage.clear(),Object.entries(e).forEach(e=>{let[a,t]=e;t&&localStorage.setItem(a,t)}),sessionStorage.clear(),"caches"in window)try{let e=await Promise.race([caches.keys(),new Promise((e,a)=>setTimeout(()=>a(Error("Cache keys timeout")),2e3))]);await Promise.race([Promise.all(e.map(e=>caches.delete(e))),new Promise((e,a)=>setTimeout(()=>a(Error("Cache deletion timeout")),2e3))])}catch(e){console.warn("Silent cache clear skipped browser caches:",e)}console.log("✅ Silent cache clear completed")}catch(e){console.warn("Silent cache clear encountered error:",e)}}}}]);