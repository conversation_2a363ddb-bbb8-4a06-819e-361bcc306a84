'use client'

import { useState } from 'react'
import { resetDailyIncrementTracking, dailyActiveDaysIncrement } from '@/lib/dataService'

export default function ResetDailyTrackingPage() {
  const [isResetting, setIsResetting] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleResetTracking = async () => {
    if (!confirm('Are you sure you want to reset daily increment tracking? This will allow the daily increment to run again today.')) {
      return
    }

    setIsResetting(true)
    setResult(null)

    try {
      const resetResult = await resetDailyIncrementTracking()
      setResult({ type: 'reset', ...resetResult })
    } catch (error) {
      console.error('Error resetting tracking:', error)
      setResult({ type: 'reset', success: false, error: (error as Error).message })
    } finally {
      setIsResetting(false)
    }
  }

  const handleRunDailyIncrement = async () => {
    if (!confirm('Are you sure you want to run the daily active days increment now?')) {
      return
    }

    setIsRunning(true)
    setResult(null)

    try {
      const runResult = await dailyActiveDaysIncrement()
      setResult({ type: 'run', ...runResult })
    } catch (error) {
      console.error('Error running daily increment:', error)
      setResult({ type: 'run', success: false, error: (error as Error).message })
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Reset Daily Increment Tracking
          </h1>

          <div className="space-y-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                ⚠️ Admin Tool - Use with Caution
              </h3>
              <p className="text-yellow-700">
                This tool resets the daily increment tracking system. Use this if the daily active days increment 
                ran but didn't actually increment users' active days (showing "0 incremented, X skipped").
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-3">
                  Step 1: Reset Tracking
                </h3>
                <p className="text-blue-700 mb-4">
                  Reset the daily increment tracking to allow it to run again today.
                </p>
                <button
                  onClick={handleResetTracking}
                  disabled={isResetting}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isResetting ? 'Resetting...' : 'Reset Tracking'}
                </button>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-green-800 mb-3">
                  Step 2: Run Daily Increment
                </h3>
                <p className="text-green-700 mb-4">
                  Manually trigger the daily active days increment process.
                </p>
                <button
                  onClick={handleRunDailyIncrement}
                  disabled={isRunning}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isRunning ? 'Running...' : 'Run Daily Increment'}
                </button>
              </div>
            </div>

            {result && (
              <div className={`border rounded-lg p-4 ${
                result.success 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <h3 className={`text-lg font-semibold mb-2 ${
                  result.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {result.type === 'reset' ? 'Reset' : 'Daily Increment'} Result
                </h3>
                
                {result.success ? (
                  <div className={result.success ? 'text-green-700' : 'text-red-700'}>
                    {result.type === 'reset' ? (
                      <p>✅ Successfully reset tracking for {result.resetCount} users</p>
                    ) : (
                      <div>
                        <p>✅ Daily increment completed:</p>
                        <ul className="list-disc list-inside mt-2">
                          <li>Incremented: {result.incrementedCount} users</li>
                          <li>Skipped: {result.skippedCount} users</li>
                          <li>Errors: {result.errorCount} users</li>
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-red-700">❌ Error: {result.error}</p>
                )}
              </div>
            )}

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                📋 Instructions
              </h3>
              <ol className="list-decimal list-inside text-gray-700 space-y-1">
                <li>First, click "Reset Tracking" to clear today's tracking data</li>
                <li>Then, click "Run Daily Increment" to manually trigger the process</li>
                <li>Check the result to see how many users were actually incremented</li>
                <li>If still showing "0 incremented", check the console logs for more details</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
