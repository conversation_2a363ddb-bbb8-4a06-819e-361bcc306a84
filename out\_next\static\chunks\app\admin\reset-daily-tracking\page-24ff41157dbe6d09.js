(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6664],{337:(e,s,r)=>{Promise.resolve().then(r.bind(r,5698))},5698:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(5155),l=r(2115),n=r(3592);function i(){let[e,s]=(0,l.useState)(!1),[r,i]=(0,l.useState)(!1),[a,c]=(0,l.useState)(null),d=async()=>{if(confirm("Are you sure you want to reset daily increment tracking? This will allow the daily increment to run again today.")){s(!0),c(null);try{let e=await (0,n.PU)();c({type:"reset",...e})}catch(e){console.error("Error resetting tracking:",e),c({type:"reset",success:!1,error:e.message})}finally{s(!1)}}},o=async()=>{if(confirm("Are you sure you want to run the daily active days increment now?")){i(!0),c(null);try{let e=await (0,n.Oe)();c({type:"run",...e})}catch(e){console.error("Error running daily increment:",e),c({type:"run",success:!1,error:e.message})}finally{i(!1)}}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Reset Daily Increment Tracking"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-yellow-800 mb-2",children:"⚠️ Admin Tool - Use with Caution"}),(0,t.jsx)("p",{className:"text-yellow-700",children:"This tool resets the daily increment tracking system. Use this if the daily active days increment ran but didn't actually increment users' active days (showing \"0 incremented, X skipped\")."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-blue-800 mb-3",children:"Step 1: Reset Tracking"}),(0,t.jsx)("p",{className:"text-blue-700 mb-4",children:"Reset the daily increment tracking to allow it to run again today."}),(0,t.jsx)("button",{onClick:d,disabled:e,className:"w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:e?"Resetting...":"Reset Tracking"})]}),(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-green-800 mb-3",children:"Step 2: Run Daily Increment"}),(0,t.jsx)("p",{className:"text-green-700 mb-4",children:"Manually trigger the daily active days increment process."}),(0,t.jsx)("button",{onClick:o,disabled:r,className:"w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:r?"Running...":"Run Daily Increment"})]})]}),a&&(0,t.jsxs)("div",{className:"border rounded-lg p-4 ".concat(a.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-2 ".concat(a.success?"text-green-800":"text-red-800"),children:["reset"===a.type?"Reset":"Daily Increment"," Result"]}),a.success?(0,t.jsx)("div",{className:a.success?"text-green-700":"text-red-700",children:"reset"===a.type?(0,t.jsxs)("p",{children:["✅ Successfully reset tracking for ",a.resetCount," users"]}):(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{children:"✅ Daily increment completed:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside mt-2",children:[(0,t.jsxs)("li",{children:["Incremented: ",a.incrementedCount," users"]}),(0,t.jsxs)("li",{children:["Skipped: ",a.skippedCount," users"]}),(0,t.jsxs)("li",{children:["Errors: ",a.errorCount," users"]})]})]})}):(0,t.jsxs)("p",{className:"text-red-700",children:["❌ Error: ",a.error]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"\uD83D\uDCCB Instructions"}),(0,t.jsxs)("ol",{className:"list-decimal list-inside text-gray-700 space-y-1",children:[(0,t.jsx)("li",{children:'First, click "Reset Tracking" to clear today\'s tracking data'}),(0,t.jsx)("li",{children:'Then, click "Run Daily Increment" to manually trigger the process'}),(0,t.jsx)("li",{children:"Check the result to see how many users were actually incremented"}),(0,t.jsx)("li",{children:'If still showing "0 incremented", check the console logs for more details'})]})]})]})]})})})}},6104:(e,s,r)=>{"use strict";r.d(s,{Cn:()=>u,db:()=>o,j2:()=>d});var t=r(3915),l=r(3004),n=r(5317),i=r(858),a=r(2144);let c=(0,t.Dk)().length?(0,t.Sx)():(0,t.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),d=(0,l.xI)(c),o=(0,n.aU)(c);(0,i.c7)(c);let u=(0,a.Uz)(c,"us-central1")}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8818,3592,8441,1684,7358],()=>s(337)),_N_E=e.O()}]);