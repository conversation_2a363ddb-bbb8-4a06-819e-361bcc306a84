(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4317],{891:(e,t,a)=>{Promise.resolve().then(a.bind(a,1451))},1451:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(5155),o=a(2115),s=a(3004),i=a(5317),n=a(6104),c=a(3592);function l(){let[e,t]=(0,o.useState)(""),[a,l]=(0,o.useState)(!1),[u,d]=(0,o.useState)({name:"Test User",email:"",mobile:"9876543210",password:"test123456",confirmPassword:"test123456",referralCode:""}),m=e=>{t(t=>t+e+"\n"),console.log(e)},D=e=>{let{name:t,value:a}=e.target;d(e=>({...e,[t]:a}))},E=async()=>{t(""),l(!0);try{var e,a,r;let t,o="test".concat(Date.now(),"@example.com"),l="Test Registration User",u="9876543210";m("\uD83D\uDE80 Starting registration test..."),m("\uD83D\uDCE7 Email: ".concat(o)),m("\uD83D\uDC64 Name: ".concat(l)),m("\uD83D\uDCF1 Mobile: ".concat(u)),m("\uD83D\uDD27 Firebase Project: ".concat("mytube-india")),m("\n=== STEP 1: Creating Firebase Auth User ===");try{let e=(await (0,s.eJ)(n.j2,o,"test123456")).user;m("✅ Auth user created successfully!"),m("\uD83C\uDD94 UID: ".concat(e.uid)),m("\uD83D\uDCE7 Email: ".concat(e.email)),m("✅ Email Verified: ".concat(e.emailVerified))}catch(e){throw m("❌ Auth creation failed: ".concat(e.message)),m("❌ Auth error code: ".concat(e.code)),e}m("\n=== STEP 2: Waiting for Auth State ==="),await new Promise(e=>setTimeout(e,2e3)),m("✅ Auth state propagated"),m("Current auth user: ".concat(null==(e=n.j2.currentUser)?void 0:e.uid)),m("Auth state matches: ".concat((null==(a=n.j2.currentUser)?void 0:a.uid)===(null==(r=n.j2.currentUser)?void 0:r.uid))),m("\n=== STEP 3: Generating Referral Code ===");try{t=await (0,c.x4)(),m("✅ Generated referral code: ".concat(t))}catch(e){throw m("❌ Referral code generation failed: ".concat(e.message)),e}m("\n=== STEP 4: Preparing User Data ===");let d={[c.FIELD_NAMES.name]:l,[c.FIELD_NAMES.email]:o.toLowerCase(),[c.FIELD_NAMES.mobile]:u,[c.FIELD_NAMES.referralCode]:t,[c.FIELD_NAMES.referredBy]:"",[c.FIELD_NAMES.referralBonusCredited]:!1,[c.FIELD_NAMES.plan]:"Trial",[c.FIELD_NAMES.planExpiry]:null,[c.FIELD_NAMES.activeDays]:1,[c.FIELD_NAMES.joinedDate]:i.Dc.now(),[c.FIELD_NAMES.wallet]:0,[c.FIELD_NAMES.totalVideos]:0,[c.FIELD_NAMES.todayVideos]:0,[c.FIELD_NAMES.lastVideoDate]:null,[c.FIELD_NAMES.videoDuration]:30,status:"active"};m("✅ User data prepared"),m("\uD83D\uDCCA Data keys: ".concat(Object.keys(d).join(", "))),m("\n=== STEP 5: Creating Firestore Document ===");let D=n.j2.currentUser;if(!D)throw m("❌ No current user found"),Error("No current user found");let E=(0,i.H9)(n.db,c.COLLECTIONS.users,D.uid);m("\uD83D\uDCCD Document path: ".concat(E.path)),m("\uD83D\uDD10 Current user UID: ".concat(D.uid)),m("\uD83D\uDCE7 Current user email: ".concat(D.email));try{await (0,i.BN)(E,d),m("✅ Firestore document created successfully!")}catch(e){throw m("❌ Firestore creation failed: ".concat(e.message)),m("❌ Firestore error code: ".concat(e.code)),m("❌ Full error: ".concat(JSON.stringify(e,null,2))),e}m("\n=== STEP 6: Verifying Document ===");try{let e=await (0,i.x7)(E);if(e.exists()){let t=e.data();m("✅ Document verification successful!"),m("\uD83D\uDCCA Document data keys: ".concat(Object.keys(t).join(", "))),m("\uD83D\uDC64 Name: ".concat(t[c.FIELD_NAMES.name])),m("\uD83D\uDCE7 Email: ".concat(t[c.FIELD_NAMES.email])),m("\uD83C\uDFAF Referral Code: ".concat(t[c.FIELD_NAMES.referralCode]))}else throw m("❌ Document was not created properly"),Error("Document verification failed")}catch(e){throw m("❌ Document verification failed: ".concat(e.message)),e}m("\n\uD83C\uDF89 Registration test completed successfully!")}catch(e){m("\n❌ Registration test failed!"),m("Error: ".concat(e.message)),m("Code: ".concat(e.code)),m("Stack: ".concat(e.stack)),console.error("Registration test error:",e)}finally{l(!1)}},p=async e=>{e.preventDefault(),t(""),l(!0);try{let e=u.email||"test".concat(Date.now(),"@example.com");if(m("\uD83D\uDE80 Starting FORM registration test..."),m("\uD83D\uDCE7 Email: ".concat(e)),m("\uD83D\uDC64 Name: ".concat(u.name)),m("\uD83D\uDCF1 Mobile: ".concat(u.mobile)),!u.name||!e||!u.mobile||!u.password)throw Error("Please fill in all required fields");if(u.password!==u.confirmPassword)throw Error("Passwords do not match");let t=(await (0,s.eJ)(n.j2,e,u.password)).user;m("✅ Auth user created: ".concat(t.uid));let a=await (0,c.x4)();m("✅ Referral code: ".concat(a));let r={[c.FIELD_NAMES.name]:u.name.trim(),[c.FIELD_NAMES.email]:e.toLowerCase(),[c.FIELD_NAMES.mobile]:u.mobile,[c.FIELD_NAMES.referralCode]:a,[c.FIELD_NAMES.referredBy]:u.referralCode||"",[c.FIELD_NAMES.referralBonusCredited]:!1,[c.FIELD_NAMES.plan]:"Trial",[c.FIELD_NAMES.planExpiry]:null,[c.FIELD_NAMES.activeDays]:1,[c.FIELD_NAMES.joinedDate]:i.Dc.now(),[c.FIELD_NAMES.wallet]:0,[c.FIELD_NAMES.totalVideos]:0,[c.FIELD_NAMES.todayVideos]:0,[c.FIELD_NAMES.lastVideoDate]:null,[c.FIELD_NAMES.videoDuration]:30,status:"active"},o=(0,i.H9)(n.db,c.COLLECTIONS.users,t.uid);await (0,i.BN)(o,r),m("✅ Document created successfully!"),m("\n\uD83C\uDF89 FORM registration test completed successfully!")}catch(e){m("\n❌ FORM registration test failed!"),m("Error: ".concat(e.message)),m("Code: ".concat(e.code))}finally{l(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"glass-card p-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Automated Test"}),(0,r.jsx)("button",{onClick:E,disabled:a,className:"btn-primary mb-6 w-full",children:a?"Testing Registration...":"Test Registration Process"}),(0,r.jsx)("div",{className:"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto",children:e||"Click the button to test registration process..."})]}),(0,r.jsxs)("div",{className:"glass-card p-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Form Test"}),(0,r.jsxs)("form",{onSubmit:p,className:"space-y-4 mb-6",children:[(0,r.jsx)("input",{type:"text",name:"name",value:u.name,onChange:D,placeholder:"Full Name",className:"form-input",required:!0}),(0,r.jsx)("input",{type:"email",name:"email",value:u.email,onChange:D,placeholder:"Email (leave empty for auto-generated)",className:"form-input"}),(0,r.jsx)("input",{type:"tel",name:"mobile",value:u.mobile,onChange:D,placeholder:"Mobile Number",className:"form-input",required:!0}),(0,r.jsx)("input",{type:"password",name:"password",value:u.password,onChange:D,placeholder:"Password",className:"form-input",required:!0}),(0,r.jsx)("input",{type:"password",name:"confirmPassword",value:u.confirmPassword,onChange:D,placeholder:"Confirm Password",className:"form-input",required:!0}),(0,r.jsx)("input",{type:"text",name:"referralCode",value:u.referralCode,onChange:D,placeholder:"Referral Code (Optional)",className:"form-input"}),(0,r.jsx)("button",{type:"submit",disabled:a,className:"btn-primary w-full",children:a?"Testing...":"Test Form Registration"})]}),(0,r.jsx)("div",{className:"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto",children:e||"Fill the form and submit to test..."})]})]})})})}},6104:(e,t,a)=>{"use strict";a.d(t,{Cn:()=>d,db:()=>u,j2:()=>l});var r=a(3915),o=a(3004),s=a(5317),i=a(858),n=a(2144);let c=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,o.xI)(c),u=(0,s.aU)(c);(0,i.c7)(c);let d=(0,n.Uz)(c,"us-central1")}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8818,3592,8441,1684,7358],()=>t(891)),_N_E=e.O()}]);