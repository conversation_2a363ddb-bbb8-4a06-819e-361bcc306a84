(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1879],{2291:(e,t,a)=>{Promise.resolve().then(a.bind(a,3381))},3381:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var o=a(5155),i=a(2115),n=a(6273),s=a(6681);function r(){let{user:e}=(0,s.hD)(),[t,a]=(0,i.useState)([]),[r,c]=(0,i.useState)(!1),d=(e,t,o,i)=>{a(a=>[...a,{test:e,success:t,data:o,error:i,timestamp:new Date().toISOString()}])},l=async()=>{c(!0),a([]);try{console.log("\uD83D\uDD0D Testing functions availability...");let e=await n.x8.areFunctionsAvailable();d("Functions Availability",e,{available:e})}catch(e){d("Functions Availability",!1,null,e)}if(e)try{console.log("\uD83D\uDD0D Testing dashboard data...");let t=await n.x8.getDashboardData(e.uid);d("Dashboard Data",!0,t)}catch(e){d("Dashboard Data",!1,null,e)}if(e&&["<EMAIL>","<EMAIL>"].includes(e.email||""))try{console.log("\uD83D\uDD0D Testing admin withdrawals...");let e=await n.x8.getAdminWithdrawals(!1);d("Admin Withdrawals",!0,e)}catch(e){d("Admin Withdrawals",!1,null,e)}c(!1)};return(0,o.jsx)("div",{className:"min-h-screen bg-gray-100 p-8",children:(0,o.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Firebase Functions Test"}),(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Connection Status"}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"User:"})," ",e?e.email:"Not logged in"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Project ID:"})," ","mytube-india"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Auth Domain:"})," ","mytube-india.firebaseapp.com"]})]})]}),(0,o.jsx)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:(0,o.jsx)("button",{onClick:l,disabled:r,className:"bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 disabled:opacity-50",children:r?"Testing...":"Test Firebase Functions"})}),t.length>0&&(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Results"}),(0,o.jsx)("div",{className:"space-y-4",children:t.map((e,t)=>(0,o.jsxs)("div",{className:"p-4 rounded border-l-4 ".concat(e.success?"border-green-500 bg-green-50":"border-red-500 bg-red-50"),children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,o.jsx)("h3",{className:"font-semibold",children:e.test}),(0,o.jsx)("span",{className:"px-2 py-1 rounded text-sm ".concat(e.success?"bg-green-200 text-green-800":"bg-red-200 text-red-800"),children:e.success?"SUCCESS":"FAILED"})]}),e.success&&e.data&&(0,o.jsxs)("div",{className:"mt-2",children:[(0,o.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Response Data:"}),(0,o.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40",children:JSON.stringify(e.data,null,2)})]}),!e.success&&e.error&&(0,o.jsxs)("div",{className:"mt-2",children:[(0,o.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Error Details:"}),(0,o.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40",children:JSON.stringify({name:e.error.name,message:e.error.message,code:e.error.code,details:e.error.details},null,2)})]}),(0,o.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:new Date(e.timestamp).toLocaleString()})]},t))})]})]})})}},6273:(e,t,a)=>{"use strict";a.d(t,{x8:()=>w});var o=a(2144),i=a(6104);let n=(0,o.Qg)(i.Cn,"getUserDashboardData"),s=(0,o.Qg)(i.Cn,"submitVideoBatch"),r=(0,o.Qg)(i.Cn,"processWithdrawalRequest"),c=(0,o.Qg)(i.Cn,"getUserNotifications"),d=(0,o.Qg)(i.Cn,"getUserTransactions"),l=(0,o.Qg)(i.Cn,"getAdminWithdrawals"),u=(0,o.Qg)(i.Cn,"getAdminDashboardStats"),m=(0,o.Qg)(i.Cn,"getAdminUsers"),h=(0,o.Qg)(i.Cn,"getAdminNotifications"),f=(0,o.Qg)(i.Cn,"createAdminNotification");async function g(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await n({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}}async function p(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let w={getDashboardData:async function(e){try{return await g(e)}catch(c){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:o,getVideoCountData:i}=await a.e(3592).then(a.bind(a,3592)),[n,s,r]=await Promise.all([t(e),o(e),i(e)]);return{userData:n,walletData:s,videoData:r}}},submitVideoBatch:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await s({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await r(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await c({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{console.log("\uD83D\uDE80 Using optimized transactions function...");let o=await d({userId:e,limit:t,type:a});if(o.data&&"object"==typeof o.data&&"success"in o.data){let e=o.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await l({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}},getAdminDashboardStats:async function(){try{return await p()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3592),a.e(6779)]).then(a.bind(a,6779));return await e()}},getAdminUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await m(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await h({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await f(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",i.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",i.Cn.region);let e=await n({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),!1}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6681,8441,1684,7358],()=>t(2291)),_N_E=e.O()}]);