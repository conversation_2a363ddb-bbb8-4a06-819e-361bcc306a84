(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[581],{2846:(e,t,a)=>{"use strict";function s(e){if(e instanceof Date)return e;if(e&&"object"==typeof e&&"function"==typeof e.toDate)return e.toDate();if(e&&("string"==typeof e||"number"==typeof e)){let t=new Date(e);if(!isNaN(t.getTime()))return t}return console.warn("Invalid date value provided to safeToDate:",e),new Date}function n(e){return s(e).toLocaleDateString()}function i(e){return s(e).toLocaleTimeString()}function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unknown",a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unknown";return e&&"string"==typeof e?e:t}(e,t);return a===t?a:a.charAt(0).toUpperCase()+a.slice(1)}a.d(t,{NI:()=>i,cI:()=>r,g1:()=>n,xi:()=>s})},3571:(e,t,a)=>{Promise.resolve().then(a.bind(a,4128))},3737:(e,t,a)=>{"use strict";function s(e,t,a){if(!e||0===e.length)return void alert("No data to export");let s=a||Object.keys(e[0]),n=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],i=new Blob(["\uFEFF"+[s.join(","),...e.map(e=>s.map(t=>{let a=e[t];if(null==a)return"";let s=n.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):s&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a");if(void 0!==r.download){let e=URL.createObjectURL(i);r.setAttribute("href",e),r.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)}}function n(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function r(e){return e.map(e=>{var t,a,s,n;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(s=e.bankDetails)?void 0:s.accountNumber)||""),"IFSC Code":(null==(n=e.bankDetails)?void 0:n.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>s,Fz:()=>n,Pe:()=>o,dB:()=>r,sL:()=>i})},4128:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(5155),n=a(2115),i=a(6874),r=a.n(i),o=a(6681),l=a(3737),c=a(3592),d=a(6273),u=a(2846),m=a(4752),x=a.n(m);function p(){console.log("\uD83D\uDD0D AdminWithdrawalsPage component loaded");let{user:e,loading:t,isAdmin:i}=(0,o.wC)(),[m,p]=(0,n.useState)([]),[h,g]=(0,n.useState)(!0),[f,w]=(0,n.useState)("pending"),[b,y]=(0,n.useState)(""),[j,N]=(0,n.useState)(null),[v,D]=(0,n.useState)(!1),[C,k]=(0,n.useState)([]),[A,S]=(0,n.useState)(!1),[E,B]=(0,n.useState)(""),[L,U]=(0,n.useState)(!1),[I,z]=(0,n.useState)(!1);(0,n.useEffect)(()=>{console.log("\uD83D\uDD0D useEffect triggered - isAdmin:",i,"loading:",t),i&&(console.log("\uD83D\uDD0D Admin verified, loading withdrawals..."),P())},[i]);let P=async()=>{try{g(!0);try{console.log("\uD83D\uDE80 Loading withdrawals with optimized function...");let e=(await d.x8.getAdminWithdrawals(I)).map(e=>({id:e.id,userId:e.userId,userName:e.userName,userEmail:e.userEmail,userMobile:e.userMobile,userPlan:e.userPlan,userActiveDays:e.userActiveDays,walletBalance:e.walletBalance,amount:e.amount,bankDetails:e.bankDetails,requestDate:(0,u.xi)(e.requestDate),status:e.status,adminNotes:e.adminNotes}));p(e),console.log("✅ Loaded ".concat(e.length," withdrawals via optimized function"))}catch(i){let e;console.warn("⚠️ Optimized function failed, using fallback:",i);let{getAllPendingWithdrawals:t,getAllWithdrawals:s}=await a.e(9160).then(a.bind(a,6779));I?(console.log("\uD83D\uDCCB Loading ALL withdrawals (all statuses)..."),e=await s()):(console.log("⏳ Loading ALL PENDING withdrawals..."),e=await t());let n=[];for(let t of e)try{if(!t.userId||!t.amount){console.warn("Withdrawal missing required fields:",t);continue}let e=await (0,c.getUserData)(t.userId),s=await (0,c.getWalletData)(t.userId);if(e){let{calculateUserActiveDays:i}=await Promise.resolve().then(a.bind(a,3592)),r=await i(t.userId);n.push({id:t.id,userId:t.userId,userName:e.name,userEmail:e.email,userMobile:e.mobile||"",userPlan:e.plan,userActiveDays:r,walletBalance:(null==s?void 0:s.wallet)||0,amount:t.amount,bankDetails:t.bankDetails||{accountHolderName:"",accountNumber:"",ifscCode:"",bankName:""},requestDate:(0,u.xi)(t.date),status:t.status||"pending",adminNotes:t.adminNotes})}}catch(e){console.error("Error loading user data for withdrawal ".concat(t.id,":"),e)}p(n),console.log("✅ Loaded ".concat(n.length," withdrawals with fallback method"))}}catch(e){console.error("Error loading withdrawals:",e),p([]),x().fire({icon:"error",title:"Error",text:"Failed to load withdrawals. Please try again."})}finally{g(!1)}};(0,n.useEffect)(()=>{i&&P()},[I]);let F=async(e,t,s)=>{try{let{updateWithdrawalStatus:n}=await a.e(9160).then(a.bind(a,6779));await n(e,t,s),p(a=>a.map(a=>a.id===e?{...a,status:t,adminNotes:s}:a)),x().fire({icon:"success",title:"Status Updated",text:"Withdrawal has been ".concat(t,"."),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error updating withdrawal status:",e),x().fire({icon:"error",title:"Update Failed",text:"Failed to update withdrawal status. Please try again."})}},T=e=>{x().fire({title:"Approve Withdrawal",text:"Approve withdrawal of ₹".concat(e.amount," for ").concat(e.userName,"?"),icon:"question",showCancelButton:!0,confirmButtonColor:"#10b981",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Approve",cancelButtonText:"Cancel"}).then(t=>{t.isConfirmed&&F(e.id,"approved")})},V=e=>{x().fire({title:"Reject Withdrawal",text:"Please provide a reason for rejection:",input:"textarea",inputPlaceholder:"Enter rejection reason...",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Reject",cancelButtonText:"Cancel",inputValidator:e=>{if(!e)return"Please provide a reason for rejection"}}).then(t=>{t.isConfirmed&&F(e.id,"rejected",t.value)})},q=e=>{x().fire({title:"Mark as Completed",text:"Mark withdrawal of ₹".concat(e.amount," as completed?"),icon:"question",showCancelButton:!0,confirmButtonColor:"#3b82f6",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Complete",cancelButtonText:"Cancel"}).then(t=>{t.isConfirmed&&F(e.id,"completed")})},W=e=>{C.includes(e)?(k(t=>t.filter(t=>t!==e)),S(!1)):(k(t=>[...t,e]),C.length+1===M.length&&S(!0))},R=async()=>{let e;if(0===C.length)return void x().fire({icon:"warning",title:"No Selection",text:"Please select at least one withdrawal to update."});if(!E)return void x().fire({icon:"warning",title:"No Action Selected",text:"Please select an action to perform."});let t="approved"===E?"approve":"rejected"===E?"reject":"completed"===E?"mark as completed":E;if((e="rejected"===E?await x().fire({title:"Bulk ".concat(t.charAt(0).toUpperCase()+t.slice(1)),text:"Please provide a reason for rejection:",input:"textarea",inputPlaceholder:"Enter rejection reason...",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"".concat(t.charAt(0).toUpperCase()+t.slice(1)," ").concat(C.length," withdrawals"),cancelButtonText:"Cancel",inputValidator:e=>{if(!e)return"Please provide a reason for rejection"}}):await x().fire({title:"Bulk ".concat(t.charAt(0).toUpperCase()+t.slice(1)),text:"Are you sure you want to ".concat(t," ").concat(C.length," selected withdrawals?"),icon:"question",showCancelButton:!0,confirmButtonColor:"approved"===E?"#10b981":"#3b82f6",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, ".concat(t.charAt(0).toUpperCase()+t.slice(1)),cancelButtonText:"Cancel"})).isConfirmed)try{for(let t of(U(!0),C))await F(t,E,e.value);k([]),S(!1),B(""),x().fire({icon:"success",title:"Bulk Update Complete",text:"Successfully ".concat(t,"ed ").concat(C.length," withdrawals."),timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error in bulk update:",e),x().fire({icon:"error",title:"Bulk Update Failed",text:"Some withdrawals could not be updated. Please try again."})}finally{U(!1)}},M=m.filter(e=>{let t=!I||!f||e.status===f,a=!b||String(e.userName||"").toLowerCase().includes(b.toLowerCase())||String(e.userEmail||"").toLowerCase().includes(b.toLowerCase())||String(e.userMobile||"").toLowerCase().includes(b.toLowerCase());return t&&a}),Q=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),O=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},H=e=>{switch(e.toLowerCase()){case"trial":default:return"bg-gray-100 text-gray-800";case"starter":return"bg-green-100 text-green-800";case"premium":return"bg-blue-100 text-blue-800";case"gold":return"bg-yellow-100 text-yellow-800";case"platinum":return"bg-purple-100 text-purple-800";case"diamond":return"bg-pink-100 text-pink-800"}};return(console.log("\uD83D\uDD0D Render check - loading:",t,"dataLoading:",h,"isAdmin:",i),t)?(console.log("\uD83D\uDD0D Showing auth loading screen"),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Checking authentication..."})]})})):i?h?(console.log("\uD83D\uDD0D Showing data loading screen"),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading withdrawals..."})]})})):(console.log("\uD83D\uDD0D Rendering main component"),(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(r(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Withdrawal Requests"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[I?"Viewing all withdrawals":"Viewing pending withdrawals only","- No pagination, all data loaded"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",M.length,C.length>0&&(0,s.jsxs)("span",{className:"ml-2 text-blue-600 font-medium",children:["(",C.length," selected)"]})]}),(0,s.jsxs)("button",{onClick:()=>{let e=m.filter(e=>"pending"===e.status);if(0===e.length)return void x().fire({icon:"warning",title:"No Pending Withdrawals",text:"No pending withdrawals to export."});let t=(0,l.dB)(e);(0,l.Bf)(t,"pending-withdrawals"),x().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(e.length," pending withdrawals to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-clock mr-2"}),"Export Pending"]}),(0,s.jsxs)("button",{onClick:()=>{if(0===M.length)return void x().fire({icon:"warning",title:"No Data",text:"No withdrawals to export."});let e=(0,l.dB)(M);(0,l.Bf)(e,"withdrawals"),x().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(M.length," withdrawals to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export All"]}),(0,s.jsxs)("button",{onClick:()=>{z(!I),w(I?"pending":""),k([]),S(!1)},className:"px-4 py-2 rounded-lg ".concat(I?"bg-orange-500 hover:bg-orange-600 text-white":"bg-blue-500 hover:bg-blue-600 text-white"),children:[(0,s.jsx)("i",{className:"fas ".concat(I?"fa-filter":"fa-list"," mr-2")}),I?"Show Pending Only":"Show All Withdrawals"]}),(0,s.jsxs)("button",{onClick:()=>P(),className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,s.jsxs)("div",{className:"grid gap-4 ".concat(I?"grid-cols-1 md:grid-cols-3":"grid-cols-1 md:grid-cols-2"),children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,s.jsx)("input",{type:"text",value:b,onChange:e=>y(e.target.value),placeholder:"Search user name, email, or mobile...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),I&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status Filter"}),(0,s.jsxs)("select",{value:f,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Status"}),(0,s.jsx)("option",{value:"pending",children:"Pending"}),(0,s.jsx)("option",{value:"approved",children:"Approved"}),(0,s.jsx)("option",{value:"rejected",children:"Rejected"}),(0,s.jsx)("option",{value:"completed",children:"Completed"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsxs)("button",{onClick:()=>{w(I?"":"pending"),y("")},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-eraser mr-2"}),"Clear Filters"]})})]}),!I&&(0,s.jsx)("div",{className:"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-info-circle text-yellow-600 mr-2"}),(0,s.jsxs)("span",{className:"text-sm text-yellow-800",children:["Currently showing ",(0,s.jsx)("strong",{children:"pending withdrawals only"}),'. Use the "Show All Withdrawals" button above to view all statuses.']})]})})]}),C.length>0&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mx-6 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-blue-800 font-medium",children:[(0,s.jsx)("i",{className:"fas fa-check-square mr-2"}),C.length," withdrawal",C.length>1?"s":""," selected"]}),(0,s.jsxs)("select",{value:E,onChange:e=>B(e.target.value),className:"px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Select Action"}),(0,s.jsx)("option",{value:"approved",children:"Approve Selected"}),(0,s.jsx)("option",{value:"rejected",children:"Reject Selected"}),(0,s.jsx)("option",{value:"completed",children:"Mark as Completed"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:R,disabled:!E||L,className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:L?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-bolt mr-2"}),"Apply Action"]})}),(0,s.jsxs)("button",{onClick:()=>{k([]),S(!1),B("")},className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-times mr-2"}),"Clear Selection"]})]})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:A,onChange:()=>{A?(k([]),S(!1)):(k(M.map(e=>e.id)),S(!0))},className:"mr-2"}),"Select All"]})}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Days"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet Balance"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Holder"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bank Name"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Number"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"IFSC Code"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Request Date"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:M.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("input",{type:"checkbox",checked:C.includes(e.id),onChange:()=>W(e.id),className:"rounded"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.userMobile||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(H(e.userPlan)),children:e.userPlan})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.userActiveDays," days"]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:Q(e.walletBalance)})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-lg font-bold text-green-600",children:Q(e.amount)})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.bankDetails.accountHolderName||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.bankDetails.bankName||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900 font-mono",children:e.bankDetails.accountNumber||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900 font-mono",children:e.bankDetails.ifscCode||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,u.g1)(e.requestDate)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(O(e.status)),children:e.status?e.status.charAt(0).toUpperCase()+e.status.slice(1):"Unknown"})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{N(e),D(!0)},className:"text-blue-600 hover:text-blue-900",children:"View"}),"pending"===e.status&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>T(e),className:"text-green-600 hover:text-green-900",children:"Approve"}),(0,s.jsx)("button",{onClick:()=>V(e),className:"text-red-600 hover:text-red-900",children:"Reject"})]}),"approved"===e.status&&(0,s.jsx)("button",{onClick:()=>q(e),className:"text-blue-600 hover:text-blue-900",children:"Complete"})]})]},e.id))})]})}),(0,s.jsx)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 text-center text-sm text-gray-600",children:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,s.jsxs)("span",{children:["Showing ",m.length," ",I?"total":"pending"," withdrawals"]}),m.length>0&&(0,s.jsxs)("span",{className:"text-green-600 font-medium",children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-1"}),"All loaded in single page"]})]})})]})}),v&&j&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-bold",children:"Withdrawal Details"}),(0,s.jsx)("button",{onClick:()=>D(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times"})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:j.userName}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:j.userEmail})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Mobile Number"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:j.userMobile||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Plan"}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(H(j.userPlan)),children:j.userPlan})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Active Days"}),(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[j.userActiveDays," days"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Wallet Balance"}),(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:Q(j.walletBalance)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Withdrawal Amount"}),(0,s.jsx)("p",{className:"text-lg font-bold text-green-600",children:Q(j.amount)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Bank Details"}),(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Account Holder:"})," ",j.bankDetails.accountHolderName]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Bank:"})," ",j.bankDetails.bankName]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Account Number:"})," ",j.bankDetails.accountNumber]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"IFSC Code:"})," ",j.bankDetails.ifscCode]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(O(j.status)),children:j.status?j.status.charAt(0).toUpperCase()+j.status.slice(1):"Unknown"})]}),j.adminNotes&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Admin Notes"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:j.adminNotes})]})]})]})})]})):(console.log("\uD83D\uDD0D User is not admin"),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-gray-600",children:"Access denied. Admin privileges required."})})}))}},6273:(e,t,a)=>{"use strict";a.d(t,{x8:()=>f});var s=a(2144),n=a(6104);let i=(0,s.Qg)(n.Cn,"getUserDashboardData"),r=(0,s.Qg)(n.Cn,"submitVideoBatch"),o=(0,s.Qg)(n.Cn,"processWithdrawalRequest"),l=(0,s.Qg)(n.Cn,"getUserNotifications"),c=(0,s.Qg)(n.Cn,"getUserTransactions"),d=(0,s.Qg)(n.Cn,"getAdminWithdrawals"),u=(0,s.Qg)(n.Cn,"getAdminDashboardStats"),m=(0,s.Qg)(n.Cn,"getAdminUsers"),x=(0,s.Qg)(n.Cn,"getAdminNotifications"),p=(0,s.Qg)(n.Cn,"createAdminNotification");async function h(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",n.Cn.app.options.projectId);let t=await i({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}}async function g(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let f={getDashboardData:async function(e){try{return await h(e)}catch(l){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:s,getVideoCountData:n}=await a.e(3592).then(a.bind(a,3592)),[i,r,o]=await Promise.all([t(e),s(e),n(e)]);return{userData:i,walletData:r,videoData:o}}},submitVideoBatch:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await r({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await o(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await l({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{console.log("\uD83D\uDE80 Using optimized transactions function...");let s=await c({userId:e,limit:t,type:a});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",n.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}},getAdminDashboardStats:async function(){try{return await g()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3592),a.e(6779)]).then(a.bind(a,6779));return await e()}},getAdminUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await m(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await x({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await p(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",n.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",n.Cn.region);let e=await i({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),!1}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>t(3571)),_N_E=e.O()}]);