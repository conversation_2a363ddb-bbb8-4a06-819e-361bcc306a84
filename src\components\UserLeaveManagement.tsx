'use client'

import { useState, useEffect } from 'react'
import Swal from 'sweetalert2'

interface UserLeave {
  id: string
  date: Date
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  appliedAt: Date
  reviewedBy?: string
  reviewedAt?: Date
  reviewNotes?: string
}

interface UserLeaveManagementProps {
  userId: string
  currentMonth: string
  usedLeaves: number
  maxLeaves: number
  onLeaveCountChange?: () => void
}

export default function UserLeaveManagement({
  userId,
  currentMonth,
  usedLeaves,
  maxLeaves,
  onLeaveCountChange
}: UserLeaveManagementProps) {
  const [leaves, setLeaves] = useState<UserLeave[]>([])
  const [showApplyModal, setShowApplyModal] = useState(false)
  const [isApplying, setIsApplying] = useState(false)
  const [maxDate, setMaxDate] = useState<string>('')

  const [formData, setFormData] = useState({
    date: '',
    reason: ''
  })

  useEffect(() => {
    loadUserLeaves()
    calculateMaxDate()
  }, [userId])

  const calculateMaxDate = async () => {
    try {
      const { getUserData, getPlanValidityDays, calculateUserActiveDays } = await import('@/lib/dataService')
      const userData = await getUserData(userId)

      if (userData) {
        let planExpiryDate: Date
        const today = new Date()

        if (userData.plan === 'Trial') {
          // For trial users, calculate expiry based on joined date + 2 days
          const joinedDate = userData.joinedDate || new Date()
          planExpiryDate = new Date(joinedDate.getTime() + (2 * 24 * 60 * 60 * 1000))
        } else if (userData.planExpiry) {
          // For paid plans with explicit expiry date
          planExpiryDate = userData.planExpiry
        } else {
          // For paid plans without explicit expiry, calculate based on centralized active days
          const planValidityDays = getPlanValidityDays(userData.plan)
          const activeDays = await calculateUserActiveDays(userId)
          const remainingDays = Math.max(0, planValidityDays - activeDays)
          planExpiryDate = new Date(today.getTime() + (remainingDays * 24 * 60 * 60 * 1000))
        }

        // Set max date to plan expiry or 30 days from now, whichever is earlier
        const thirtyDaysFromNow = new Date(today.getTime() + (30 * 24 * 60 * 60 * 1000))
        const maxAllowedDate = planExpiryDate < thirtyDaysFromNow ? planExpiryDate : thirtyDaysFromNow

        setMaxDate(maxAllowedDate.toISOString().split('T')[0])
      }
    } catch (error) {
      console.error('Error calculating max date:', error)
      // Default to 30 days from now if calculation fails
      const thirtyDaysFromNow = new Date()
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)
      setMaxDate(thirtyDaysFromNow.toISOString().split('T')[0])
    }
  }

  const loadUserLeaves = async () => {
    try {
      // Load actual user leaves from Firestore
      const { getUserLeaves } = await import('@/lib/leaveService')
      const userLeaves = await getUserLeaves(userId)
      setLeaves(userLeaves)
    } catch (error) {
      console.error('Error loading user leaves:', error)
      setLeaves([]) // Set empty array on error
    }
  }

  const handleApplyLeave = async () => {
    try {
      if (!formData.date || !formData.reason.trim()) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Please fill in all required fields.',
        })
        return
      }

      const selectedDate = new Date(formData.date)
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      // Prevent applying leave for current day or past dates
      if (selectedDate <= today) {
        Swal.fire({
          icon: 'error',
          title: 'Invalid Date',
          text: 'Cannot apply leave for today or past dates. Please select a future date.',
        })
        return
      }

      // Check if the selected date is within plan expiry
      try {
        const { isUserPlanExpired } = await import('@/lib/dataService')
        const planStatus = await isUserPlanExpired(userId)

        if (planStatus.expired) {
          Swal.fire({
            icon: 'error',
            title: 'Plan Expired',
            text: 'Your plan has expired. Cannot apply for leave.',
          })
          return
        }

        // Calculate plan expiry date for validation using centralized calculation
        const { getUserData, getPlanValidityDays, calculateUserActiveDays } = await import('@/lib/dataService')
        const userData = await getUserData(userId)

        if (userData) {
          let planExpiryDate: Date

          if (userData.plan === 'Trial') {
            // For trial users, calculate expiry based on joined date + 2 days
            const joinedDate = userData.joinedDate || new Date()
            planExpiryDate = new Date(joinedDate.getTime() + (2 * 24 * 60 * 60 * 1000))
          } else if (userData.planExpiry) {
            // For paid plans with explicit expiry date
            planExpiryDate = userData.planExpiry
          } else {
            // For paid plans without explicit expiry, calculate based on centralized active days
            const planValidityDays = getPlanValidityDays(userData.plan)
            const activeDays = await calculateUserActiveDays(userId)
            const remainingDays = Math.max(0, planValidityDays - activeDays)
            planExpiryDate = new Date(today.getTime() + (remainingDays * 24 * 60 * 60 * 1000))
          }

          if (selectedDate > planExpiryDate) {
            Swal.fire({
              icon: 'error',
              title: 'Date Outside Plan Period',
              text: `Cannot apply leave beyond your plan expiry date (${planExpiryDate.toLocaleDateString()}).`,
            })
            return
          }
        }
      } catch (error) {
        console.error('Error checking plan expiry:', error)
        // Continue with leave application if plan check fails
      }

      // Check if user has remaining leaves for this month
      if (usedLeaves >= maxLeaves) {
        Swal.fire({
          icon: 'error',
          title: 'Leave Limit Exceeded',
          text: `You have already used all ${maxLeaves} leaves for this month.`,
        })
        return
      }

      // Check if leave already applied for this date
      const existingLeave = leaves.find(leave => 
        leave.date.toDateString() === selectedDate.toDateString()
      )

      if (existingLeave) {
        Swal.fire({
          icon: 'error',
          title: 'Duplicate Application',
          text: 'You have already applied for leave on this date.',
        })
        return
      }

      setIsApplying(true)

      // Save to Firestore
      const { applyUserLeave } = await import('@/lib/leaveService')
      const result = await applyUserLeave({
        userId,
        date: selectedDate,
        reason: formData.reason.trim()
      })

      // Reload leaves from Firestore to get updated data
      await loadUserLeaves()

      // Notify parent component to refresh leave count
      if (onLeaveCountChange) {
        onLeaveCountChange()
      }

      // Show appropriate success message based on auto-approval
      if (result.autoApproved) {
        Swal.fire({
          icon: 'success',
          title: '✅ Leave Auto-Approved!',
          html: `
            <div class="text-left">
              <p><strong>Your leave has been automatically approved!</strong></p>
              <br>
              <p><strong>Date:</strong> ${selectedDate.toLocaleDateString()}</p>
              <p><strong>Reason:</strong> ${formData.reason.trim()}</p>
              <br>
              <p class="text-green-600"><strong>Leave Quota:</strong> ${result.usedLeaves}/${result.maxLeaves} used this month</p>
              <p class="text-blue-600"><strong>Status:</strong> Approved automatically</p>
            </div>
          `,
          timer: 6000,
          showConfirmButton: true,
          confirmButtonText: 'Great!'
        })
      } else {
        Swal.fire({
          icon: 'warning',
          title: '⏳ Leave Pending Approval',
          html: `
            <div class="text-left">
              <p><strong>Your leave application has been submitted.</strong></p>
              <br>
              <p><strong>Date:</strong> ${selectedDate.toLocaleDateString()}</p>
              <p><strong>Reason:</strong> ${formData.reason.trim()}</p>
              <br>
              <p class="text-orange-600"><strong>Status:</strong> Pending admin approval (quota exceeded)</p>
              <p class="text-gray-600"><strong>Leave Quota:</strong> ${result.maxLeaves}/${result.maxLeaves} used this month</p>
            </div>
          `,
          timer: 6000,
          showConfirmButton: true,
          confirmButtonText: 'Understood'
        })
      }

      // Reset form and close modal
      setFormData({
        date: '',
        reason: ''
      })
      setShowApplyModal(false)
    } catch (error) {
      console.error('Error applying leave:', error)
      Swal.fire({
        icon: 'error',
        title: 'Application Failed',
        text: 'Failed to apply for leave. Please try again.',
      })
    } finally {
      setIsApplying(false)
    }
  }

  const handleCancelLeave = async (leaveId: string) => {
    try {
      const leave = leaves.find(l => l.id === leaveId)
      if (!leave || leave.status !== 'pending') {
        Swal.fire({
          icon: 'error',
          title: 'Cannot Cancel',
          text: 'Only pending leave applications can be cancelled.',
        })
        return
      }

      const result = await Swal.fire({
        title: 'Cancel Leave Application',
        text: 'Are you sure you want to cancel this leave application?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, Cancel',
        cancelButtonText: 'Keep Application'
      })

      if (result.isConfirmed) {
        // Delete from Firestore
        const { cancelUserLeave } = await import('@/lib/leaveService')
        await cancelUserLeave(leaveId)

        // Reload leaves from Firestore to get updated data
        await loadUserLeaves()

        // Notify parent component to refresh leave count
        if (onLeaveCountChange) {
          onLeaveCountChange()
        }

        Swal.fire({
          icon: 'success',
          title: 'Application Cancelled',
          text: 'Your leave application has been cancelled.',
          timer: 2000,
          showConfirmButton: false
        })
      }
    } catch (error) {
      console.error('Error cancelling leave:', error)
      Swal.fire({
        icon: 'error',
        title: 'Cancellation Failed',
        text: 'Failed to cancel leave application. Please try again.',
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string, reviewedBy?: string) => {
    switch (status) {
      case 'approved':
        return reviewedBy === 'system'
          ? 'fas fa-magic text-green-500'
          : 'fas fa-check-circle text-green-500'
      case 'rejected':
        return 'fas fa-times-circle text-red-500'
      case 'pending':
        return 'fas fa-clock text-yellow-500'
      default:
        return 'fas fa-question-circle text-gray-500'
    }
  }

  const remainingLeaves = maxLeaves - usedLeaves

  return (
    <div className="glass-card p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold text-white">
          <i className="fas fa-calendar-times mr-2"></i>
          Leave Management
        </h3>
        <div className="text-right">
          <div className="text-sm text-white/80">
            {currentMonth} Leaves
          </div>
          <div className="text-lg font-bold text-white">
            {remainingLeaves}/{maxLeaves} Available
          </div>
        </div>
      </div>

      {/* Leave Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-green-500/20 p-3 rounded-lg text-center">
          <div className="text-xl font-bold text-green-400">{maxLeaves}</div>
          <div className="text-xs text-white/80">Monthly Quota</div>
        </div>
        <div className="bg-yellow-500/20 p-3 rounded-lg text-center">
          <div className="text-xl font-bold text-yellow-400">{usedLeaves}</div>
          <div className="text-xs text-white/80">Used</div>
        </div>
        <div className="bg-blue-500/20 p-3 rounded-lg text-center">
          <div className="text-xl font-bold text-blue-400">{remainingLeaves}</div>
          <div className="text-xs text-white/80">Remaining</div>
        </div>
      </div>

      {/* Apply Leave Button */}
      <div className="mb-4">
        <button
          onClick={() => setShowApplyModal(true)}
          disabled={remainingLeaves <= 0}
          className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <i className="fas fa-plus mr-2"></i>
          {remainingLeaves > 0 ? 'Apply for Leave' : 'No Leaves Available'}
        </button>
      </div>

      {/* Leave Applications List */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-white/80">Recent Applications</h4>
        {leaves.length === 0 ? (
          <div className="text-center py-4 text-white/60">
            <i className="fas fa-calendar-check text-2xl mb-2"></i>
            <p className="text-sm">No leave applications yet</p>
          </div>
        ) : (
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {leaves.slice(-5).reverse().map((leave) => (
              <div key={leave.id} className="bg-white/10 p-3 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-white font-medium">
                        {leave.date.toLocaleDateString()}
                      </span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(leave.status)}`}>
                        <i className={`${getStatusIcon(leave.status, leave.reviewedBy)} mr-1`}></i>
                        {leave.status ? leave.status.charAt(0).toUpperCase() + leave.status.slice(1) : 'Unknown'}
                        {leave.reviewedBy === 'system' && leave.status === 'approved' && (
                          <span className="ml-1" title="Auto-approved">⚡</span>
                        )}
                      </span>
                    </div>
                    <div className="text-sm text-white/70 mt-1">
                      {leave.reason}
                      {leave.reviewedBy === 'system' && leave.status === 'approved' && (
                        <div className="text-xs text-green-400 mt-1">
                          <i className="fas fa-magic mr-1"></i>
                          Auto-approved (within quota)
                        </div>
                      )}
                      {leave.reviewNotes && leave.reviewedBy !== 'system' && (
                        <div className="text-xs text-blue-400 mt-1">
                          <i className="fas fa-comment mr-1"></i>
                          {leave.reviewNotes}
                        </div>
                      )}
                    </div>
                  </div>
                  {leave.status === 'pending' && (
                    <button
                      onClick={() => handleCancelLeave(leave.id)}
                      className="text-red-400 hover:text-red-300 text-sm"
                    >
                      <i className="fas fa-times"></i>
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Apply Leave Modal */}
      {showApplyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4" style={{ zIndex: 99999 }}>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Apply for Leave</h3>
              <button
                onClick={() => setShowApplyModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <i className="fas fa-times text-xl"></i>
              </button>
            </div>

            {maxDate && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center text-blue-800">
                  <i className="fas fa-info-circle mr-2"></i>
                  <span className="text-sm">
                    Leave applications are allowed until {new Date(maxDate).toLocaleDateString()}
                  </span>
                </div>
              </div>
            )}
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                  min={(() => {
                    const tomorrow = new Date()
                    tomorrow.setDate(tomorrow.getDate() + 1)
                    return tomorrow.toISOString().split('T')[0]
                  })()}
                  max={maxDate}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Leave can only be applied for future dates within your plan period
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Reason</label>
                <textarea
                  value={formData.reason}
                  onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter reason for leave..."
                />
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="text-sm text-blue-800">
                  <i className="fas fa-info-circle mr-2"></i>
                  You have {remainingLeaves} leave(s) remaining for {currentMonth}.
                </div>
                {remainingLeaves > 0 && (
                  <div className="text-sm text-green-700 mt-2">
                    <i className="fas fa-check-circle mr-2"></i>
                    <strong>Auto-Approval:</strong> Your leave will be automatically approved since you have available quota.
                  </div>
                )}
                {remainingLeaves <= 0 && (
                  <div className="text-sm text-orange-700 mt-2">
                    <i className="fas fa-clock mr-2"></i>
                    <strong>Manual Review:</strong> Leave will require admin approval as quota is exceeded.
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex justify-end space-x-4 mt-6">
              <button
                onClick={() => setShowApplyModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={handleApplyLeave}
                disabled={isApplying}
                className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
              >
                {isApplying ? (
                  <>
                    <div className="spinner w-4 h-4 mr-2 inline-block"></div>
                    Applying...
                  </>
                ) : (
                  <>
                    <i className="fas fa-paper-plane mr-2"></i>
                    Apply Leave
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
