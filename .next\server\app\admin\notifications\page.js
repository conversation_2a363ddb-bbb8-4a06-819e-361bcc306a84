(()=>{var e={};e.id=993,e.ids=[993,1391,3772],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},6453:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var s=a(60687),i=a(43210),r=a(85814),o=a.n(r),n=a(87979),l=a(3582),c=a(92617),d=a(91391),u=a(83475),m=a(77567);function f(){console.log("\uD83D\uDD0D AdminNotificationsPage component loaded");let{user:e,loading:t,isAdmin:a}=(0,n.wC)(),[r,f]=(0,i.useState)([]),[g,p]=(0,i.useState)([]),[h,x]=(0,i.useState)(!0),[y,b]=(0,i.useState)(!1),[w,D]=(0,i.useState)(!1),[N,v]=(0,i.useState)([]),[j,E]=(0,i.useState)(!1);console.log("\uD83D\uDD0D Render check - loading:",t,"dataLoading:",h,"isAdmin:",a);let[S,A]=(0,i.useState)({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]}),C=async()=>{try{x(!0);try{console.log("\uD83D\uDE80 Loading admin notifications with optimized functions...");let[e,t]=await Promise.all([c.x8.getAdminNotifications(50,"all"),c.x8.getAdminUsers({limit:100})]),a=e.map(e=>({...e,createdAt:e.createdAt instanceof Date?e.createdAt:e.createdAt?.toDate?.()||new Date(e.createdAt||Date.now())}));f(a),p(t.users),console.log("✅ Admin notifications loaded via optimized functions")}catch(s){console.warn("⚠️ Optimized functions failed, using fallback:",s);let[e,t]=await Promise.all([(0,l._f)(50),(0,d.lo)()]),a=e.map(e=>({...e,createdAt:e.createdAt instanceof Date?e.createdAt:e.createdAt?.toDate?.()||new Date(e.createdAt||Date.now())}));f(a),p(t.users)}}catch(e){console.error("Error loading data:",e),m.A.fire({icon:"error",title:"Error",text:"Failed to load data. Please try again."})}finally{x(!1)}},L=async()=>{try{D(!0);try{await c.x8.createAdminNotification({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[]})}catch(t){console.warn("⚠️ Optimized notification creation failed, using fallback:",t),await (0,l.z8)({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[],createdBy:e?.email||"admin"})}m.A.fire({icon:"success",title:"Test Notification Sent!",text:"Test notification sent to all users. Check user dashboards to verify delivery.",timer:3e3,showConfirmButton:!1}),C()}catch(e){console.error("Error sending test notification:",e),m.A.fire({icon:"error",title:"Test Failed",text:"Failed to send test notification. Please try again."})}finally{D(!1)}},I=async()=>{try{if(!S.title.trim()||!S.message.trim())return void m.A.fire({icon:"error",title:"Validation Error",text:"Please fill in both title and message."});if("specific"===S.targetUsers&&0===S.selectedUserIds.length)return void m.A.fire({icon:"error",title:"Validation Error",text:"Please select at least one user for specific targeting."});D(!0),console.log("Sending notification:",{title:S.title.trim(),message:S.message.trim(),type:S.type,targetUsers:S.targetUsers,userIds:"specific"===S.targetUsers?S.selectedUserIds:[],createdBy:e?.email||"admin"});try{await c.x8.createAdminNotification({title:S.title.trim(),message:S.message.trim(),type:S.type,targetUsers:S.targetUsers,userIds:"specific"===S.targetUsers?S.selectedUserIds:[]})}catch(t){console.warn("⚠️ Optimized notification creation failed, using fallback:",t),await (0,l.z8)({title:S.title.trim(),message:S.message.trim(),type:S.type,targetUsers:S.targetUsers,userIds:"specific"===S.targetUsers?S.selectedUserIds:[],createdBy:e?.email||"admin"})}m.A.fire({icon:"success",title:"Notification Sent!",text:`Notification sent to ${"all"===S.targetUsers?"all users":`${S.selectedUserIds.length} selected users`}.`,timer:3e3,showConfirmButton:!1}),A({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]}),b(!1),C()}catch(e){console.error("Error sending notification:",e),m.A.fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{D(!1)}},U=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},k=e=>{let t,a=new Date;t=e instanceof Date?e:e&&"function"==typeof e.toDate?e.toDate():e&&"string"==typeof e||e&&"number"==typeof e?new Date(e):new Date;let s=Math.floor((a.getTime()-t.getTime())/1e3);if(s<60)return"Just now";if(s<3600){let e=Math.floor(s/60);return`${e} minute${e>1?"s":""} ago`}if(s<86400){let e=Math.floor(s/3600);return`${e} hour${e>1?"s":""} ago`}{let e=Math.floor(s/86400);return`${e} day${e>1?"s":""} ago`}},M=async(e,t)=>{if((await m.A.fire({icon:"warning",title:"Delete Notification",text:`Are you sure you want to delete "${t}"? This action cannot be undone.`,showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await (0,l.fP)(e),f(t=>t.filter(t=>t.id!==e)),m.A.fire({icon:"success",title:"Notification Deleted",text:"Notification has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notification:",e),m.A.fire({icon:"error",title:"Delete Failed",text:"Failed to delete notification. Please try again."})}finally{E(!1)}},T=async()=>{if(0===N.length)return void m.A.fire({icon:"warning",title:"No Selection",text:"Please select notifications to delete."});if((await m.A.fire({icon:"warning",title:"Delete Selected Notifications",text:`Are you sure you want to delete ${N.length} selected notifications? This action cannot be undone.`,showCancelButton:!0,confirmButtonText:"Yes, Delete All",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await Promise.all(N.map(e=>(0,l.fP)(e))),f(e=>e.filter(e=>!N.includes(e.id))),v([]),m.A.fire({icon:"success",title:"Notifications Deleted",text:`${N.length} notifications have been deleted successfully`,timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notifications:",e),m.A.fire({icon:"error",title:"Delete Failed",text:"Failed to delete some notifications. Please try again."})}finally{E(!1)}},P=e=>{v(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return t||h?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(o(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",r.length,N.length>0&&(0,s.jsxs)("span",{className:"ml-2 text-blue-600",children:["(",N.length," selected)"]})]}),N.length>0&&(0,s.jsxs)("button",{onClick:T,disabled:j,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-trash mr-2"}),"Delete Selected (",N.length,")"]}),(0,s.jsxs)("button",{onClick:L,disabled:w||j,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-vial mr-2"}),"Test Notification"]}),(0,s.jsxs)("button",{onClick:()=>{if(0===r.length)return void m.A.fire({icon:"warning",title:"No Data",text:"No notifications to export."});let e=(0,u.Pe)(r);(0,u.Bf)(e,"notifications"),m.A.fire({icon:"success",title:"Export Complete",text:`Exported ${r.length} notifications to CSV file.`,timer:2e3,showConfirmButton:!1})},disabled:j,className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:()=>b(!0),disabled:j,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Send Notification"]}),(0,s.jsxs)("button",{onClick:C,disabled:j,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===r.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-6xl mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications sent yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Start by sending your first notification to users"}),(0,s.jsxs)("button",{onClick:()=>b(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Send First Notification"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-gray-50 px-6 py-3 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:N.length===r.length&&r.length>0,onChange:()=>{N.length===r.length?v([]):v(r.map(e=>e.id).filter(Boolean))},className:"mr-3"}),(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:["Select All (",r.length," notifications)"]})]}),N.length>0&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[N.length," selected"]}),(0,s.jsxs)("button",{onClick:T,disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete Selected"]})]})]})}),(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:r.map(e=>(0,s.jsx)("div",{className:`p-6 hover:bg-gray-50 ${N.includes(e.id)?"bg-blue-50":""}`,children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("input",{type:"checkbox",checked:N.includes(e.id),onChange:()=>P(e.id),className:"mr-3"})}),(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("i",{className:U(e.type)})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.title,(0,s.jsx)("span",{className:"ml-2 px-2 py-1 text-xs font-bold bg-red-100 text-red-800 rounded-full",children:"\uD83D\uDEA8 BLOCKING"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"success"===e.type?"bg-green-100 text-green-800":"warning"===e.type?"bg-yellow-100 text-yellow-800":"error"===e.type?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:e.type.charAt(0).toUpperCase()+e.type.slice(1)}),(0,s.jsx)("button",{onClick:()=>M(e.id,e.title),disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50 p-1",title:"Delete notification",children:(0,s.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,s.jsx)("p",{className:"text-gray-700 mt-2",children:e.message}),(0,s.jsx)("div",{className:"flex items-center justify-between mt-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-user mr-1"}),"By: ",e.createdBy]}),(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-users mr-1"}),"Target: ","all"===e.targetUsers?"All Users":`${e.userIds?.length||0} Selected Users`]}),(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-clock mr-1"}),k(e.createdAt)]})]})})]})]})},e.id))})]})})}),y&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Send Notification"}),(0,s.jsx)("button",{onClick:()=>b(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),(0,s.jsx)("input",{type:"text",value:S.title,onChange:e=>A(t=>({...t,title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification title..."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,s.jsx)("textarea",{value:S.message,onChange:e=>A(t=>({...t,message:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification message..."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,s.jsxs)("select",{value:S.type,onChange:e=>A(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"info",children:"Info"}),(0,s.jsx)("option",{value:"success",children:"Success"}),(0,s.jsx)("option",{value:"warning",children:"Warning"}),(0,s.jsx)("option",{value:"error",children:"Error"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Target"}),(0,s.jsxs)("select",{value:S.targetUsers,onChange:e=>A(t=>({...t,targetUsers:e.target.value,selectedUserIds:"all"===e.target.value?[]:t.selectedUserIds})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Users"}),(0,s.jsx)("option",{value:"specific",children:"Specific Users"})]})]})]}),(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle text-red-500 mt-1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"\uD83D\uDEA8 All Notifications are Blocking (Mandatory)"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Users must acknowledge this notification before they can continue with any activities (watching videos, accessing dashboard features, etc.)"})]})]})}),"specific"===S.targetUsers&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Users"}),(0,s.jsx)("div",{className:"max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2",children:g.map(e=>(0,s.jsxs)("label",{className:"flex items-center p-2 hover:bg-gray-50 rounded",children:[(0,s.jsx)("input",{type:"checkbox",checked:S.selectedUserIds.includes(e.id),onChange:t=>{t.target.checked?A(t=>({...t,selectedUserIds:[...t.selectedUserIds,e.id]})):A(t=>({...t,selectedUserIds:t.selectedUserIds.filter(t=>t!==e.id)}))},className:"mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name||"Unknown User"}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[e.email||"No email"," • ",e.plan||"No plan"]})]})]},e.id))}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[S.selectedUserIds.length," user(s) selected"]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,s.jsx)("button",{onClick:()=>b(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,s.jsx)("button",{onClick:I,disabled:w,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:w?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Notification"]})})]})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},35592:(e,t,a)=>{Promise.resolve().then(a.bind(a,60795))},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60795:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\notifications\\page.tsx","default")},62251:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=a(65239),i=a(48088),r=a(88170),o=a.n(r),n=a(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let c={children:["",{children:["admin",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,60795)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\notifications\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\notifications\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/notifications/page",pathname:"/admin/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,a)=>{"use strict";function s(e,t,a){if(!e||0===e.length)return void alert("No data to export");let s=a||Object.keys(e[0]),i=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],r=new Blob(["\uFEFF"+[s.join(","),...e.map(e=>s.map(t=>{let a=e[t];if(null==a)return"";let s=i.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return`"${e}"`}return a instanceof Date?`"${a.toLocaleDateString()}"`:"object"==typeof a&&null!==a&&a.toDate?`"${a.toDate().toLocaleDateString()}"`:s&&("number"==typeof a||!isNaN(Number(a)))?`"${a}"`:"number"==typeof a?a.toString():`"${String(a)}"`}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),o=document.createElement("a");if(void 0!==o.download){let e=URL.createObjectURL(r);o.setAttribute("href",e),o.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),o.style.visibility="hidden",document.body.appendChild(o),o.click(),document.body.removeChild(o)}}function i(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days (Calculated)":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function r(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function o(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":String(e.bankDetails?.accountNumber||""),"IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}))}function n(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>s,Fz:()=>i,Pe:()=>n,dB:()=>o,sL:()=>r})},91391:(e,t,a)=>{"use strict";a.d(t,{CF:()=>d,I0:()=>m,TK:()=>p,getAdminDashboardStats:()=>n,getAllPendingWithdrawals:()=>f,getAllWithdrawals:()=>g,hG:()=>h,lo:()=>l,nQ:()=>u,updateWithdrawalStatus:()=>x,x5:()=>c});var s=a(75535),i=a(33784),r=a(3582);let o=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=o.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=s.Dc.fromDate(t),n=await (0,s.getDocs)((0,s.collection)(i.db,r.COLLECTIONS.users)),l=n.size,c=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s._M)(r.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,s.getDocs)(c)).size,u=0,m=0,f=0,g=0;n.forEach(e=>{let a=e.data();u+=a[r.FIELD_NAMES.totalVideos]||0,m+=a[r.FIELD_NAMES.wallet]||0;let s=a[r.FIELD_NAMES.lastVideoDate]?.toDate();s&&s.toDateString()===t.toDateString()&&(f+=a[r.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s._M)(r.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.getDocs)(e)).forEach(e=>{let a=e.data(),s=a[r.FIELD_NAMES.date]?.toDate();s&&s>=t&&(g+=a[r.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let p=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),h=(await (0,s.getDocs)(p)).size,x=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s._M)("date",">=",a)),y=(await (0,s.getDocs)(x)).size,b={totalUsers:l,totalVideos:u,totalEarnings:m,pendingWithdrawals:h,todayUsers:d,todayVideos:f,todayEarnings:g,todayWithdrawals:y};return o.set(e,{data:b,timestamp:Date.now()}),b}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(e=50,t=null){try{let a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let o=await (0,s.getDocs)(a);return{users:o.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[r.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[r.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(a)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[r.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[r.FIELD_NAMES.planExpiry]?.toDate()})).filter(e=>{let a=String(e[r.FIELD_NAMES.name]||"").toLowerCase(),s=String(e[r.FIELD_NAMES.email]||"").toLowerCase(),i=String(e[r.FIELD_NAMES.mobile]||"").toLowerCase(),o=String(e[r.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||s.includes(t)||i.includes(t)||o.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[r.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[r.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users));return(await (0,s.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function m(e=50,t=null){try{let a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s.My)(r.FIELD_NAMES.date,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s.My)(r.FIELD_NAMES.date,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let o=await (0,s.getDocs)(a);return{transactions:o.docs.map(e=>({id:e.id,...e.data(),date:e.data()[r.FIELD_NAMES.date]?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function f(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending"),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} pending withdrawals`),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} total withdrawals`),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function p(e,t){try{await (0,s.mZ)((0,s.H9)(i.db,r.COLLECTIONS.users,e),t),o.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function h(e){try{await (0,s.kd)((0,s.H9)(i.db,r.COLLECTIONS.users,e)),o.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function x(e,t,n){try{let l=await (0,s.x7)((0,s.H9)(i.db,r.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=l.data(),m={status:t,updatedAt:s.Dc.now()};if(n&&(m.adminNotes=n),await (0,s.mZ)((0,s.H9)(i.db,r.COLLECTIONS.withdrawals,e),m),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3582));await e(c,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${d} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3582));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:`Withdrawal rejected - ₹${d} credited back to wallet`})}o.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},92617:(e,t,a)=>{"use strict";a.d(t,{x8:()=>x});var s=a(24791),i=a(33784);let r=(0,s.Qg)(i.Cn,"getUserDashboardData"),o=(0,s.Qg)(i.Cn,"submitVideoBatch"),n=(0,s.Qg)(i.Cn,"processWithdrawalRequest"),l=(0,s.Qg)(i.Cn,"getUserNotifications"),c=(0,s.Qg)(i.Cn,"getUserTransactions"),d=(0,s.Qg)(i.Cn,"getAdminWithdrawals"),u=(0,s.Qg)(i.Cn,"getAdminDashboardStats"),m=(0,s.Qg)(i.Cn,"getAdminUsers"),f=(0,s.Qg)(i.Cn,"getAdminNotifications"),g=(0,s.Qg)(i.Cn,"createAdminNotification");async function p(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await r({userId:e});if(console.log("\uD83D\uDCE1 Function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",t),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}}async function h(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let x={getDashboardData:async function(e){try{return await p(e)}catch(l){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:s,getVideoCountData:i}=await a.e(3582).then(a.bind(a,3582)),[r,o,n]=await Promise.all([t(e),s(e),i(e)]);return{userData:r,walletData:o,videoData:n}}},submitVideoBatch:async function(e,t=50){try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await o({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await n(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e,t=10){try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await l({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e,t=10,a="all"){try{console.log("\uD83D\uDE80 Using optimized transactions function...");let s=await c({userId:e,limit:t,type:a});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(e=!1){try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",i.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}},getAdminDashboardStats:async function(){try{return await h()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3582),a.e(1391)]).then(a.bind(a,91391));return await e()}},getAdminUsers:async function(e={}){try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await m(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(e=50,t="all"){try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await f({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await g(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",i.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",i.Cn.region);let e=await r({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),!1}}}},94735:e=>{"use strict";e.exports=require("events")},99144:(e,t,a)=>{Promise.resolve().then(a.bind(a,6453))}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[6204,6958,7567,8441,3582,7979],()=>a(62251));module.exports=s})();