'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAuth } from '@/hooks/useAuth'
import { getTransactions } from '@/lib/dataService'
import { optimizedService } from '@/lib/optimizedDataService'
import { formatDisplayDate, formatDisplayTime, safeToDate } from '@/lib/dateUtils'
import Swal from 'sweetalert2'

interface Transaction {
  id: string
  type: string
  amount: number
  description: string
  date: Date
  status: string
}

interface FilterOptions {
  type: string
  status: string
  dateFrom: string
  dateTo: string
  searchTerm: string
}

export default function TransactionsPage() {
  const { user, loading } = useRequireAuth()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([])
  const [dataLoading, setDataLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 20

  // Filter state
  const [filters, setFilters] = useState<FilterOptions>({
    type: '',
    status: '',
    dateFrom: '',
    dateTo: '',
    searchTerm: ''
  })
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    if (user) {
      loadTransactions()
    }
  }, [user])

  useEffect(() => {
    applyFilters()
  }, [transactions, filters])

  const loadTransactions = async () => {
    try {
      setDataLoading(true)

      // Use optimized transactions function
      try {
        console.log('🚀 Loading transactions with optimized function...')
        const result = await optimizedService.getUserTransactions(user!.uid, 100, 'all')
        // Ensure dates are properly converted
        const formattedTransactions = (result as any[]).map(transaction => ({
          ...transaction,
          date: safeToDate(transaction.date)
        }))
        setTransactions(formattedTransactions as Transaction[])
        console.log('✅ Transactions loaded via optimized function')
      } catch (optimizedError) {
        console.warn('⚠️ Optimized function failed, using fallback:', optimizedError)
        // Fallback to original method
        const result = await getTransactions(user!.uid, 100)
        // Ensure dates are properly converted for fallback too
        const formattedTransactions = (result as any[]).map(transaction => ({
          ...transaction,
          date: safeToDate(transaction.date)
        }))
        setTransactions(formattedTransactions as Transaction[])
      }
    } catch (error) {
      console.error('Error loading transactions:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to load transactions. Please try again.',
      })
    } finally {
      setDataLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...transactions]

    // Filter by type
    if (filters.type) {
      filtered = filtered.filter(t => t.type === filters.type)
    }

    // Filter by status
    if (filters.status) {
      filtered = filtered.filter(t => t.status === filters.status)
    }

    // Filter by date range
    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom)
      filtered = filtered.filter(t => t.date >= fromDate)
    }

    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo)
      toDate.setHours(23, 59, 59, 999) // End of day
      filtered = filtered.filter(t => t.date <= toDate)
    }

    // Filter by search term
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase()
      filtered = filtered.filter(t => 
        t.description.toLowerCase().includes(searchLower) ||
        t.type.toLowerCase().includes(searchLower)
      )
    }

    setFilteredTransactions(filtered)
    setTotalPages(Math.ceil(filtered.length / itemsPerPage))
    setCurrentPage(1)
  }

  const handleFilterChange = (key: keyof FilterOptions, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const clearFilters = () => {
    setFilters({
      type: '',
      status: '',
      dateFrom: '',
      dateTo: '',
      searchTerm: ''
    })
  }

  const exportTransactions = () => {
    if (filteredTransactions.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data',
        text: 'No transactions to export.',
      })
      return
    }

    const csvContent = [
      ['Date', 'Type', 'Description', 'Amount', 'Status'].join(','),
      ...filteredTransactions.map(t => [
        t.date instanceof Date ? t.date.toLocaleDateString() : new Date(t.date).toLocaleDateString(),
        t.type,
        `"${t.description}"`,
        t.amount,
        t.status
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `transactions_${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '₹0.00'
    }
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'video_earning':
        return 'fas fa-play-circle text-green-400'
      case 'withdrawal':
        return 'fas fa-download text-red-400'
      case 'bonus':
        return 'fas fa-gift text-yellow-400'
      case 'referral':
        return 'fas fa-users text-blue-400'
      default:
        return 'fas fa-exchange-alt text-white'
    }
  }

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'video_earning':
        return 'Video Earning'
      case 'withdrawal':
        return 'Withdrawal'
      case 'bonus':
        return 'Bonus'
      case 'referral':
        return 'Referral'
      default:
        return type.charAt(0).toUpperCase() + type.slice(1)
    }
  }

  // Pagination
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentTransactions = filteredTransactions.slice(startIndex, endIndex)

  if (loading || dataLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-white">Loading transactions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between">
          <Link href="/wallet" className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Wallet
          </Link>
          <h1 className="text-xl font-bold text-white">Transaction History</h1>
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="glass-button px-4 py-2 text-white"
            >
              <i className="fas fa-filter mr-2"></i>
              Filters
            </button>
            <button
              onClick={loadTransactions}
              className="glass-button px-4 py-2 text-white"
            >
              <i className="fas fa-sync-alt mr-2"></i>
              Refresh
            </button>
          </div>
        </div>
      </header>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="glass-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Total Transactions</p>
              <p className="text-2xl font-bold text-white">{filteredTransactions.length}</p>
            </div>
            <i className="fas fa-list text-blue-400 text-2xl"></i>
          </div>
        </div>
        
        <div className="glass-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Total Earned</p>
              <p className="text-2xl font-bold text-green-400">
                {formatCurrency(
                  filteredTransactions
                    .filter(t => t.amount > 0)
                    .reduce((sum, t) => sum + t.amount, 0)
                )}
              </p>
            </div>
            <i className="fas fa-arrow-up text-green-400 text-2xl"></i>
          </div>
        </div>
        
        <div className="glass-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Total Withdrawn</p>
              <p className="text-2xl font-bold text-red-400">
                {formatCurrency(
                  Math.abs(filteredTransactions
                    .filter(t => t.amount < 0)
                    .reduce((sum, t) => sum + t.amount, 0))
                )}
              </p>
            </div>
            <i className="fas fa-arrow-down text-red-400 text-2xl"></i>
          </div>
        </div>
        
        <div className="glass-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">This Month</p>
              <p className="text-2xl font-bold text-yellow-400">
                {filteredTransactions.filter(t => {
                  const now = new Date()
                  const transactionDate = new Date(t.date)
                  return transactionDate.getMonth() === now.getMonth() && 
                         transactionDate.getFullYear() === now.getFullYear()
                }).length}
              </p>
            </div>
            <i className="fas fa-calendar text-yellow-400 text-2xl"></i>
          </div>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="glass-card p-6 mb-6">
          <h3 className="text-lg font-bold text-white mb-4">
            <i className="fas fa-filter mr-2"></i>
            Filter Transactions
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            {/* Search */}
            <div>
              <label className="block text-white font-medium mb-2">Search</label>
              <input
                type="text"
                value={filters.searchTerm}
                onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                placeholder="Search description or type..."
                className="form-input"
              />
            </div>

            {/* Transaction Type */}
            <div>
              <label className="block text-white font-medium mb-2">Type</label>
              <select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="form-input"
              >
                <option value="">All Types</option>
                <option value="video_earning">Video Earning</option>
                <option value="withdrawal">Withdrawal</option>
                <option value="bonus">Bonus</option>
                <option value="referral">Referral</option>
              </select>
            </div>

            {/* Status */}
            <div>
              <label className="block text-white font-medium mb-2">Status</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="form-input"
              >
                <option value="">All Status</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            {/* Date From */}
            <div>
              <label className="block text-white font-medium mb-2">From Date</label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                className="form-input"
              />
            </div>

            {/* Date To */}
            <div>
              <label className="block text-white font-medium mb-2">To Date</label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                className="form-input"
              />
            </div>

            {/* Export Button */}
            <div className="flex items-end">
              <button
                onClick={exportTransactions}
                className="btn-primary w-full"
                disabled={filteredTransactions.length === 0}
              >
                <i className="fas fa-download mr-2"></i>
                Export CSV
              </button>
            </div>
          </div>

          <div className="flex gap-2">
            <button
              onClick={clearFilters}
              className="glass-button px-4 py-2 text-white"
            >
              <i className="fas fa-times mr-2"></i>
              Clear Filters
            </button>
            <span className="text-white/60 text-sm flex items-center">
              Showing {filteredTransactions.length} of {transactions.length} transactions
            </span>
          </div>
        </div>
      )}

      {/* Transaction List */}
      <div className="glass-card p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white">
            <i className="fas fa-history mr-2"></i>
            Transactions
          </h3>
          {filteredTransactions.length > 0 && (
            <p className="text-white/60 text-sm">
              Page {currentPage} of {totalPages}
            </p>
          )}
        </div>

        {filteredTransactions.length === 0 ? (
          <div className="text-center py-12">
            <i className="fas fa-receipt text-white/30 text-6xl mb-4"></i>
            <p className="text-white/60 text-lg mb-2">No transactions found</p>
            <p className="text-white/40 text-sm">
              {transactions.length === 0
                ? "You haven't made any transactions yet"
                : "Try adjusting your filters"}
            </p>
          </div>
        ) : (
          <>
            {/* Desktop Table View */}
            <div className="hidden md:block overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left text-white font-medium py-3 px-2">Date & Time</th>
                    <th className="text-left text-white font-medium py-3 px-2">Type</th>
                    <th className="text-left text-white font-medium py-3 px-2">Description</th>
                    <th className="text-right text-white font-medium py-3 px-2">Amount</th>
                    <th className="text-center text-white font-medium py-3 px-2">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {currentTransactions.map((transaction) => (
                    <tr key={transaction.id} className="border-b border-white/10 hover:bg-white/5">
                      <td className="py-4 px-2">
                        <div className="text-white text-sm">
                          {formatDisplayDate(transaction.date)}
                        </div>
                        <div className="text-white/60 text-xs">
                          {formatDisplayTime(transaction.date)}
                        </div>
                      </td>
                      <td className="py-4 px-2">
                        <div className="flex items-center">
                          <i className={`${getTransactionIcon(transaction.type)} mr-2`}></i>
                          <span className="text-white text-sm">
                            {getTransactionTypeLabel(transaction.type)}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-2">
                        <p className="text-white text-sm">{transaction.description}</p>
                      </td>
                      <td className="py-4 px-2 text-right">
                        <p className={`font-bold text-sm ${
                          transaction.amount > 0 ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                        </p>
                      </td>
                      <td className="py-4 px-2 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          transaction.status === 'completed' ? 'bg-green-400/20 text-green-400' :
                          transaction.status === 'pending' ? 'bg-yellow-400/20 text-yellow-400' :
                          transaction.status === 'failed' ? 'bg-red-400/20 text-red-400' :
                          'bg-gray-400/20 text-gray-400'
                        }`}>
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-3">
              {currentTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="p-4 bg-white/10 rounded-lg"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <i className={`${getTransactionIcon(transaction.type)} mr-2`}></i>
                      <span className="text-white font-medium text-sm">
                        {getTransactionTypeLabel(transaction.type)}
                      </span>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      transaction.status === 'completed' ? 'bg-green-400/20 text-green-400' :
                      transaction.status === 'pending' ? 'bg-yellow-400/20 text-yellow-400' :
                      transaction.status === 'failed' ? 'bg-red-400/20 text-red-400' :
                      'bg-gray-400/20 text-gray-400'
                    }`}>
                      {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                    </span>
                  </div>

                  <p className="text-white text-sm mb-2">{transaction.description}</p>

                  <div className="flex items-center justify-between">
                    <div className="text-white/60 text-xs">
                      {formatDisplayDate(transaction.date)} {formatDisplayTime(transaction.date)}
                    </div>
                    <p className={`font-bold text-sm ${
                      transaction.amount > 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center mt-6 gap-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="glass-button px-3 py-2 text-white disabled:opacity-50"
                >
                  <i className="fas fa-chevron-left"></i>
                </button>

                <div className="flex gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum
                    if (totalPages <= 5) {
                      pageNum = i + 1
                    } else if (currentPage <= 3) {
                      pageNum = i + 1
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i
                    } else {
                      pageNum = currentPage - 2 + i
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-2 text-sm rounded ${
                          currentPage === pageNum
                            ? 'bg-blue-500 text-white'
                            : 'glass-button text-white'
                        }`}
                      >
                        {pageNum}
                      </button>
                    )
                  })}
                </div>

                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="glass-button px-3 py-2 text-white disabled:opacity-50"
                >
                  <i className="fas fa-chevron-right"></i>
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
