"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6681],{12:(e,t,o)=>{o.d(t,{CQ:()=>f,Dl:()=>i,G9:()=>d,M4:()=>h,Mb:()=>_,_f:()=>g,g4:()=>s,nS:()=>u});var r=o(6104),a=o(4752),c=o.n(a);function s(e){try{let t=new Date().toDateString(),o="video_session_".concat(e,"_").concat(t),r="watch_times_".concat(e,"_").concat(t),a="daily_watch_times_".concat(e,"_").concat(t),c=localStorage.getItem("backup_timestamp_".concat(e));if(!c)return!1;if(new Date(parseInt(c)).toDateString()!==t)return n(e),!1;let s=localStorage.getItem("backup_".concat(o)),l=localStorage.getItem("backup_".concat(r)),i=localStorage.getItem("backup_".concat(a)),u=!1;if(s&&(localStorage.setItem(o,s),u=!0),l&&(localStorage.setItem(r,l),u=!0),i&&(localStorage.setItem(a,i),u=!0),u)return console.log("Session data restored for user:",e,{sessionCount:s,watchTimesCount:l?JSON.parse(l).length:0,dailyWatchTimesCount:i?JSON.parse(i).length:0}),n(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function n(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function l(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),o="video_session_".concat(e,"_").concat(t),r="watch_times_".concat(e,"_").concat(t),a="daily_watch_times_".concat(e,"_").concat(t),c=localStorage.getItem(o),s=localStorage.getItem(r),n=localStorage.getItem(a);c&&localStorage.setItem("backup_".concat(o),c),s&&localStorage.setItem("backup_".concat(r),s),n&&localStorage.setItem("backup_".concat(a),n),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:c,watchTimesCount:s?JSON.parse(s).length:0,dailyWatchTimesCount:n?JSON.parse(n).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),console.log("\uD83E\uDDF9 Starting comprehensive localStorage cleanup for user:",e);let o=Object.keys(localStorage),r=0;o.forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("daily_watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.startsWith("notification_")||t.startsWith("wallet_")||t.startsWith("transaction_")||t.startsWith("work_")||t.startsWith("session_")||t.includes("mytube_")||t.includes("user_")||t.includes("_uid_")||t.includes("firebase"))&&(localStorage.removeItem(t),r++,console.log("\uD83D\uDDD1️ Cleared key:",t))}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache","userPreferences","dashboardCache","profileCache","withdrawalCache","planCache","videoCache","lastActiveUser","currentSession","activeUserId"].forEach(e=>{localStorage.getItem(e)&&(localStorage.removeItem(e),r++,console.log("\uD83D\uDDD1️ Cleared common key:",e))}),console.log("✅ Local storage cleanup completed for user ".concat(e,": ").concat(r," keys cleared"),{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}function i(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if(console.log("\uD83E\uDDF9 Starting COMPLETE localStorage cleanup..."),e){let e=Object.keys(localStorage),t={};e.forEach(e=>{e.startsWith("backup_")&&(t[e]=localStorage.getItem(e)||"")}),localStorage.clear(),Object.entries(t).forEach(e=>{let[t,o]=e;localStorage.setItem(t,o)}),console.log("✅ Complete cleanup done, preserved ".concat(Object.keys(t).length," backup keys"))}else localStorage.clear(),console.log("✅ Complete localStorage cleared (nuclear option)")}catch(e){console.error("Error in complete localStorage cleanup:",e)}}function u(e){try{console.log("\uD83D\uDD12 Isolating session for user:",e),Object.keys(localStorage).forEach(t=>{(t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("daily_watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_"))&&!t.includes(e)&&(localStorage.removeItem(t),console.log("\uD83D\uDDD1️ Removed other user data:",t))}),localStorage.setItem("activeUserId",e),localStorage.setItem("sessionIsolatedAt",Date.now().toString()),console.log("✅ Session isolated for user:",e)}catch(e){console.error("Error isolating user session:",e)}}async function g(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await c().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&l(e,!1),await r.j2.signOut(),c().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),c().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function h(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&l(e,o),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function d(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let o=localStorage.getItem(e);o&&new Date(parseInt(o)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}function m(e){try{let t=localStorage.getItem("activeUserId");if(!t)return console.warn("⚠️ No active user ID found in localStorage"),!1;if(t!==e)return console.warn("⚠️ Session user mismatch:",{activeUserId:t,requestedUserId:e}),!1;return!0}catch(e){return console.error("Error validating user session:",e),!1}}function _(e,t){try{if(!m(t))return console.warn("⚠️ Blocked localStorage access due to session validation failure"),null;if(e.includes("_")&&!e.includes(t)&&(e.startsWith("video_")||e.startsWith("watch_")||e.startsWith("daily_")))return console.warn("⚠️ Blocked access to other user's data:",e),null;return localStorage.getItem(e)}catch(e){return console.error("Error in secure localStorage get:",e),null}}function f(e,t,o){try{if(!m(o))return console.warn("⚠️ Blocked localStorage write due to session validation failure"),!1;if(e.includes("_")&&!e.includes(o)&&(e.startsWith("video_")||e.startsWith("watch_")||e.startsWith("daily_")))return console.warn("⚠️ Blocked write to other user's data:",e),!1;return localStorage.setItem(e,t),!0}catch(e){return console.error("Error in secure localStorage set:",e),!1}}},6104:(e,t,o)=>{o.d(t,{Cn:()=>g,db:()=>u,j2:()=>i});var r=o(3915),a=o(3004),c=o(5317),s=o(858),n=o(2144);let l=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),i=(0,a.xI)(l),u=(0,c.aU)(l);(0,s.c7)(l);let g=(0,n.Uz)(l,"us-central1")},6681:(e,t,o)=>{o.d(t,{Nu:()=>l,hD:()=>n,wC:()=>i});var r=o(2115),a=o(3004),c=o(6104),s=o(12);function n(){let[e,t]=(0,r.useState)(null),[o,n]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,a.hg)(c.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),n(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),n(!1)}},[]);let l=async()=>{try{await (0,s.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:o,signOut:l}}function l(){let{user:e,loading:t}=n();return(0,r.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function i(){let{user:e,loading:t}=n(),[o,a]=(0,r.useState)(!1),[c,s]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");a(t),s(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||c,isAdmin:o}}}}]);