'use client'

import { useState } from 'react'
import { useRequireAdmin } from '@/hooks/useAuth'
import { getAllUsers } from '@/lib/adminDataService'
import { calculateUserActiveDays } from '@/lib/dataService'

export default function DebugActiveDaysPage() {
  const { isAdmin, loading } = useRequireAdmin()
  const [debugResults, setDebugResults] = useState<any[]>([])
  const [isProcessing, setIsProcessing] = useState(false)

  const runDebugCheck = async () => {
    try {
      setIsProcessing(true)
      setDebugResults([])

      console.log('🔍 Starting active days debug check...')
      
      // Get first 10 users for debugging
      const allUsers = await getAllUsers()
      const sampleUsers = allUsers.slice(0, 10)

      const results = []

      for (const user of sampleUsers) {
        try {
          // Get stored value from Firestore
          const storedActiveDays = (user as any).activeDays || 0
          
          // Get calculated value from centralized function
          const calculatedActiveDays = await calculateUserActiveDays(user.id)
          
          // Check if there's a discrepancy
          const discrepancy = storedActiveDays !== calculatedActiveDays

          results.push({
            userId: user.id,
            email: (user as any).email,
            plan: (user as any).plan,
            joinedDate: (user as any).joinedDate,
            planExpiry: (user as any).planExpiry,
            storedActiveDays,
            calculatedActiveDays,
            discrepancy,
            manuallySet: (user as any).manuallySetActiveDays || false,
            lastUpdate: (user as any).lastActiveDaysUpdate
          })

          console.log(`User ${(user as any).email}: Stored=${storedActiveDays}, Calculated=${calculatedActiveDays}, Discrepancy=${discrepancy}`)
        } catch (error) {
          console.error(`Error processing user ${user.id}:`, error)
          results.push({
            userId: user.id,
            email: (user as any).email,
            error: (error as any).message
          })
        }
      }

      setDebugResults(results)
      console.log('✅ Debug check completed')

    } catch (error) {
      console.error('Error in debug check:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Debug Active Days</h1>
            <p className="text-gray-600 mt-2">
              Compare stored Firestore values vs calculated values for active days
            </p>
          </div>

          <div className="p-6">
            <button
              onClick={runDebugCheck}
              disabled={isProcessing}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isProcessing ? 'Processing...' : 'Run Debug Check (First 10 Users)'}
            </button>

            {debugResults.length > 0 && (
              <div className="mt-8">
                <h2 className="text-lg font-semibold mb-4">Debug Results</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Plan
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Stored Active Days
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Calculated Active Days
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Discrepancy
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Manually Set
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {debugResults.map((result, index) => (
                        <tr key={index} className={result.discrepancy ? 'bg-red-50' : ''}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{result.email}</div>
                            <div className="text-sm text-gray-500">{result.userId}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.plan}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.storedActiveDays}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.calculatedActiveDays}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {result.discrepancy ? (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                YES
                              </span>
                            ) : (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                NO
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.manuallySet ? 'Yes' : 'No'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
