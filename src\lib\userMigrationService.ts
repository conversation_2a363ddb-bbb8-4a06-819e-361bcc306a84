import { createUserWithEmailAndPassword, fetchSignInMethodsForEmail } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp, collection, query, where, getDocs } from 'firebase/firestore'
import { auth, db } from './firebase'
import { FIELD_NAMES, COLLECTIONS, generateUniqueReferralCode } from './dataService'

export interface UserUploadData {
  name: string
  email: string
  mobile: string
  password: string
  plan?: string
  activeDays?: number
  wallet?: number
  totalVideos?: number
  referredBy?: string
  referralCode?: string
  joinedDate?: string
  quickVideoAdvantage?: boolean
  quickVideoAdvantageDays?: number
  quickVideoAdvantageSeconds?: number
  quickVideoAdvantageGrantedBy?: string
}

export interface UploadResult {
  success: number
  failed: number
  errors: string[]
  duplicates: number
}

// Validate user data
export function validateUserData(userData: UserUploadData): string[] {
  const errors: string[] = []

  // Required fields
  if (!userData.name || userData.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters')
  }

  if (!userData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
    errors.push('Valid email address is required')
  }

  if (!userData.mobile || !/^[6-9]\d{9}$/.test(userData.mobile)) {
    errors.push('Valid 10-digit mobile number is required')
  }

  if (!userData.password || userData.password.length < 6) {
    errors.push('Password is required and must be at least 6 characters')
  }

  // Optional field validation
  if (userData.plan && !['Trial', 'Starter', 'Basic', 'Premium', 'Gold', 'Platinum', 'Diamond'].includes(userData.plan)) {
    errors.push('Plan must be one of: Trial, Starter, Basic, Premium, Gold, Platinum, Diamond')
  }

  if (userData.activeDays && (userData.activeDays < 0 || userData.activeDays > 365)) {
    errors.push('Active days must be between 0 and 365')
  }

  if (userData.wallet && userData.wallet < 0) {
    errors.push('Wallet balance cannot be negative')
  }

  if (userData.totalVideos && userData.totalVideos < 0) {
    errors.push('Total videos cannot be negative')
  }

  // Referral code validation
  if (userData.referralCode) {
    // Check if referral code follows the expected format (MYN followed by numbers)
    const referralCodePattern = /^MYN\d+$/
    if (!referralCodePattern.test(userData.referralCode)) {
      errors.push('Referral code must follow format MYN0001, MYN0002, etc.')
    }
  }

  // Quick video advantage validation
  if (userData.quickVideoAdvantage !== undefined && typeof userData.quickVideoAdvantage !== 'boolean') {
    errors.push('Quick video advantage must be true or false')
  }

  if (userData.quickVideoAdvantageDays && (userData.quickVideoAdvantageDays < 1 || userData.quickVideoAdvantageDays > 365)) {
    errors.push('Quick video advantage days must be between 1 and 365')
  }

  if (userData.quickVideoAdvantageSeconds && (userData.quickVideoAdvantageSeconds < 1 || userData.quickVideoAdvantageSeconds > 420)) {
    errors.push('Quick video advantage seconds must be between 1 and 420 (7 minutes)')
  }

  // If quick advantage is true, days should be provided
  if (userData.quickVideoAdvantage === true && !userData.quickVideoAdvantageDays) {
    errors.push('Quick video advantage days must be provided when quick advantage is enabled')
  }

  // If quick advantage is true and seconds are provided, validate common durations
  if (userData.quickVideoAdvantage === true && userData.quickVideoAdvantageSeconds) {
    const validSeconds = [1, 10, 30, 60, 120, 180, 240, 300, 360, 420, 600] // 1s, 10s, 30s, 1-7 minutes, 10 minutes
    if (!validSeconds.includes(userData.quickVideoAdvantageSeconds)) {
      console.warn(`Quick video advantage seconds ${userData.quickVideoAdvantageSeconds} is not a common duration. Valid options: ${validSeconds.join(', ')}`)
    }
  }

  return errors
}

// Check if user already exists (with rate limiting)
async function checkUserExists(email: string, mobile: string): Promise<boolean> {
  try {
    // Check Firestore first (faster and no quota limits)
    const emailQuery = query(
      collection(db, COLLECTIONS.users),
      where(FIELD_NAMES.email, '==', email)
    )
    const emailSnapshot = await getDocs(emailQuery)

    if (!emailSnapshot.empty) {
      console.log(`Email ${email} already exists in Firestore`)
      return true
    }

    // Check Firestore by mobile
    const mobileQuery = query(
      collection(db, COLLECTIONS.users),
      where(FIELD_NAMES.mobile, '==', mobile)
    )
    const mobileSnapshot = await getDocs(mobileQuery)

    if (!mobileSnapshot.empty) {
      console.log(`Mobile ${mobile} already exists in Firestore`)
      return true
    }

    // Skip Firebase Auth check to avoid quota issues
    // Let the createUserWithEmailAndPassword handle auth duplicates
    return false
  } catch (error) {
    console.error('Error checking user existence:', error)
    // If we can't check, assume user doesn't exist and let the creation attempt handle duplicates
    return false
  }
}

// Create single user
async function createSingleUser(userData: UserUploadData): Promise<{ success: boolean; error?: string }> {
  try {
    // Validate data
    const validationErrors = validateUserData(userData)
    if (validationErrors.length > 0) {
      return { success: false, error: validationErrors.join(', ') }
    }

    // Check if user already exists
    const exists = await checkUserExists(userData.email, userData.mobile)
    if (exists) {
      return { success: false, error: 'User with this email or mobile already exists (duplicate)' }
    }

    // Create Firebase Auth account
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      userData.email,
      userData.password
    )

    const user = userCredential.user

    // Use provided referral code or generate a new sequential one
    let userReferralCode = userData.referralCode

    if (userReferralCode) {
      // Check if the provided referral code already exists
      const existingCodeQuery = query(
        collection(db, COLLECTIONS.users),
        where(FIELD_NAMES.referralCode, '==', userReferralCode)
      )
      const existingCodeSnapshot = await getDocs(existingCodeQuery)

      if (!existingCodeSnapshot.empty) {
        throw new Error(`Referral code ${userReferralCode} already exists. Please use a unique referral code.`)
      }
    } else {
      // Generate a new unique referral code
      userReferralCode = await generateUniqueReferralCode()
    }

    // Calculate quick video advantage expiry if applicable
    let quickAdvantageExpiry = null
    if (userData.quickVideoAdvantage && userData.quickVideoAdvantageDays) {
      const now = new Date()
      quickAdvantageExpiry = Timestamp.fromDate(new Date(now.getTime() + (userData.quickVideoAdvantageDays * 24 * 60 * 60 * 1000)))
    }

    // Prepare user document data
    const userDocData = {
      [FIELD_NAMES.name]: userData.name.trim(),
      [FIELD_NAMES.email]: userData.email.toLowerCase(),
      [FIELD_NAMES.mobile]: userData.mobile,
      [FIELD_NAMES.referralCode]: userReferralCode,
      [FIELD_NAMES.referredBy]: userData.referredBy || '',
      [FIELD_NAMES.referralBonusCredited]: false, // Bonus not credited yet
      [FIELD_NAMES.plan]: userData.plan || 'Trial',
      [FIELD_NAMES.planExpiry]: null,
      [FIELD_NAMES.activeDays]: userData.activeDays || 1,
      [FIELD_NAMES.joinedDate]: userData.joinedDate ? new Date(userData.joinedDate) : Timestamp.now(),
      [FIELD_NAMES.wallet]: userData.wallet || 0,
      [FIELD_NAMES.totalVideos]: userData.totalVideos || 0,
      [FIELD_NAMES.todayVideos]: 0,
      [FIELD_NAMES.lastVideoDate]: null,
      [FIELD_NAMES.videoDuration]: 300, // Default 5 minutes
      status: 'active',

      // Quick Video Advantage fields
      [FIELD_NAMES.quickVideoAdvantage]: userData.quickVideoAdvantage || false,
      [FIELD_NAMES.quickVideoAdvantageExpiry]: quickAdvantageExpiry,
      [FIELD_NAMES.quickVideoAdvantageDays]: userData.quickVideoAdvantageDays || 0,
      [FIELD_NAMES.quickVideoAdvantageSeconds]: userData.quickVideoAdvantageSeconds || 30, // Default to 30 seconds
      [FIELD_NAMES.quickVideoAdvantageGrantedBy]: userData.quickVideoAdvantageGrantedBy || '',
      [FIELD_NAMES.quickVideoAdvantageGrantedAt]: userData.quickVideoAdvantage ? Timestamp.now() : null
    }

    // Create user document in Firestore
    await setDoc(doc(db, COLLECTIONS.users, user.uid), userDocData)

    // Note: Referral bonus will be credited when admin upgrades user from Trial to paid plan
    // No immediate bonus processing during user creation

    return { success: true }
  } catch (error: any) {
    console.error('Error creating user:', error)
    
    let errorMessage = 'Unknown error occurred'
    
    if (error.code === 'auth/email-already-in-use') {
      errorMessage = 'Email address is already in use (duplicate)'
    } else if (error.code === 'auth/invalid-email') {
      errorMessage = 'Invalid email address'
    } else if (error.code === 'auth/weak-password') {
      errorMessage = 'Password is too weak'
    } else if (error.message) {
      errorMessage = error.message
    }

    return { success: false, error: errorMessage }
  }
}

// Upload users from CSV
export async function uploadUsersFromCSV(file: File): Promise<UploadResult> {
  const result: UploadResult = {
    success: 0,
    failed: 0,
    errors: [],
    duplicates: 0
  }

  try {
    const text = await file.text()
    const lines = text.split('\n').filter(line => line.trim())
    
    if (lines.length < 2) {
      throw new Error('CSV file must have at least a header row and one data row')
    }

    // Detect delimiter (comma or tab)
    const firstLine = lines[0]
    const delimiter = firstLine.includes('\t') ? '\t' : ','

    const headers = firstLine.split(delimiter).map(h => h.trim().replace(/"/g, ''))
    const userDataArray: UserUploadData[] = []

    // Parse CSV/TSV data
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(delimiter).map(v => v.trim().replace(/"/g, ''))
      const userData: any = {}
      
      headers.forEach((header, index) => {
        userData[header] = values[index] || ''
      })

      // Convert numeric fields
      if (userData.activeDays) userData.activeDays = parseInt(userData.activeDays) || 0
      if (userData.wallet) userData.wallet = parseFloat(userData.wallet) || 0
      if (userData.totalVideos) userData.totalVideos = parseInt(userData.totalVideos) || 0

      // Convert boolean and numeric fields for quick video advantage
      if (userData.quickVideoAdvantage) {
        userData.quickVideoAdvantage = userData.quickVideoAdvantage.toLowerCase() === 'true'
      }
      if (userData.quickVideoAdvantageDays) {
        userData.quickVideoAdvantageDays = parseInt(userData.quickVideoAdvantageDays) || 0
      }
      if (userData.quickVideoAdvantageSeconds) {
        userData.quickVideoAdvantageSeconds = parseInt(userData.quickVideoAdvantageSeconds) || 30
      }

      userDataArray.push(userData as UserUploadData)
    }

    // Process each user
    for (let i = 0; i < userDataArray.length; i++) {
      const userData = userDataArray[i]

      // Update progress
      const progress = `Processing user ${i + 1} of ${userDataArray.length}: ${userData.name || userData.email}`
      console.log(progress)

      // Update progress in UI if available
      if (typeof window !== 'undefined' && (window as any).updateUploadProgress) {
        (window as any).updateUploadProgress(progress)
      }

      try {
        const createResult = await createSingleUser(userData)

        if (createResult.success) {
          result.success++
          console.log(`✅ Created user: ${userData.email}`)
        } else {
          result.failed++
          if (createResult.error?.includes('already exists') || createResult.error?.includes('duplicate')) {
            result.duplicates++
            console.log(`⚠️ Skipped duplicate: ${userData.email}`)
          } else {
            console.log(`❌ Failed to create: ${userData.email} - ${createResult.error}`)
          }
          result.errors.push(`Row ${i + 2}: ${createResult.error}`)
        }
      } catch (error: any) {
        result.failed++
        console.log(`❌ Error creating: ${userData.email} - ${error.message}`)
        result.errors.push(`Row ${i + 2}: ${error.message || 'Unknown error'}`)
      }

      // Add delay to avoid rate limiting
      if (i % 5 === 0) {
        await new Promise(resolve => setTimeout(resolve, 500))
      } else {
        await new Promise(resolve => setTimeout(resolve, 200))
      }
    }

    return result
  } catch (error: any) {
    console.error('Error uploading users from CSV:', error)
    throw new Error(`Failed to process CSV file: ${error.message}`)
  }
}

// Upload users from JSON
export async function uploadUsersFromJSON(file: File): Promise<UploadResult> {
  const result: UploadResult = {
    success: 0,
    failed: 0,
    errors: [],
    duplicates: 0
  }

  try {
    const text = await file.text()
    const userDataArray: UserUploadData[] = JSON.parse(text)

    if (!Array.isArray(userDataArray)) {
      throw new Error('JSON file must contain an array of user objects')
    }

    // Process each user
    for (let i = 0; i < userDataArray.length; i++) {
      const userData = userDataArray[i]
      
      try {
        const createResult = await createSingleUser(userData)
        
        if (createResult.success) {
          result.success++
        } else {
          result.failed++
          if (createResult.error?.includes('already exists') || createResult.error?.includes('duplicate')) {
            result.duplicates++
          }
          result.errors.push(`User ${i + 1} (${userData.email}): ${createResult.error}`)
        }
      } catch (error: any) {
        result.failed++
        result.errors.push(`User ${i + 1} (${userData.email}): ${error.message || 'Unknown error'}`)
      }

      // Add delay to avoid rate limiting
      if (i % 5 === 0) {
        await new Promise(resolve => setTimeout(resolve, 500))
      } else {
        await new Promise(resolve => setTimeout(resolve, 200))
      }
    }

    return result
  } catch (error: any) {
    console.error('Error uploading users from JSON:', error)
    throw new Error(`Failed to process JSON file: ${error.message}`)
  }
}
