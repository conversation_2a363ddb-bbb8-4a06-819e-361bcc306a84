(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[784],{4371:(e,t,a)=>{Promise.resolve().then(a.bind(a,5746))},5746:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var c=a(5155),s=a(2115),n=a(3004),r=a(5317),i=a(6104),o=a(3592);function l(){let[e,t]=(0,s.useState)(""),[a,l]=(0,s.useState)(!1),u=e=>{t(t=>t+e+"\n")},d=async()=>{u("=== STEP 1: Testing Collection Access ===");try{let e=(0,r.collection)(i.db,o.COLLECTIONS.users);u("✅ Collection reference created");let t=(0,r.P)(e,(0,r._M)(o.FIELD_NAMES.email,"==","<EMAIL>"));u("✅ Query created");let a=await (0,r.getDocs)(t);u("✅ Query executed, found ".concat(a.size," documents"))}catch(e){u("❌ Collection access failed: ".concat(e.message)),u("❌ Error code: ".concat(e.code))}},m=async()=>{u("\n=== STEP 2: Testing Count Operation ===");try{let e=(0,r.collection)(i.db,o.COLLECTIONS.users),t=(await (0,r.d_)(e)).data().count;u("✅ Count operation successful: ".concat(t," users"))}catch(e){u("❌ Count operation failed: ".concat(e.message)),u("❌ Error code: ".concat(e.code))}},E=async()=>{u("\n=== STEP 3: Testing Auth User Creation ===");try{var e;let t="test".concat(Date.now(),"@example.com");u("Creating auth user with email: ".concat(t));let a=(await (0,n.eJ)(i.j2,t,"test123456")).user;u("✅ Auth user created: ".concat(a.uid)),u("   Email: ".concat(a.email)),u("   Email verified: ".concat(a.emailVerified)),u("   Auth token available: ".concat(!!i.j2.currentUser)),await new Promise(e=>setTimeout(e,1e3)),u("\n=== STEP 4: Testing Document Creation ===");let c=Date.now().toString().slice(-4),s=Math.random().toString(36).substring(2,4).toUpperCase(),l="MY".concat(c).concat(s);u("Generated referral code: ".concat(l));let d={[o.FIELD_NAMES.name]:"Test User",[o.FIELD_NAMES.email]:t,[o.FIELD_NAMES.mobile]:"9876543210",[o.FIELD_NAMES.referralCode]:l,[o.FIELD_NAMES.referredBy]:"",[o.FIELD_NAMES.referralBonusCredited]:!1,[o.FIELD_NAMES.plan]:"Trial",[o.FIELD_NAMES.planExpiry]:null,[o.FIELD_NAMES.activeDays]:1,[o.FIELD_NAMES.joinedDate]:new Date,[o.FIELD_NAMES.wallet]:0,[o.FIELD_NAMES.totalVideos]:0,[o.FIELD_NAMES.todayVideos]:0,[o.FIELD_NAMES.lastVideoDate]:null,[o.FIELD_NAMES.videoDuration]:30,status:"active"};u("Document path: ".concat(o.COLLECTIONS.users,"/").concat(a.uid)),u("Current auth user: ".concat(null==(e=i.j2.currentUser)?void 0:e.uid)),u("Auth state: ".concat(i.j2.currentUser?"authenticated":"not authenticated"));let m=(0,r.H9)(i.db,o.COLLECTIONS.users,a.uid);try{u("Attempting setDoc..."),await (0,r.BN)(m,d),u("✅ User document created successfully"),(await (0,r.x7)(m)).exists()?u("✅ Document verification successful"):u("❌ Document verification failed")}catch(e){u("❌ setDoc failed: ".concat(e.message)),u("❌ setDoc error code: ".concat(e.code)),u("❌ Full setDoc error: ".concat(JSON.stringify(e,null,2)))}try{await a.delete(),u("✅ Test user deleted")}catch(e){u("⚠️ User deletion failed: ".concat(e.message))}}catch(e){u("❌ Auth/Document creation failed: ".concat(e.message)),u("❌ Error code: ".concat(e.code)),u("❌ Full error: ".concat(JSON.stringify(e,null,2)))}},D=async()=>{l(!0),t("");try{await d(),await m(),await E(),u("\n=== ALL TESTS COMPLETED ===")}catch(e){u("\n❌ Test suite failed: ".concat(e.message))}finally{l(!1)}};return(0,c.jsx)("div",{className:"min-h-screen p-4",children:(0,c.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,c.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Debug Registration"}),(0,c.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,c.jsx)("button",{onClick:D,disabled:a,className:"btn-primary mb-4",children:a?"Running Tests...":"Run Registration Debug Tests"}),(0,c.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,c.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap",children:e||'Click "Run Registration Debug Tests" to start...'})})]})]})})}},6104:(e,t,a)=>{"use strict";a.d(t,{Cn:()=>d,db:()=>u,j2:()=>l});var c=a(3915),s=a(3004),n=a(5317),r=a(858),i=a(2144);let o=(0,c.Dk)().length?(0,c.Sx)():(0,c.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,s.xI)(o),u=(0,n.aU)(o);(0,r.c7)(o);let d=(0,i.Uz)(o,"us-central1")}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8818,3592,8441,1684,7358],()=>t(4371)),_N_E=e.O()}]);