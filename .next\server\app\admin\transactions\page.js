(()=>{var e={};e.id=4426,e.ids=[1391,3772,4426],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8574:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\transactions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\transactions\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15256:(e,t,r)=>{"use strict";function a(e){if(e instanceof Date)return e;if(e&&"object"==typeof e&&"function"==typeof e.toDate)return e.toDate();if(e&&("string"==typeof e||"number"==typeof e)){let t=new Date(e);if(!isNaN(t.getTime()))return t}return console.warn("Invalid date value provided to safeToDate:",e),new Date}function s(e){return a(e).toLocaleDateString()}function i(e){return a(e).toLocaleTimeString()}r.d(t,{NI:()=>i,g1:()=>s,xi:()=>a})},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22245:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["transactions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8574)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\transactions\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\transactions\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/transactions/page",pathname:"/admin/transactions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41772:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(60687),s=r(43210),i=r(85814),n=r.n(i),o=r(87979),l=r(91391),d=r(83475),c=r(15256),u=r(77567);function p(){let{user:e,loading:t,isAdmin:i}=(0,o.wC)(),[p,x]=(0,s.useState)([]),[m,g]=(0,s.useState)(!0),[h,y]=(0,s.useState)(""),[f,D]=(0,s.useState)(""),[w,b]=(0,s.useState)(""),[N,S]=(0,s.useState)(null),[E,v]=(0,s.useState)(!0),L=async(e=!0)=>{try{g(!0);let t=await (0,l.I0)(20,e?null:N),a=[];for(let e of t.transactions){let t="Unknown User",s="<EMAIL>",i="N/A";try{let{getUserData:a}=await Promise.resolve().then(r.bind(r,3582)),n=await a(e.userId);n&&(t=n.name,s=n.email,i=n.mobile||"N/A")}catch(t){console.error(`Error loading user data for transaction ${e.id}:`,t)}a.push({id:e.id,userId:e.userId,userName:t,userEmail:s,userMobile:i,type:e.type,amount:e.amount,description:e.description,date:(0,c.xi)(e.date),status:e.status||"completed"})}e?x(a):x(e=>[...e,...a]),S(t.lastDoc),v(t.hasMore)}catch(e){console.error("Error loading transactions:",e),u.A.fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{g(!1)}},j=p.filter(e=>{let t=!h||e.type===h,r=!f||e.status===f,a=!w||String(e.userName||"").toLowerCase().includes(w.toLowerCase())||String(e.userEmail||"").toLowerCase().includes(w.toLowerCase())||String(e.userMobile||"").toLowerCase().includes(w.toLowerCase())||String(e.description||"").toLowerCase().includes(w.toLowerCase());return t&&r&&a}),C=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),A=e=>{switch(e){case"video_earning":return"Video Earning";case"withdrawal":return"Withdrawal";case"bonus":return"Bonus";case"referral":return"Referral";default:return e.charAt(0).toUpperCase()+e.slice(1)}},M=e=>{switch(e){case"video_earning":return"fas fa-play-circle text-green-500";case"withdrawal":return"fas fa-download text-red-500";case"bonus":return"fas fa-gift text-yellow-500";case"referral":return"fas fa-users text-blue-500";default:return"fas fa-exchange-alt text-gray-500"}};return t||m?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading transactions..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(n(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Transactions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-gray-700",children:["Total: ",j.length]}),(0,a.jsxs)("button",{onClick:()=>{if(0===j.length)return void u.A.fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=(0,d.sL)(j);(0,d.Bf)(e,"transactions"),u.A.fire({icon:"success",title:"Export Complete",text:`Exported ${j.length} transactions to CSV file.`,timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,a.jsxs)("button",{onClick:()=>L(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,a.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,a.jsx)("input",{type:"text",value:w,onChange:e=>b(e.target.value),placeholder:"Search user, email, mobile, or description...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,a.jsxs)("select",{value:h,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Types"}),(0,a.jsx)("option",{value:"video_earning",children:"Video Earning"}),(0,a.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,a.jsx)("option",{value:"bonus",children:"Bonus"}),(0,a.jsx)("option",{value:"referral",children:"Referral"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:f,onChange:e=>D(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{onClick:()=>{y(""),D(""),b("")},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"Clear Filters"})})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:j.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.userMobile})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:`${M(e.type)} mr-2`}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:A(e.type)})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.description})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("span",{className:`text-sm font-medium ${e.amount>0?"text-green-600":"text-red-600"}`,children:[e.amount>0?"+":"",C(e.amount)]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[(0,c.g1)(e.date)," ",(0,c.NI)(e.date)]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"failed"===e.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id))})]})}),E&&(0,a.jsx)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 text-center",children:(0,a.jsx)("button",{onClick:()=>{E&&!m&&L(!1)},disabled:m,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Loading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),"Load More Transactions"]})})}),(0,a.jsxs)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 text-center text-sm text-gray-600",children:["Showing ",p.length," transactions",!E&&p.length>0&&" (All loaded)"]})]})})]})}},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65221:(e,t,r)=>{Promise.resolve().then(r.bind(r,8574))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78373:(e,t,r)=>{Promise.resolve().then(r.bind(r,41772))},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,r)=>{"use strict";function a(e,t,r){if(!e||0===e.length)return void alert("No data to export");let a=r||Object.keys(e[0]),s=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],i=new Blob(["\uFEFF"+[a.join(","),...e.map(e=>a.map(t=>{let r=e[t];if(null==r)return"";let a=s.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof r){let e=r.replace(/"/g,'""');return`"${e}"`}return r instanceof Date?`"${r.toLocaleDateString()}"`:"object"==typeof r&&null!==r&&r.toDate?`"${r.toDate().toLocaleDateString()}"`:a&&("number"==typeof r||!isNaN(Number(r)))?`"${r}"`:"number"==typeof r?r.toString():`"${String(r)}"`}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let e=URL.createObjectURL(i);n.setAttribute("href",e),n.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)}}function s(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":String(e.bankDetails?.accountNumber||""),"IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}))}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}r.d(t,{Bf:()=>a,Fz:()=>s,Pe:()=>o,dB:()=>n,sL:()=>i})},91391:(e,t,r)=>{"use strict";r.d(t,{CF:()=>c,I0:()=>p,TK:()=>g,getAdminDashboardStats:()=>o,getAllPendingWithdrawals:()=>x,getAllWithdrawals:()=>m,hG:()=>h,lo:()=>l,nQ:()=>u,updateWithdrawalStatus:()=>y,x5:()=>d});var a=r(75535),s=r(33784),i=r(3582);let n=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=n.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let r=a.Dc.fromDate(t),o=await (0,a.getDocs)((0,a.collection)(s.db,i.COLLECTIONS.users)),l=o.size,d=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.users),(0,a._M)(i.FIELD_NAMES.joinedDate,">=",r)),c=(await (0,a.getDocs)(d)).size,u=0,p=0,x=0,m=0;o.forEach(e=>{let r=e.data();u+=r[i.FIELD_NAMES.totalVideos]||0,p+=r[i.FIELD_NAMES.wallet]||0;let a=r[i.FIELD_NAMES.lastVideoDate]?.toDate();a&&a.toDateString()===t.toDateString()&&(x+=r[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.transactions),(0,a._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,a.AB)(1e3));(await (0,a.getDocs)(e)).forEach(e=>{let r=e.data(),a=r[i.FIELD_NAMES.date]?.toDate();a&&a>=t&&(m+=r[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let g=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending")),h=(await (0,a.getDocs)(g)).size,y=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.withdrawals),(0,a._M)("date",">=",r)),f=(await (0,a.getDocs)(y)).size,D={totalUsers:l,totalVideos:u,totalEarnings:p,pendingWithdrawals:h,todayUsers:c,todayVideos:x,todayEarnings:m,todayWithdrawals:f};return n.set(e,{data:D,timestamp:Date.now()}),D}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(e=50,t=null){try{let r=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.AB)(e));t&&(r=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let n=await (0,a.getDocs)(r);return{users:n.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),r=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(r)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})).filter(e=>{let r=String(e[i.FIELD_NAMES.name]||"").toLowerCase(),a=String(e[i.FIELD_NAMES.email]||"").toLowerCase(),s=String(e[i.FIELD_NAMES.mobile]||"").toLowerCase(),n=String(e[i.FIELD_NAMES.referralCode]||"").toLowerCase();return r.includes(t)||a.includes(t)||s.includes(t)||n.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.users));return(await (0,a.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function p(e=50,t=null){try{let r=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.AB)(e));t&&(r=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let n=await (0,a.getDocs)(r);return{transactions:n.docs.map(e=>({id:e.id,...e.data(),date:e.data()[i.FIELD_NAMES.date]?.toDate()})),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function x(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending"),(0,a.My)("date","desc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} pending withdrawals`),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function m(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,a.P)((0,a.collection)(s.db,i.COLLECTIONS.withdrawals),(0,a.My)("date","desc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${t.length} total withdrawals`),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function g(e,t){try{await (0,a.mZ)((0,a.H9)(s.db,i.COLLECTIONS.users,e),t),n.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function h(e){try{await (0,a.kd)((0,a.H9)(s.db,i.COLLECTIONS.users,e)),n.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function y(e,t,o){try{let l=await (0,a.x7)((0,a.H9)(s.db,i.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=l.data(),p={status:t,updatedAt:a.Dc.now()};if(o&&(p.adminNotes=o),await (0,a.mZ)((0,a.H9)(s.db,i.COLLECTIONS.withdrawals,e),p),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(r.bind(r,3582));await e(d,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(r.bind(r,3582));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}n.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[6204,6958,7567,8441,3582,7979],()=>r(22245));module.exports=a})();