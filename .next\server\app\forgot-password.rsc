1:"$Sreact.fragment"
2:I[5080,["7177","static/chunks/app/layout-aa743498fb4dc734.js"],"default"]
3:I[7555,[],""]
4:I[1901,["6874","static/chunks/6874-e64f23fe5c883dd1.js","3063","static/chunks/3063-65b606d3273e4fb8.js","8039","static/chunks/app/error-949b4a1c6a891a5d.js"],"default"]
5:I[1295,[],""]
6:I[3063,["6874","static/chunks/6874-e64f23fe5c883dd1.js","3063","static/chunks/3063-65b606d3273e4fb8.js","4345","static/chunks/app/not-found-bc28e8498609ab0a.js"],"Image"]
7:I[6874,["6874","static/chunks/6874-e64f23fe5c883dd1.js","3063","static/chunks/3063-65b606d3273e4fb8.js","4345","static/chunks/app/not-found-bc28e8498609ab0a.js"],""]
8:I[1340,["7177","static/chunks/app/layout-aa743498fb4dc734.js"],"default"]
9:I[894,[],"ClientPageRoot"]
a:I[9028,["2992","static/chunks/bc9e92e6-4c8647483afc3464.js","7416","static/chunks/69806262-3d08c9dfa16d5687.js","8320","static/chunks/41ade5dc-9baccd68aadeeb3e.js","8818","static/chunks/8818-990e6495fbb6ba1e.js","6874","static/chunks/6874-e64f23fe5c883dd1.js","3063","static/chunks/3063-65b606d3273e4fb8.js","6681","static/chunks/6681-60add03f6402d63d.js","2162","static/chunks/app/forgot-password/page-ae387e36bfb186b6.js"],"default"]
d:I[9665,[],"MetadataBoundary"]
f:I[9665,[],"OutletBoundary"]
12:I[4911,[],"AsyncMetadataOutlet"]
14:I[9665,[],"ViewportBoundary"]
16:I[6614,[],""]
:HL["/_next/static/css/eb1ce7a6a6435070.css","style"]
0:{"P":null,"b":"NFL2O66IrIe2ezHIbtuvU","p":"","c":["","forgot-password",""],"i":false,"f":[[["",{"children":["forgot-password",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/eb1ce7a6a6435070.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"__variable_51684b","children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":""}],["$","link",null,{"rel":"stylesheet","href":"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"}],["$","script",null,{"src":"https://cdn.jsdelivr.net/npm/sweetalert2@11","async":true}]]}],["$","body",null,{"className":"__className_51684b antialiased","children":[["$","div",null,{"className":"animated-bg"}],["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$4","errorStyles":[],"errorScripts":[],"template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen flex items-center justify-center px-4","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"mb-8","children":[["$","$L6",null,{"src":"/img/mytube-logo.svg","alt":"MyTube Logo","width":80,"height":80,"className":"mx-auto mb-4"}],["$","h1",null,{"className":"text-6xl font-bold text-white mb-4","children":"404"}],["$","h2",null,{"className":"text-2xl font-semibold text-white mb-2","children":"Page Not Found"}],["$","p",null,{"className":"text-white/80 mb-8 max-w-md mx-auto","children":"The page you're looking for doesn't exist or has been moved."}],["$","div",null,{"className":"mb-8","children":[["$","p",null,{"className":"text-white/60 mb-4","children":"Need help finding what you're looking for?"}],["$","div",null,{"className":"flex justify-center","children":["$","a",null,{"href":"mailto:<EMAIL>","className":"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors","children":[["$","i",null,{"className":"fas fa-envelope mr-2"}],"Email Support"]}]}]]}]]}],["$","div",null,{"className":"space-y-4","children":[["$","$L7",null,{"href":"/","className":"btn-primary inline-flex items-center","children":[["$","i",null,{"className":"fas fa-home mr-2"}],"Go Home"]}],["$","div",null,{"className":"flex flex-col sm:flex-row gap-4 justify-center","children":[["$","$L7",null,{"href":"/dashboard","className":"btn-secondary inline-flex items-center","children":[["$","i",null,{"className":"fas fa-tachometer-alt mr-2"}],"Dashboard"]}],["$","$L7",null,{"href":"/work","className":"btn-secondary inline-flex items-center","children":[["$","i",null,{"className":"fas fa-play-circle mr-2"}],"Watch Videos"]}]]}]]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L8",null,{}]]}]]}]]}],{"children":["forgot-password",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L9",null,{"Component":"$a","searchParams":{},"params":{},"promises":["$@b","$@c"]}],["$","$Ld",null,{"children":"$Le"}],null,["$","$Lf",null,{"children":["$L10","$L11",["$","$L12",null,{"promise":"$@13"}]]}]]}],{},null,false]},null,false]},[["$","div","l",{"className":"min-h-screen flex items-center justify-center","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"spinner mb-4"}],["$","p",null,{"className":"text-white/80","children":"Loading MyTube..."}]]}]}],[],[]],false],["$","$1","h",{"children":[null,["$","$1","8Ol_EOJiLoOaii1usQlHw",{"children":[["$","$L14",null,{"children":"$L15"}],null]}],null]}],false]],"m":"$undefined","G":["$16","$undefined"],"s":false,"S":true}
17:"$Sreact.suspense"
18:I[4911,[],"AsyncMetadata"]
b:{}
c:{}
e:["$","$17",null,{"fallback":null,"children":["$","$L18",null,{"promise":"$@19"}]}]
11:null
15:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}],["$","meta","2",{"name":"theme-color","content":"#FF0000"}]]
10:null
19:{"metadata":[["$","title","0",{"children":"MyTube - Watch Videos & Earn"}],["$","meta","1",{"name":"description","content":"Watch videos and earn money. Complete daily video watching tasks to earn rewards."}],["$","meta","2",{"name":"author","content":"MyTube Team"}],["$","link","3",{"rel":"manifest","href":"/manifest.json","crossOrigin":"$undefined"}],["$","meta","4",{"name":"keywords","content":"video watching, earn money, online earning, video tasks, rewards"}],["$","link","5",{"rel":"icon","href":"/img/mytube-favicon.svg"}],["$","link","6",{"rel":"apple-touch-icon","href":"/img/mytube-favicon.svg"}]],"error":null,"digest":"$undefined"}
13:{"metadata":"$19:metadata","error":null,"digest":"$undefined"}
